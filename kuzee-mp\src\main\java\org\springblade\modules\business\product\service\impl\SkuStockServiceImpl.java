/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.product.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springblade.common.pojo.vo.IdNumVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.order.mapper.SaleOrderMapper;
import org.springblade.modules.business.product.mapper.SkuStockMapper;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.pojo.vo.PageAuditSkuVO;
import org.springblade.modules.business.product.pojo.vo.SkuStockVO;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品SKU表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
@AllArgsConstructor
public class SkuStockServiceImpl extends BaseServiceImpl<SkuStockMapper, SkuStockEntity> implements ISkuStockService {

	private final SaleOrderMapper saleOrderMapper;
	@Override
	public IPage<SkuStockVO> selectSkuStockPage(IPage<SkuStockVO> page, SkuStockVO skuStock) {
		return page.setRecords(baseMapper.selectSkuStockPage(page, skuStock));
	}



//	@Override
//	public List<SkuStockEntity> getIds(List<Long> skuIds) {
//		return baseMapper.getIds(skuIds);
//	}

	@Override
	public List<PageAuditSkuVO> pageAuditSku(IPage<PageAuditSkuVO> page, Long warehouseId, Integer selectType, String selectCode) {
		return switch (selectType) {
			case 0 ->// 查询已选择的sku
				baseMapper.pageAuditSkuSelect(page, warehouseId, selectCode);
			case 1 ->// 查询未选择的sku
				baseMapper.pageAuditSkuUnselect(page, warehouseId, selectCode);
			default -> throw new ServiceException("请输入正确的查询类型:" + selectType);
		};

	}

//	@Override
//	public List<SkuStockEntity> listBasisByIds(List<Long> productSkuIdList) {
//		if (productSkuIdList.isEmpty()) {
//			return List.of();
//		}
//		return list(Wrappers.<SkuStockEntity>lambdaQuery()
//			.select(SkuStockEntity::getId, SkuStockEntity::getSkuCode, SkuStockEntity::getSpData)
//			.in(SkuStockEntity::getId, productSkuIdList));
//	}


	@Override
	@Async
	public void updateSale(Long orderId) {
		if (Func.isEmpty(orderId)) {
			return;
		}
		List<IdNumVO> skuIds =saleOrderMapper.selectSkuIds(orderId);
 		if (Func.isEmpty(skuIds)) {
			return;
		}
		for (IdNumVO sku : skuIds) {
			if (Func.isNull(sku.getId())) {
				continue;
			}
			update(Wrappers.<SkuStockEntity>lambdaUpdate()
				.setSql("sale = sale + {0}", sku.getValue())
				.eq(SkuStockEntity::getId, sku.getId()));
		}
	}
}
