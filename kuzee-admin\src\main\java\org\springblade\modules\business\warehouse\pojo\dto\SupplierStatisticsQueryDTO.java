package org.springblade.modules.business.warehouse.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 供应商明细统计查询DTO
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@Schema(description = "供应商明细统计查询DTO")
public class SupplierStatisticsQueryDTO {

	@Schema(description = "仓库ID")
	private Long warehouseId;

	@Schema(description = "日期")
	private String dateTime;

	@Schema(description = "供应商ID")
	private Long supplierId;

	@Schema(description = "商品SKU ID")
	private Long productSkuId;
}
