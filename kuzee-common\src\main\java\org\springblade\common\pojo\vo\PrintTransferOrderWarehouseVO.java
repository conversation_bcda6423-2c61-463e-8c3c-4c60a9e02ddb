package org.springblade.common.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
/**
 * <AUTHOR>
 * @date 2022/11/23 10:05
 * @desc 仓库出库-档口
 */
public class PrintTransferOrderWarehouseVO {
	//品名
	@Schema(description = "品名")
	private String productName;
	// 单位
	@Schema(description = "单位")
	private String baseUnit;
	// 计量方式
	@Schema(description = "计量方式")
	private String measureMode;
	// 数量
	@Schema(description = "数量")
	private Integer quantity;
	// 出库数量
	@Schema(description = "出库数量")
	private Integer outboundQuantity;
	// 出库重量
	@Schema(description = "出库重量")
	private BigDecimal outboundWeight;
	// 收货重量
	@Schema(description = "收货重量")
	private BigDecimal storeWeight;

	// 备注
	@Schema(description = "备注")
	private String remark;
	// 商品种类
	@Schema(description = "商品种类")
	private String productType;

	@Schema(description = "档口")
	private String stall;
}
