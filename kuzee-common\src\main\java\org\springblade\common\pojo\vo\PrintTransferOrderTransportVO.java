package org.springblade.common.pojo.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
/**
 * <AUTHOR>
 * @date 2022/11/23 10:05
 * @desc 分配搬运
 */
public class PrintTransferOrderTransportVO {
	//供应商
	@Schema(description = "供应商")
	private String supplierName;
	// 详细地址
	@Schema(description = "详细地址")
	private String address;
	// 品名
	@Schema(description = "品名")
	private String productName;
	// 单位
	@Schema(description = "单位")
	private String baseUnit;
	// 计量方式
	@Schema(description = "计量方式")
	private String measureMode;
	// 合计
	@Schema(description = "合计")
	private Integer total;
	// 拉货数量
	@Schema(description = "拉货数量")
	private Integer pullQuantity;
	// 拉货重量
	@Schema(description = "拉货重量")
	private BigDecimal pullWeight;
	// 仓库收货
	@Schema(description = "仓库收货")
	private BigDecimal store;
	// 备注
	@Schema(description = "备注")
	private String remark;

	// 商品种类
	@Schema(description = "商品种类")
	private String productType;
}
