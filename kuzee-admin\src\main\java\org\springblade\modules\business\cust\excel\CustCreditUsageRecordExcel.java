/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.excel;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serial;


/**
 * 客户额度使用记录表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustCreditUsageRecordExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("用户ID")
	private Long custId;
	/**
	 * 使用额度金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("使用额度金额")
	private Integer usedAmount;
	/**
	 * 关联订单号（如订单模块的订单ID）
	 */
	@ColumnWidth(20)
	@ExcelProperty("关联订单号（如订单模块的订单ID）")
	private Long orderId;
	/**
	 * 使用时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("使用时间")
	private LocalDateTime usageTime;
	/**
	 * 使用描述（如“购买商品扣减额度”）
	 */
	@ColumnWidth(20)
	@ExcelProperty("使用描述（如“购买商品扣减额度”）")
	private String description;

}
