/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.StoreBizTypeEnum;
import org.springblade.common.utills.CommonUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.common.utills.JsonValueExtractor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.order.mapper.PurchaseOrderItemMapper;
import org.springblade.modules.business.order.service.IOrderAfterSalesServiceService;
import org.springblade.modules.business.order.service.IReplenishmentOrderService;
import org.springblade.modules.business.order.service.ISupplierService;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.warehouse.excel.WarehouseStoreExcel;
import org.springblade.modules.business.warehouse.mapper.WarehouseStoreMapper;
import org.springblade.modules.business.warehouse.pojo.dto.*;
import org.springblade.modules.business.warehouse.pojo.entity.*;
import org.springblade.modules.business.warehouse.pojo.vo.*;
import org.springblade.modules.business.warehouse.service.*;
import org.springblade.modules.business.warehouse.utils.WarehouseHelper;
import org.springblade.modules.business.warehouse.wrapper.WarehouseStoreItemWrapper;
import org.springblade.modules.business.warehouse.wrapper.WarehouseStoreWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 入库记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
@AllArgsConstructor
@Slf4j
public class WarehouseStoreServiceImpl extends BaseServiceImpl<WarehouseStoreMapper, WarehouseStoreEntity> implements IWarehouseStoreService {

	private final IWarehouseStoreItemService warehouseStoreItemService;
	private final IWarehouseSupportTransService warehouseSupportTransService;
	private final IWarehouseService warehouseService;
	private final ISupplierService supplierService;
	private final IProductService productService;
	private final IInventoryService inventoryService;
	private final IInventorySupportTransService inventorySupportTransService;
	private final ITransportOrderService transportOrderService;
	private final IWarehouseStoreOutboundRelationService warehouseStoreOutboundRelationService;
	private final IReplenishmentOrderService replenishmentOrderService;
	private final IOrderAfterSalesServiceService orderAfterSalesServiceService;
	private final ISkuStockService skuStockService;
	private final PurchaseOrderItemMapper purchaseOrderItemMapper;

	@Override
	public WarehouseStoreVO getDetail(Long storeBatchId) {
		WarehouseStoreEntity entity = getById(storeBatchId);
		if (Func.isNull(entity)) {
			return null;
		}

		WarehouseStoreVO vo = WarehouseStoreWrapper.build().entityVO(entity);
		if (StoreBizTypeEnum.isSupplierIn(vo.getBizType())) {
			Map<Long, String> map = supplierService.listNameByIds(Lists.newArrayList(vo.getRelatedSourceId()));
			vo.setRelatedSourceName(map.get(vo.getRelatedSourceId()));
		} else if (StoreBizTypeEnum.isOtherWarehouseIn(vo.getBizType())) {
			Map<Long, String> map = warehouseService.listNameByIds(Lists.newArrayList(vo.getRelatedSourceId()));
			vo.setRelatedSourceName(map.get(vo.getRelatedSourceId()));
		}
		vo.setBizTypeName(StoreBizTypeEnum.getNameByCode(vo.getBizType()));
		if (Func.notNull(vo.getCompletionTime())) {
			vo.setUpdateUserName(UserCache.getUserName(vo.getUpdateUser()));
		}
		return vo;
	}

	@Override
	public IPage<WarehouseStorePageVO> selectWarehouseStorePage(IPage<WarehouseStorePageVO> page, WarehouseStoreQueryDTO queryDTO) {
		IPage<WarehouseStorePageVO> resultPage = page.setRecords(baseMapper.selectWarehouseStorePage(page, queryDTO, StoreBizTypeEnum.getExcludeBizTypes()));

		if (resultPage.getTotal() < 1) {
			return resultPage;
		}

		// 关联仓库名称或者供应商名称
		List<WarehouseStorePageVO> records = resultPage.getRecords();
		convertWarehouseName(records);

		// 商品信息转换
//		productService.assembleProductVO(records);
		return resultPage;
	}

	/**
	 * 盘点差异入库
	 *
	 * @param relatedId 入库id
	 * @param skuId     商品sku id
	 * @return IPage<WarehouseStoreVO>
	 */
	@Override
	public WarehouseStorePdDiffListVO diffList(Long relatedId, Long skuId) {
		// 1. 查询所有符合条件的差异入库记录
		List<WarehouseStoreEntity> storeEntityList = list(Wrappers.<WarehouseStoreEntity>lambdaQuery()
			.eq(WarehouseStoreEntity::getRelatedOrderId, relatedId)
			.eq(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode())
			.eq(WarehouseStoreEntity::getStatus, BusinessConstant.ENABLE_STATUS));

		if (Func.isEmpty(storeEntityList)) {
			return null;
		}

		// 2. 批量获取所有相关数据，避免循环查询
		List<Long> storeIds = storeEntityList.stream().map(WarehouseStoreEntity::getId).collect(Collectors.toList());

		// 批量获取商品明细
		List<WarehouseStoreItemEntity> itemEntities = warehouseStoreItemService.list(
			Wrappers.<WarehouseStoreItemEntity>lambdaQuery().in(WarehouseStoreItemEntity::getWarehouseStoreId, storeIds)
		);
		List<WarehouseStoreItemVO> allItemsList = WarehouseStoreItemWrapper.build().listVO(itemEntities);
		productService.assembleProductVO(allItemsList);

		// 批量获取聚合后的配套运输品信息
		List<WarehouseSupportTransVO> supportTransDetails = warehouseSupportTransService.getAggregatedByStoreIds(storeIds);

		// 3. 将数据转换为Map以便高效查找
		Map<Long, WarehouseStoreEntity> storeEntityMap = storeEntityList.stream()
			.collect(Collectors.toMap(WarehouseStoreEntity::getId, Function.identity()));

		// 配套运输品信息Map: Map<StoreId, Map<SkuId, SupportTransVO>>
		Map<Long, Map<Long, WarehouseSupportTransVO>> supportTransMap = supportTransDetails.stream()
			.filter(vo -> vo.getProductSkuId() != null && vo.getRelatedRecordId() != null)
			.collect(Collectors.groupingBy(
				WarehouseSupportTransVO::getRelatedRecordId,
				Collectors.toMap(WarehouseSupportTransVO::getProductSkuId, Function.identity(), (a, b) -> a)
			));


		// 4. 遍历商品明细，为其填充父级信息和配套运输品信息
		for (WarehouseStoreItemVO itemVO : allItemsList) {
			// 填充父级入库单信息
			WarehouseStoreEntity parentStore = storeEntityMap.get(itemVO.getWarehouseStoreId());
			if (parentStore != null) {
				itemVO.setCompletionTime(parentStore.getCompletionTime());
				itemVO.setUpdateUser(parentStore.getUpdateUser());
				itemVO.setUpdateUserName(UserCache.getUserName(parentStore.getUpdateUser()));
			}

			// 填充配套运输品信息
			WarehouseSupportTransVO supportInfo = Optional.ofNullable(supportTransMap.get(itemVO.getWarehouseStoreId()))
				.map(skuMap -> skuMap.get(itemVO.getProductSkuId()))
				.orElse(null);

			if (supportInfo != null) {
				itemVO.setSupportTransNum(supportInfo.getSupportTransNum());
				itemVO.setSupportTransUnitName(supportInfo.getSupportTransUnitName());
			}
			if (Func.isNotEmpty(itemVO.getSupplierId())) {
				Long supplierId = itemVO.getSupplierId();
				Map<Long, String> supplierMap = supplierService.listNameByIds(Collections.singletonList(supplierId));
				if (supplierMap != null && supplierMap.containsKey(supplierId)) {
					itemVO.setSupplierName(supplierMap.get(supplierId));
				}
			}

		}

		// 5. 根据传入的skuId（如果存在）进行最终过滤
		List<WarehouseStoreItemVO> finalItemsList = allItemsList;
		if (Func.notNull(skuId)) {
			finalItemsList = allItemsList.stream()
				.filter(item -> skuId.equals(item.getProductSkuId()))
				.collect(Collectors.toList());
		}

		// 6. 组装并返回最终结果
		WarehouseStorePdDiffListVO vo = new WarehouseStorePdDiffListVO();
		WarehouseStoreEntity firstStore = storeEntityList.get(0);
		vo.setId(firstStore.getRelatedOrderId());
		vo.setUpdateUser(firstStore.getUpdateUser());
		vo.setUpdateUserName(UserCache.getUserName(firstStore.getUpdateUser()));
		vo.setCompletionTime(firstStore.getCompletionTime());
		vo.setWarehouseStoreItemVOList(finalItemsList);

		return vo;
	}

	/**
	 * 根据备货单号分页 自定义分页
	 *
	 * @param page    分页参数
	 * @param storeNo 备货单号
	 * @return IPage<WarehouseStoreVO>
	 */
	@Override
	public IPage<WarehouseScopeStoreNoPageVO> pageByStoreNo(IPage<WarehouseScopeStoreNoPageVO> page, String storeNo) {
		IPage<WarehouseScopeStoreNoPageVO> resultPage = baseMapper.selectByStoreNo(page, storeNo);
		if (!resultPage.getRecords().isEmpty()) {
			resultPage.getRecords().forEach(this::convertWarehouseScopeStoreNoPageVO);
		}
		return resultPage;
	}

	/**
	 * /**
	 * 转换 WarehouseScopeStoreNoPageVO 对象的字段
	 *
	 * @param vo WarehouseScopeStoreNoPageVO 对象
	 */
	private void convertWarehouseScopeStoreNoPageVO(WarehouseScopeStoreNoPageVO vo) {
		// 1. 转换用于前端展示的字段
		// 将数据库存储的 "分" 单位的价格转换为 "元"
		vo.setConverUnitPrice(CommonUtil.ConvertIntBigDecimal(vo.getUnitPrice()));
		vo.setConverWholePrice(CommonUtil.ConvertIntBigDecimal(vo.getWholePrice()));
		// 将数据库存储的 "克" 单位的重量转换为 "斤"
		vo.setConverActualWeight(CommonUtil.WeightIntBigDecimal(vo.getActualWeight()));

		// 2. 处理转运费相关字段
		if(Func.notNull(vo.getPriceType())) {
			// 将转运费单价和转运费小计从 "分" 转为 "元"
			BigDecimal feePriceYuan = CommonUtil.ConvertToBigDecimal(vo.getFeePrice());
			vo.setFeePrice(feePriceYuan);
			vo.setFeePriceStr(vo.getPriceType() == 0 ? "每件" + feePriceYuan + "元" : "每斤" + feePriceYuan + "元");
			vo.setFeeAmount(CommonUtil.ConvertToBigDecimal(vo.getFeeAmount()));
		} else {
			vo.setFeeAmount(null);
			vo.setFeePrice(null);
			vo.setFeePriceStr(null);
		}

		// 3. 初始化金额变量
		BigDecimal totalAmount = BigDecimal.ZERO;
		BigDecimal supportTransAmount = BigDecimal.ZERO;

		// 4. 计算商品小计金额 (优先使用重量计算)
		Integer unitPriceInt = vo.getUnitPrice();
		Integer actualWeightInt = vo.getActualWeight();
		// 检查按重量计算所需参数是否齐全且有效
		if (Func.notNull(unitPriceInt) && Func.notNull(actualWeightInt) && unitPriceInt > 0 && actualWeightInt > 0) {
			// 按单位重量(斤)计算: (实际重量g/500) * (每斤单价/100)
			BigDecimal weightInJin = CommonUtil.WeightIntBigDecimal(actualWeightInt);
			BigDecimal unitPriceYuan = CommonUtil.ConvertIntBigDecimal(unitPriceInt);
			if (weightInJin != null && unitPriceYuan != null) {
				totalAmount = weightInJin.multiply(unitPriceYuan);
			}
		} else {
			// 如果无法按重量计算，则回退到按整件价计算
			Integer wholePriceInt = vo.getWholePrice();
			if (Func.notNull(wholePriceInt) && wholePriceInt > 0) {
				// 按整件价计算: 数量 * (整件单价/100)
				BigDecimal wholePriceYuan = CommonUtil.ConvertIntBigDecimal(wholePriceInt);
				if (wholePriceYuan != null) {
					totalAmount = new BigDecimal(vo.getActualQuantity()).multiply(wholePriceYuan);
				}
			}
		}
		vo.setTotalAmount(totalAmount.setScale(2, RoundingMode.HALF_UP));

		// 5. 计算配套运输品小计金额
		BigDecimal supportTransPriceInCents = vo.getSupportTransPrice();
		BigDecimal supportTransPriceInYuan = CommonUtil.ConvertToBigDecimal(supportTransPriceInCents);
		// 将配套运输品单价转换为"元"并更新VO，用于前端展示
		vo.setSupportTransPrice(supportTransPriceInYuan);

		Integer supportTransNum = vo.getSupportTransNum();
		if (Func.notNull(supportTransPriceInYuan) && Func.notNull(supportTransNum) && supportTransNum > 0) {
			// 计算方式：数量 * 单价(元)
			supportTransAmount = supportTransPriceInYuan.multiply(new BigDecimal(supportTransNum));
		}
		vo.setSupportTransAmount(supportTransAmount.setScale(2, RoundingMode.HALF_UP));

		// 6. 计算总合计金额
		vo.setTotal(vo.getTotalAmount().add(vo.getSupportTransAmount()));
	}

	/**
	 * 仓库/供应商名称转换
	 */
	private void convertWarehouseName(List<WarehouseStorePageVO> records) {
		List<Long> warehouseIdList = Lists.newArrayList();
		List<Long> supplierIdList = Lists.newArrayList();
		records.forEach(s -> {
			warehouseIdList.add(s.getWarehouseId());
			if (Func.notNull(s.getRelatedSourceId())) {
				if (StoreBizTypeEnum.isSupplierIn(s.getBizType())) {
					supplierIdList.add(s.getRelatedSourceId());
				} else if (StoreBizTypeEnum.isOtherWarehouseIn(s.getBizType())) {
					warehouseIdList.add(s.getRelatedSourceId());
				}
			}
		});

		// 查询对应的名称(TODO 切换缓存查询？)
		Map<Long, String> warehouseMap = Maps.newHashMap();
		Map<Long, String> supplierIdMap = Maps.newHashMap();
		if (!warehouseIdList.isEmpty()) {
			warehouseMap.putAll(warehouseService.listNameByIds(warehouseIdList));
		}

		if (!supplierIdList.isEmpty()) {
			supplierIdMap.putAll(supplierService.listNameByIds(supplierIdList));
		}

		if (Func.isEmpty(warehouseMap) && Func.isEmpty(supplierIdMap)) {
			return;
		}

		for (WarehouseStorePageVO record : records) {
			record.setBizTypeName(StoreBizTypeEnum.getNameByCode(record.getBizType()));
			record.setWarehouseName(warehouseMap.get(record.getWarehouseId()));
			if (Func.notNull(record.getCompletionTime())) {
				record.setUpdateUserName(UserCache.getUserName(record.getUpdateUser()));
			}
			if (StoreBizTypeEnum.isSupplierIn(record.getBizType())) {
				record.setRelatedSourceName(supplierIdMap.get(record.getRelatedSourceId()));
			} else if (StoreBizTypeEnum.isOtherWarehouseIn(record.getBizType())) {
				record.setRelatedSourceName(warehouseMap.get(record.getRelatedSourceId()));
			}
		}
	}

	@Override
	public List<WarehouseStoreExcel> exportWarehouseStore(Wrapper<WarehouseStoreEntity> queryWrapper) {
		List<WarehouseStoreExcel> warehouseStoreList = baseMapper.exportWarehouseStore(queryWrapper);
		//warehouseStoreList.forEach(warehouseStore -> {
		//	warehouseStore.setTypeName(DictCache.getValue(DictEnum.YES_NO, WarehouseStoreEntity.getType()));
		//});
		return warehouseStoreList;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveOrUpdate(WarehouseStoreDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseStoreEntity entity = BeanUtil.copyProperties(dto, WarehouseStoreEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 是否需要入库
		boolean addInventory = Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS);
		entity.setCompletionTime(addInventory ? DateUtil.now() : null);
		// 生成入库单号
		if (Func.isNull(dto.getId())) {
			// 同一业务单只允许一条入库记录
			if (Func.notNull(dto.getRelatedOrderId())) {
				long count = count(Wrappers.<WarehouseStoreEntity>lambdaQuery()
					.eq(WarehouseStoreEntity::getBizType, dto.getBizType())
					.eq(WarehouseStoreEntity::getWarehouseId, dto.getWarehouseId())
					.notIn(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode(),  StoreBizTypeEnum.CHECK.getCode())
					.eq(WarehouseStoreEntity::getRelatedOrderId, dto.getRelatedOrderId()));
				Assert.isTrue(count < 1, () -> new ServiceException("该业务单已存在入库记录"));
			}
			entity.setStoreNo(GenerateNumberUtil.generate(StoreBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
			entity.setCreateTimeAt(System.currentTimeMillis());
			// 设置一个默认值
			if (Func.isNull(entity.getRelatedOrderId())) {
				entity.setRelatedOrderId(System.currentTimeMillis());
			}
			Assert.isTrue(save(entity), () -> new ServiceException("新增仓库入库记录失败"));
		} else {
			Assert.isTrue(updateById(entity), () -> new ServiceException("更新仓库入库记录失败"));
		}

		// 2.新增或更新仓库入库商品明细
		List<WarehouseStoreItemEntity> itemEntityList = Lists.newArrayList();
		if (Func.isNotEmpty(dto.getStoreItemList())) {
			// 更新或插入入库商品明细
			itemEntityList = dto.convertStoreItem(entity.getId(), entity.getStoreNo(), dto.getBizType());
			warehouseStoreItemService.saveOrUpdateBatch(itemEntityList);
			// 确认入库
			if (addInventory) {
				// 添加商品库存
				List<InventoryEntity> inventoryList = convertInventory(itemEntityList);
				// 转运入库是由订单创建后总仓出库，由订单来的都需要锁定库存
				if (StoreBizTypeEnum.isTransport(dto.getBizType())) {
					inventoryList.forEach(inventory -> inventory.setLockStock(inventory.getCurrentStock()));
				}
				Assert.isTrue(inventoryService.addInventory(inventoryList), () -> new ServiceException("更新商品库存失败"));
//				StoreBizTypeEnum bizType = StoreBizTypeEnum.getByCode(dto.getBizType());
//				// 退货入库需要更新指定批次库存，当前批次没有库存
//				//					case RETURN_IN -> itemEntityList.forEach(warehouseStoreItemService::updateByStoreNo);
//				// 供应商供货入库需要更新备货单状态并算出转运费
//				if (bizType == StoreBizTypeEnum.PURCHASE_IN) {
//					replenishmentOrderService.updateAndGenerateAmount(dto.getRelatedOrderId(), itemEntityList);
//				}
			}
		}

		// 3.新增或更新仓库配套运输库存
		List<WarehouseSupportTransEntity> supportList = null;
		if (Func.isNotEmpty(dto.getStoreSupportList())) {
			supportList = dto.convertSupportList(entity.getId(), BusinessConstant.WAREHOUSE_STORE);
			// 供应商供货入库和临时采购入库设置供应商
			if (StoreBizTypeEnum.isSupplierIn(dto.getBizType())) {
				supportList.forEach(s -> s.setSupplierId(dto.getRelatedSourceId()));
			}
			warehouseSupportTransService.saveOrUpdateBatch(supportList);
			if (addInventory) {
				Assert.isTrue(inventorySupportTransService.addInventory(supportList),
					() -> new ServiceException("更新商品配套运输品库存库存失败"));
				// 退货入库如果是配套运输品则需要退运输品费用
				if (Objects.equals(dto.getBizType(), StoreBizTypeEnum.RETURN_IN.getCode())) {
					orderAfterSalesServiceService.refundTrans(dto.getRelatedOrderId(),supportList);
				}
			}
		}

		// 4.转运入库需要更新转运单状态和时间
		if (addInventory && StoreBizTypeEnum.isTransport(dto.getBizType())) {
			transportOrderService.updateTransportOrderStore(dto.getRelatedOrderId(),  itemEntityList,  supportList);
		}
		return true;
	}

	/**
	 * 新增差异入库记录信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean addDiffInbound(WarehouseStoreDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseStoreEntity entity = BeanUtil.copyProperties(dto, WarehouseStoreEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 是否需要入库
		boolean addInventory = Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS);
		entity.setCompletionTime(addInventory ? DateUtil.now() : null);
		// 生成入库单号
		if (Func.isNull(dto.getId())) {
			// 同一业务单只允许一条入库记录
			if (Func.notNull(dto.getRelatedOrderId())) {
				long count = count(Wrappers.<WarehouseStoreEntity>lambdaQuery()
					.eq(WarehouseStoreEntity::getBizType, dto.getBizType())
					.eq(WarehouseStoreEntity::getWarehouseId, dto.getWarehouseId())
					.notIn(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode(),  StoreBizTypeEnum.CHECK.getCode())
					.eq(WarehouseStoreEntity::getRelatedOrderId, dto.getRelatedOrderId()));
				Assert.isTrue(count < 1, () -> new ServiceException("该业务单已存在入库记录"));
			}
			entity.setStoreNo(GenerateNumberUtil.generate(StoreBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
			Assert.isTrue(save(entity), () -> new ServiceException("新增仓库入库记录失败"));
		} else {
			Assert.isTrue(updateById(entity), () -> new ServiceException("更新仓库入库记录失败"));
		}

		// 2.新增或更新仓库入库商品明细
		if (Func.isNotEmpty(dto.getStoreItemList())) {
			// 更新或插入入库商品明细
			List<WarehouseStoreItemEntity> itemEntityList = dto.convertStoreItem(entity.getId(), entity.getStoreNo(),dto.getBizType() );
			warehouseStoreItemService.saveOrUpdateBatch(itemEntityList);
			// 确认入库
			if (addInventory) {
				// 添加商品库存
				Assert.isTrue(inventoryService.addInventory(convertInventory(itemEntityList)), () -> new ServiceException("更新商品库存失败"));
			}
		}

		// 3.新增或更新仓库配套运输库存
		if (Func.isNotEmpty(dto.getStoreSupportList())) {
			List<WarehouseSupportTransEntity> supportList = dto.convertSupportList(entity.getId(), BusinessConstant.WAREHOUSE_STORE);
			warehouseSupportTransService.saveOrUpdateBatch(supportList);
			if (addInventory) {
				Assert.isTrue(inventorySupportTransService.addInventory(supportList),
					() -> new ServiceException("更新商品配套运输品库存库存失败"));
			}
		}
		return true;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean diffSaveOrUpdate(WarehouseStoreDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseStoreEntity entity = BeanUtil.copyProperties(dto, WarehouseStoreEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 是否需要入库
		boolean addInventory = Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS);
		entity.setCompletionTime(addInventory ? DateUtil.now() : null);
		// 生成入库单号
		if (Func.isNull(dto.getId())) {
			// 同一业务单只允许一条入库记录
			if (Func.notNull(dto.getRelatedOrderId())) {
				long count = count(Wrappers.<WarehouseStoreEntity>lambdaQuery()
					.eq(WarehouseStoreEntity::getBizType, dto.getBizType())
					.eq(WarehouseStoreEntity::getWarehouseId, dto.getWarehouseId())
					.notIn(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode(),  StoreBizTypeEnum.CHECK.getCode())
					.eq(WarehouseStoreEntity::getRelatedOrderId, dto.getRelatedOrderId()));
				Assert.isTrue(count < 1, () -> new ServiceException("该业务单已存在入库记录"));
			}
			entity.setStoreNo(GenerateNumberUtil.generate(StoreBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
			// 设置一个默认值
			if (Func.isNull(entity.getRelatedOrderId())) {
				entity.setRelatedOrderId(System.currentTimeMillis());
			}
			Assert.isTrue(save(entity), () -> new ServiceException("新增仓库入库记录失败"));
		} else {
			Assert.isTrue(updateById(entity), () -> new ServiceException("更新仓库入库记录失败"));
		}

		// 2.新增或更新仓库入库商品明细
		List<WarehouseStoreItemEntity> itemEntityList = Lists.newArrayList();
		if (Func.isNotEmpty(dto.getStoreItemList())) {
			// 更新或插入入库商品明细
			itemEntityList = dto.convertStoreItem(entity.getId(), entity.getStoreNo(), dto.getBizType());
			warehouseStoreItemService.saveOrUpdateBatch(itemEntityList);
			// 确认入库
			if (addInventory) {
				// 1. 分离增加和减少库存的商品项
				List<WarehouseStoreItemEntity> itemsToAdd = itemEntityList.stream()
					.filter(item -> (item.getActualQuantity() != null && item.getActualQuantity() > 0) || (item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) > 0))
					.collect(Collectors.toList());

				List<WarehouseStoreItemEntity> itemsToDeductSource = itemEntityList.stream()
					.filter(item -> (item.getActualQuantity() != null && item.getActualQuantity() < 0) || (item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) < 0))
					.collect(Collectors.toList());

				// 2. 处理增加库存
				if (Func.isNotEmpty(itemsToAdd)) {
					Assert.isTrue(inventoryService.addInventory(convertInventory(itemsToAdd)), () -> new ServiceException("增加商品库存失败"));
				}

				// 3. 处理减少库存
				if (Func.isNotEmpty(itemsToDeductSource)) {
					List<WarehouseOutboundItemEntity> itemsToDeduct = itemsToDeductSource.stream().map(item -> {
						WarehouseOutboundItemEntity outboundItem = new WarehouseOutboundItemEntity();
						outboundItem.setWarehouseId(entity.getWarehouseId());
						outboundItem.setProductSkuId(item.getProductSkuId());
						outboundItem.setSupplierId(item.getSupplierId());
						// 扣减库存时使用绝对值
						if (item.getActualQuantity() != null) {
							outboundItem.setQuantity(Math.abs(item.getActualQuantity()));
						}
						if (item.getActualWeight() != null) {
							outboundItem.setWeight(item.getActualWeight().abs());
						}
						return outboundItem;
					}).collect(Collectors.toList());
					Assert.isTrue(inventoryService.deductInventory(dto.getBizType(), itemsToDeduct), () -> new ServiceException("扣减商品库存失败"));
				}
			}
		}

		// 3.新增或更新仓库配套运输库存
		List<WarehouseSupportTransEntity> supportList = null;
		if (Func.isNotEmpty(dto.getStoreSupportList())) {
			supportList = dto.convertSupportList(entity.getId(), BusinessConstant.WAREHOUSE_STORE);
			warehouseSupportTransService.saveOrUpdateBatch(supportList);
			if (addInventory) {
				// 1. 分离增加和减少库存的配套运输项
				List<WarehouseSupportTransEntity> supportsToAdd = supportList.stream()
					.filter(s -> s.getSupportTransNum() != null && s.getSupportTransNum() > 0)
					.collect(Collectors.toList());

				List<WarehouseSupportTransEntity> supportsToDeductSource = supportList.stream()
					.filter(s -> s.getSupportTransNum() != null && s.getSupportTransNum() < 0)
					.collect(Collectors.toList());

				// 2. 处理增加库存
				if (Func.isNotEmpty(supportsToAdd)) {
					Assert.isTrue(inventorySupportTransService.addInventory(supportsToAdd),
						() -> new ServiceException("增加商品配套运输品库存失败"));
				}

				// 3. 处理减少库存
				if (Func.isNotEmpty(supportsToDeductSource)) {
					List<WarehouseSupportTransEntity> supportsToDeduct = supportsToDeductSource.stream().map(s -> {
						WarehouseSupportTransEntity newS = BeanUtil.copy(s, WarehouseSupportTransEntity.class);
						newS.setSupportTransNum(Math.abs(s.getSupportTransNum()));
						return newS;
					}).collect(Collectors.toList());
					Assert.isTrue(inventorySupportTransService.deductInventory(supportsToDeduct),
						() -> new ServiceException("扣减商品配套运输品库存失败"));
				}
			}
		}

		// 4.转运入库需要更新转运单状态和时间
		if (addInventory && StoreBizTypeEnum.isTransport(dto.getBizType())) {
			transportOrderService.updateTransportOrderStore(dto.getRelatedOrderId(),  itemEntityList,  supportList);
		}
		return true;
	}


	/**
	 * 新增/删减差异入库记录信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean diffAddDiffInbound(WarehouseStoreDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseStoreEntity entity = BeanUtil.copyProperties(dto, WarehouseStoreEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 是否需要入库
		boolean addInventory = Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS);
		entity.setCompletionTime(addInventory ? DateUtil.now() : null);
		// 生成入库单号
		if (Func.isNull(dto.getId())) {
			// 同一业务单只允许一条入库记录
			if (Func.notNull(dto.getRelatedOrderId())) {
				long count = count(Wrappers.<WarehouseStoreEntity>lambdaQuery()
					.eq(WarehouseStoreEntity::getBizType, dto.getBizType())
					.eq(WarehouseStoreEntity::getWarehouseId, dto.getWarehouseId())
					.notIn(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode(),  StoreBizTypeEnum.CHECK.getCode())
					.eq(WarehouseStoreEntity::getRelatedOrderId, dto.getRelatedOrderId()));
				Assert.isTrue(count < 1, () -> new ServiceException("该业务单已存在入库记录"));
			}
			entity.setStoreNo(GenerateNumberUtil.generate(StoreBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
			Assert.isTrue(save(entity), () -> new ServiceException("新增仓库入库记录失败"));
		} else {
			Assert.isTrue(updateById(entity), () -> new ServiceException("更新仓库入库记录失败"));
		}

		// 2.新增或更新仓库入库商品明细
		if (Func.isNotEmpty(dto.getStoreItemList())) {
			// 更新或插入入库商品明细
			List<WarehouseStoreItemEntity> itemEntityList = dto.convertStoreItem(entity.getId(), entity.getStoreNo(),dto.getBizType() );
			warehouseStoreItemService.saveOrUpdateBatch(itemEntityList);
			// 确认入库
			if (addInventory) {
				// 1. 分离增加和减少库存的商品项
				List<WarehouseStoreItemEntity> itemsToAdd = itemEntityList.stream()
					.filter(item -> (item.getActualQuantity() != null && item.getActualQuantity() > 0) || (item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) > 0))
					.collect(Collectors.toList());

				List<WarehouseStoreItemEntity> itemsToDeductSource = itemEntityList.stream()
					.filter(item -> (item.getActualQuantity() != null && item.getActualQuantity() < 0) || (item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) < 0))
					.collect(Collectors.toList());

				// 2. 处理增加库存
				if (Func.isNotEmpty(itemsToAdd)) {
					Assert.isTrue(inventoryService.addInventory(convertInventory(itemsToAdd)), () -> new ServiceException("增加商品库存失败"));
				}

				// 3. 处理减少库存
				if (Func.isNotEmpty(itemsToDeductSource)) {
					List<WarehouseOutboundItemEntity> itemsToDeduct = itemsToDeductSource.stream().map(item -> {
						WarehouseOutboundItemEntity outboundItem = new WarehouseOutboundItemEntity();
						outboundItem.setWarehouseId(entity.getWarehouseId());
						outboundItem.setProductSkuId(item.getProductSkuId());
						outboundItem.setSupplierId(item.getSupplierId());
						// 扣减库存时使用绝对值
						if (item.getActualQuantity() != null) {
							outboundItem.setQuantity(Math.abs(item.getActualQuantity()));
						}
						if (item.getActualWeight() != null) {
							outboundItem.setWeight(item.getActualWeight().abs());
						}
						return outboundItem;
					}).collect(Collectors.toList());
					Assert.isTrue(inventoryService.deductInventory(dto.getBizType(), itemsToDeduct), () -> new ServiceException("扣减商品库存失败"));
				}
			}
		}

		// 3.新增或更新仓库配套运输库存
		if (Func.isNotEmpty(dto.getStoreSupportList())) {
			List<WarehouseSupportTransEntity> supportList = dto.convertSupportList(entity.getId(), BusinessConstant.WAREHOUSE_STORE);
			warehouseSupportTransService.saveOrUpdateBatch(supportList);
			if (addInventory) {
				// 1. 分离增加和减少库存的配套运输项
				List<WarehouseSupportTransEntity> supportsToAdd = supportList.stream()
					.filter(s -> s.getSupportTransNum() != null && s.getSupportTransNum() > 0)
					.collect(Collectors.toList());

				List<WarehouseSupportTransEntity> supportsToDeductSource = supportList.stream()
					.filter(s -> s.getSupportTransNum() != null && s.getSupportTransNum() < 0)
					.collect(Collectors.toList());

				// 2. 处理增加库存
				if (Func.isNotEmpty(supportsToAdd)) {
					Assert.isTrue(inventorySupportTransService.addInventory(supportsToAdd),
						() -> new ServiceException("增加商品配套运输品库存失败"));
				}

				// 3. 处理减少库存
				if (Func.isNotEmpty(supportsToDeductSource)) {
					List<WarehouseSupportTransEntity> supportsToDeduct = supportsToDeductSource.stream().map(s -> {
						WarehouseSupportTransEntity newS = BeanUtil.copy(s, WarehouseSupportTransEntity.class);
						newS.setSupportTransNum(Math.abs(s.getSupportTransNum()));
						return newS;
					}).collect(Collectors.toList());
					Assert.isTrue(inventorySupportTransService.deductInventory(supportsToDeduct),
						() -> new ServiceException("扣减商品配套运输品库存失败"));
				}
			}
		}
		return true;
	}


	@Override
	public boolean deductInventory(List<WarehouseOutboundItemEntity> list) {
		// 查询是否还存在商品库存 按照入库时间排序(遵循先进先出)
//		List<WarehouseStoreItemEntity> existsList = warehouseStoreItemService.
//			list(Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
//			.select(WarehouseStoreItemEntity::getId, WarehouseStoreItemEntity::getWarehouseStoreId, WarehouseStoreItemEntity::getProductSkuId,
//				WarehouseStoreItemEntity::getWarehouseStoreNo, WarehouseStoreItemEntity::getRemainingQuantity, WarehouseStoreItemEntity::getRemainingWeight)
//			.eq(WarehouseStoreItemEntity::getWarehouseId, list.get(0).getWarehouseId())
//			.in(WarehouseStoreItemEntity::getProductSkuId, list.stream().map(WarehouseOutboundItemEntity::getProductSkuId).toList())
//			.and(w -> w.gt(WarehouseStoreItemEntity::getRemainingQuantity, 0)
//				.or()
//				.gt(WarehouseStoreItemEntity::getRemainingWeight, 0))
//			.orderByAsc(WarehouseStoreItemEntity::getCreateTime)
//			.last("for update")
//		);
		List<Long> skuIds = list.stream().map(WarehouseOutboundItemEntity::getProductSkuId).toList();
		List<WarehouseStoreItemEntity> existsList = warehouseStoreItemService.listByWarehouseIdAndSkuIds(list.get(0).getWarehouseId(), skuIds);
		Assert.notEmpty(existsList, "商品库存不足");

		// 根据商品sku分组
		Map<Long, List<WarehouseStoreItemEntity>> groupStoreItem = existsList.stream()
			.collect(Collectors.groupingBy(WarehouseStoreItemEntity::getProductSkuId));
		// 需要更新的入库批次
		List<WarehouseStoreItemEntity> updateStoreItemList = Lists.newArrayList();

		// 出库关联的入库批次
		List<WarehouseStoreOutboundRelationEntity> relationList = Lists.newArrayList();

		for (WarehouseOutboundItemEntity entity : list) {
			// 先检查所有商品sku入库库存是否满足需求
			List<WarehouseStoreItemEntity> itemList = groupStoreItem.get(entity.getProductSkuId());
			Assert.notEmpty(itemList, "商品库存不足");

			// 校验所有批次总库存（前面总库存已经校验，理论到这里库存足够）
			checkStoreTotalInventory(itemList, entity);

			// 剩余待扣减库存
			Integer quantity = Func.notNull(entity.getQuantity()) ? entity.getQuantity() : 0;
			BigDecimal weight = Func.notNull(entity.getWeight()) ? entity.getWeight() : BigDecimal.ZERO;

			// 检查扣减数量是否合法
			if (quantity < 0 || weight.compareTo(BigDecimal.ZERO) < 0) {
				throw new ServiceException("扣减数量不能为负数");
			}

			// 已确认的批次优先扣除
			if (Func.notNull(entity.getStoreBatchId())) {
				WarehouseStoreItemEntity itemEntity = itemList.stream()
					.filter(item -> item.getWarehouseStoreId().equals(entity.getStoreBatchId()))
					.findFirst().orElseThrow(() -> new ServiceException("商品库存批次不存在"));

				// 检查批次库存是否足够
				if (itemEntity.getRemainingQuantity() < quantity ||
					itemEntity.getRemainingWeight().compareTo(weight) < 0) {
					throw new ServiceException("指定批次库存不足");
				}

				quantity = itemEntity.deductQuantity(quantity);
				weight = itemEntity.deductWeight(weight);
				updateStoreItemList.add(itemEntity);
				// 添加出入库关联
				relationList.add(WarehouseHelper.outboundRelation(itemEntity, entity.getId(), entity.getWarehouseOutboundId(), entity.getQuantity(), entity.getWeight()));
			}

			// 未扣完
			if (quantity > 0 || weight.compareTo(BigDecimal.ZERO) > 0) {
				// 未扣完按入库时间扣除
				for (WarehouseStoreItemEntity itemEntity : itemList) {
					if (Func.notNull(entity.getStoreBatchId()) && itemEntity.getWarehouseStoreId().equals(entity.getStoreBatchId())) {
						continue;
					}
					quantity = itemEntity.deductQuantity(quantity);
					weight = itemEntity.deductWeight(weight);
					updateStoreItemList.add(itemEntity);
					// 添加出入库关联
					relationList.add(WarehouseHelper.outboundRelation(itemEntity, entity.getId(), entity.getWarehouseOutboundId(), entity.getQuantity(), entity.getWeight()));
					if (quantity == 0 && weight.compareTo(BigDecimal.ZERO) == 0) {
						break;
					}
				}

				// 检查是否还有未扣完的库存
				if (quantity > 0 || weight.compareTo(BigDecimal.ZERO) > 0) {
					throw new ServiceException("库存不足，无法完成扣减");
				}
			}
		}

		if (updateStoreItemList.isEmpty()) {
			return true;
		}
		// 插入出入库关联关系
		Assert.isTrue(warehouseStoreOutboundRelationService.saveBatch(relationList), () -> new ServiceException("插入出入库关联关系失败"));
		// 更新入库批次剩余库存量
		return warehouseStoreItemService.updateBatchById(updateStoreItemList);
	}

	/**
	 * 更新商品库存
	 *
	 * @param warehouseId      仓库id
	 * @param skuId            商品id
	 * @param difference       差异数量
	 * @param differenceWeight 差异重量
	 */
	@Override
	public boolean updateStock(Long warehouseId, Long skuId, Integer difference, BigDecimal differenceWeight, StoreBizTypeEnum bizType, String remark) {
		// 1. 根据仓库id和商品sku去查询对应的批次 按照入库时间排序(遵循先进先出)
//		List<WarehouseStoreItemEntity> existsList = warehouseStoreItemService.list(Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
//			.select(WarehouseStoreItemEntity::getId, WarehouseStoreItemEntity::getWarehouseStoreId, WarehouseStoreItemEntity::getProductSkuId,
//				WarehouseStoreItemEntity::getWarehouseStoreNo, WarehouseStoreItemEntity::getRemainingQuantity, WarehouseStoreItemEntity::getRemainingWeight)
//			.eq(WarehouseStoreItemEntity::getWarehouseId, warehouseId)
//			.eq(WarehouseStoreItemEntity::getProductSkuId, skuId)
//			.and(w -> w.gt(WarehouseStoreItemEntity::getRemainingQuantity, 0)
//				.or()
//				.gt(WarehouseStoreItemEntity::getRemainingWeight, 0))
//			.orderByAsc(WarehouseStoreItemEntity::getCreateTime)
//			.last("for update")
//		);
		List<WarehouseStoreItemEntity> existsList = warehouseStoreItemService.listByWarehouseIdAndSkuIds(warehouseId, Lists.newArrayList(skuId));

		// 需要更新的入库批次
		// List<WarehouseStoreItemEntity> updateStoreItemList = Lists.newArrayList();
		// 使用 Set 确保批次不重复添加，即使数量和重量调整都触碰到同一个批次
		Set<WarehouseStoreItemEntity> itemsToUpdate = new java.util.HashSet<>();


		if (Func.isEmpty(existsList)) {
			// 如果不存在库存记录，则新增一条
			WarehouseStoreEntity storeEntity = new WarehouseStoreEntity();
			storeEntity.setWarehouseId(warehouseId);
			storeEntity.setBizType(bizType.getCode());
			storeEntity.setStatus(BusinessConstant.ENABLE_STATUS);
			storeEntity.setStoreNo(GenerateNumberUtil.generate(bizType.getPrefix(), ""));
			storeEntity.setCompletionTime(DateUtil.now());
			storeEntity.setNote(remark);
			storeEntity.setCreateTimeAt(System.currentTimeMillis());
			// 确保设置创建用户等其他必要字段
			// storeEntity.setCreateUser(AuthUtil.getUserId());
			this.save(storeEntity);

			// 查询SKU库存信息
			SkuStockEntity skuStock = skuStockService.getOne(
				Wrappers.<SkuStockEntity>lambdaQuery()
					.eq(SkuStockEntity::getId, skuId)
					.last("limit 1")
			);
			Assert.notNull(skuStock, "未找到SKU信息 for ID: " + skuId);

			// 查询商品信息 (如果需要商品名称等信息)
			// ProductEntity product = productService.getById(skuStock.getProductId());
			// Assert.notNull(product, "未找到商品信息");

			// 新增库存明细
			WarehouseStoreItemEntity itemEntity = new WarehouseStoreItemEntity();
			itemEntity.setWarehouseId(warehouseId);
			itemEntity.setWarehouseStoreId(storeEntity.getId());
			itemEntity.setWarehouseStoreNo(storeEntity.getStoreNo());
			itemEntity.setProductId(skuStock.getProductId());
			itemEntity.setProductSkuId(skuId);
			// 对于盘点发现的新库存，difference 和 differenceWeight 代表实际数量和重量
			itemEntity.setActualQuantity(Math.max(0, difference));
			itemEntity.setActualWeight((differenceWeight.compareTo(BigDecimal.ZERO) > 0 ? differenceWeight : BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
			itemEntity.setRemainingQuantity(Math.max(0, difference));
			itemEntity.setRemainingWeight((differenceWeight.compareTo(BigDecimal.ZERO) > 0 ? differenceWeight : BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
			itemEntity.setStatus(BusinessConstant.ENABLE_STATUS);
			// 设置其他商品相关信息
			itemEntity.setSkuCode(skuStock.getSkuCode());
			itemEntity.setSpData(skuStock.getSpData());
			// 确保设置创建用户等其他必要字段
			// itemEntity.setCreateUser(AuthUtil.getUserId());
			return warehouseStoreItemService.save(itemEntity);
		} else { // 调整现有库存批次

			// 阶段1: 调整数量
			if (difference > 0) { // 数量增加 (盘盈)
				WarehouseStoreItemEntity targetItem = existsList.get(0); // 应用到第一个可用批次 (通常是最近或最早，取决于existsList的排序)
				targetItem.setRemainingQuantity(targetItem.getRemainingQuantity() + difference);
				itemsToUpdate.add(targetItem);
			} else if (difference < 0) { // 数量减少 (盘亏)
				int qtyToDeduct = Math.abs(difference);
				for (WarehouseStoreItemEntity item : existsList) {
					if (qtyToDeduct == 0) break;
					int canDeduct = Math.min(qtyToDeduct, item.getRemainingQuantity());
					if (canDeduct > 0) {
						item.setRemainingQuantity(item.getRemainingQuantity() - canDeduct);
						qtyToDeduct -= canDeduct;
						itemsToUpdate.add(item);
					}
				}
				if (qtyToDeduct > 0) {
					// 记录警告：盘点调整时，数量不足以完全扣减
					log.warn("盘点调整警告 (数量盘亏): SKU ID {} 在仓库 {} 的库存数量不足以完全扣减盘亏量 {}. 尚有 {} 未能扣减。", skuId, warehouseId, Math.abs(difference), qtyToDeduct);
					// 根据业务策略，这里可能需要抛出异常 new ServiceException("盘点调整失败：库存数量不足以扣减盘亏量");
				}
			}

			// 阶段2: 调整重量
			if (differenceWeight.compareTo(BigDecimal.ZERO) > 0) { // 重量增加
				WarehouseStoreItemEntity targetItem = existsList.get(0); // 应用到第一个可用批次
				// 如果此批次已因数量调整而被修改，从itemsToUpdate中获取最新引用
				WarehouseStoreItemEntity itemForWeightUpdate = itemsToUpdate.stream()
					.filter(i -> i.getId().equals(targetItem.getId()))
					.findFirst()
					.orElse(targetItem); // 如果未在itemsToUpdate中（例如数量无变化），则使用原始targetItem
				itemForWeightUpdate.setRemainingWeight(itemForWeightUpdate.getRemainingWeight().add(differenceWeight).setScale(2, RoundingMode.HALF_UP));
				itemsToUpdate.add(itemForWeightUpdate); // 确保添加到待更新集合
			} else if (differenceWeight.compareTo(BigDecimal.ZERO) < 0) { // 重量减少
				BigDecimal weightToDeduct = differenceWeight.abs();
				for (WarehouseStoreItemEntity item : existsList) { // 再次遍历批次以分配重量扣减
					if (weightToDeduct.compareTo(BigDecimal.ZERO) == 0) break;
					// 获取可能已修改的批次引用
					WarehouseStoreItemEntity itemForWeightUpdate = itemsToUpdate.stream()
						.filter(i -> i.getId().equals(item.getId()))
						.findFirst()
						.orElse(item);

					if (itemForWeightUpdate.getRemainingWeight().compareTo(BigDecimal.ZERO) > 0) {
						BigDecimal canDeduct = weightToDeduct.min(itemForWeightUpdate.getRemainingWeight());
						if (canDeduct.compareTo(BigDecimal.ZERO) > 0) {
							itemForWeightUpdate.setRemainingWeight(itemForWeightUpdate.getRemainingWeight().subtract(canDeduct).setScale(2, RoundingMode.HALF_UP));
							weightToDeduct = weightToDeduct.subtract(canDeduct);
							itemsToUpdate.add(itemForWeightUpdate); // 确保添加到待更新集合
						}
					}
				}
				if (weightToDeduct.compareTo(BigDecimal.ZERO) > 0) {
					// 记录警告：盘点调整时，重量不足以完全扣减
					log.warn("盘点调整警告 (重量盘亏): SKU ID {} 在仓库 {} 的库存重量不足以完全扣减盘亏量 {}. 尚有 {} 未能扣减。", skuId, warehouseId, differenceWeight.abs(), weightToDeduct);
					// 根据业务策略，这里可能需要抛出异常 new ServiceException("盘点调整失败：库存重量不足以扣减盘亏量");
				}
			}

			if (!itemsToUpdate.isEmpty()) {
				return warehouseStoreItemService.updateBatchById(new ArrayList<>(itemsToUpdate));
			}
			return true; // 如果没有实际需要更新的批次 (例如，差异都为0)
		}
	}

	/**
	 * 修改商品库存差异
	 *
	 * @param storeId      入库记录id
	 * @param skuId        商品id
	 * @param diffQuantity 差异数量
	 * @param diffWeight   差异重量
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByDiff(Long storeId, Long skuId, Integer diffQuantity, BigDecimal diffWeight) {
		// 获取入库记录
		WarehouseStoreItemEntity itemEntity = warehouseStoreItemService.getOne(
			Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
				.eq(WarehouseStoreItemEntity::getWarehouseStoreId, storeId)
				.eq(WarehouseStoreItemEntity::getProductSkuId, skuId)
		);
		Assert.notNull(itemEntity, () -> new ServiceException("商品库存不存在"));

		// 调整入库记录的库存
		itemEntity.setRemainingQuantity(itemEntity.getRemainingQuantity() + diffQuantity);
		itemEntity.setRemainingWeight(itemEntity.getRemainingWeight().add(diffWeight));
		warehouseStoreItemService.updateById(itemEntity);

//		// 获取或创建总库存记录并加锁
//		List<InventoryEntity> existsList = existsInventoryList(itemEntity.getWarehouseId(), List.of(skuId));
//		Assert.notEmpty(existsList, () -> new ServiceException("总库存记录不存在"));
//		InventoryEntity inventoryEntity = existsList.get(0);
//
//		// 更新总库存
//		inventoryEntity.setCurrentStock(inventoryEntity.getCurrentStock() + diffQuantity);
//		inventoryEntity.setWeight(inventoryEntity.getWeight().add(diffWeight));
//
//		// 保存或更新总库存记录
//		inventoryService.saveOrUpdate(inventoryEntity);
	}

	/**
	 * 校验商品sku所有批次总库存
	 *
	 * @param storeItemList 入库批次记录
	 * @param entity        出库商品明细
	 */
	private void checkStoreTotalInventory
	(List<WarehouseStoreItemEntity> storeItemList, WarehouseOutboundItemEntity entity) {
		if (Func.notNull(entity.getQuantity())) {
			int totalInventory = storeItemList.stream().mapToInt(WarehouseStoreItemEntity::getRemainingQuantity).sum();
			Assert.isTrue(totalInventory >= entity.getQuantity(), "商品数量库存不足");
		}
		if (Func.notNull(entity.getWeight())) {
			BigDecimal totalInventory = storeItemList.stream().map(WarehouseStoreItemEntity::getRemainingWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
			Assert.isTrue(totalInventory.compareTo(entity.getWeight()) >= 0, "商品重量库存不足");
		}
	}

	/**
	 * 商品库存信息转换
	 */
	private List<InventoryEntity> convertInventory(List<WarehouseStoreItemEntity> itemEntityList) {
		BladeUser user = AuthUtil.getUser();
		List<InventoryEntity> list = new ArrayList<>();
		for (WarehouseStoreItemEntity i : itemEntityList) {
			if (Func.isNull(i.getSupplierId())) {
				continue;
			}
			InventoryEntity inventoryEntity = new InventoryEntity();
			inventoryEntity.setWarehouseId(i.getWarehouseId());
			inventoryEntity.setProductId(i.getProductId());
			inventoryEntity.setProductSkuId(i.getProductSkuId());
			inventoryEntity.setSupplierId(i.getSupplierId());
			inventoryEntity.setSkuCode(i.getSkuCode());
			inventoryEntity.setSpData(i.getSpData());
			inventoryEntity.setCurrentStock(i.getActualQuantity());
			inventoryEntity.setWeight(i.getActualWeight());
			if (Func.notNull(user)) {
				inventoryEntity.setCreateUser(user.getUserId());
				inventoryEntity.setCreateDept(Func.firstLong(user.getDeptId()));
				inventoryEntity.setUpdateUser(user.getUserId());
			}
			list.add(inventoryEntity);
		}
		return list;
	}

	@Override
	public Map<Long, List<WarehouseStoreBatchVO>> listBatch(Long warehouseId, List<Long> productSkuIds) {
		List<WarehouseStoreBatchVO> storeList = baseMapper.listByWarehouseAndSku(warehouseId, productSkuIds);
		return storeList.stream().collect(Collectors.groupingBy(WarehouseStoreBatchVO::getProductSkuId));
	}

	/**
	 * 处理差异入库的批次更新
	 * 按照后进先出原则补充原始批次库存，确保不超过各批次原始入库量，不创建新批次，若差异无法完全吸收则抛出异常。
	 *
	 * @param storeRecords 入库批次记录列表（按入库时间升序排列）
	 * @param skuId 商品SKU ID
	 * @param diffQuantity 差异数量
	 * @param diffWeight 差异重量
	 * @return 是否更新成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBatchByDiffInbound(List<WarehouseStoreEntity> storeRecords, Long skuId, Integer diffQuantity, BigDecimal diffWeight) {
		if (Func.isEmpty(storeRecords) || Func.isNull(skuId)) {
			// 如果基础数据无效，或者没有差异量需要处理，则直接返回
			boolean noQuantityChange = (Func.isNull(diffQuantity) || diffQuantity <= 0);
			boolean noWeightChange = (Func.isNull(diffWeight) || diffWeight.compareTo(BigDecimal.ZERO) <= 0);
			if (noQuantityChange && noWeightChange) {
				return true; // 没有有效差异量需要处理
			}
		}

		// 初始化待增加的差异数量和重量
		int remainingQuantityToAdd = (Func.notNull(diffQuantity) && diffQuantity > 0) ? diffQuantity : 0;
		BigDecimal remainingWeightToAdd = (Func.notNull(diffWeight) && diffWeight.compareTo(BigDecimal.ZERO) > 0) ? diffWeight : BigDecimal.ZERO;

		// 如果初始就没有需要增加的数量和重量，直接成功
		if (remainingQuantityToAdd == 0 && remainingWeightToAdd.compareTo(BigDecimal.ZERO) == 0) {
		    return true;
		}

		// 检查传入的原始差异量是否为负，这在业务上通常不允许差异"入库"负数
		if (Func.notNull(diffQuantity) && diffQuantity < 0) {
			throw new ServiceException("差异入库数量不能为负数");
		}
		if (Func.notNull(diffWeight) && diffWeight.compareTo(BigDecimal.ZERO) < 0) {
			throw new ServiceException("差异入库重量不能为负数");
		}

		List<WarehouseStoreItemEntity> updateItems = new ArrayList<>();

		// 按入库时间倒序处理批次（后进先处理 LIFO principle for adding back)
		for (int i = storeRecords.size() - 1; i >= 0; i--) {
			// 如果差异已完全分配，则提前结束循环
			if (remainingQuantityToAdd == 0 && remainingWeightToAdd.compareTo(BigDecimal.ZERO) == 0) {
				break;
			}

			WarehouseStoreEntity storeRecord = storeRecords.get(i);
			WarehouseStoreItemEntity storeItem = warehouseStoreItemService.getOne(
				Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
					.eq(WarehouseStoreItemEntity::getWarehouseStoreId, storeRecord.getId())
					.eq(WarehouseStoreItemEntity::getProductSkuId, skuId)
			);

			if (storeItem != null) {
				boolean currentItemModified = false;

				// --- 处理数量 --- A
				if (remainingQuantityToAdd > 0) {
					// 计算当前批次还能增加多少数量 (不能超过其原始入库量 actualQuantity)
					int spaceForQuantity = Math.max(0, storeItem.getActualQuantity() - storeItem.getRemainingQuantity());
					if (spaceForQuantity > 0) {
						int quantityToAddThisBatch = Math.min(remainingQuantityToAdd, spaceForQuantity);
						storeItem.setRemainingQuantity(storeItem.getRemainingQuantity() + quantityToAddThisBatch);
						remainingQuantityToAdd -= quantityToAddThisBatch;
						currentItemModified = true;
					}
				}

				// --- 处理重量 --- B
				if (remainingWeightToAdd.compareTo(BigDecimal.ZERO) > 0) {
					// 计算当前批次还能增加多少重量 (不能超过其原始入库量 actualWeight)
					BigDecimal spaceForWeight = storeItem.getActualWeight().subtract(storeItem.getRemainingWeight());
					if (spaceForWeight.compareTo(BigDecimal.ZERO) < 0) { // 确保可增加空间不为负
						spaceForWeight = BigDecimal.ZERO;
					}

					if (spaceForWeight.compareTo(BigDecimal.ZERO) > 0) {
						BigDecimal weightToAddThisBatch = remainingWeightToAdd.min(spaceForWeight);
						storeItem.setRemainingWeight(storeItem.getRemainingWeight().add(weightToAddThisBatch));
						remainingWeightToAdd = remainingWeightToAdd.subtract(weightToAddThisBatch);
						currentItemModified = true;
					}
				}

				if (currentItemModified) {
					// 如果不想重复添加同一个storeItem对象（如果外部循环可能导致），可以先检查
					// 但在此处，每个storeRecord是唯一的，因此storeItem也是唯一的，直接添加即可
					updateItems.add(storeItem);
				}
			}
		}

		// 根据用户需求：不需要创建新批次。如果差异量在遍历完所有指定批次后仍有剩余，
		// 这意味着传入的差异量超出了这些批次可以恢复的上限，应视为错误。
		if (remainingQuantityToAdd > 0 || remainingWeightToAdd.compareTo(BigDecimal.ZERO) > 0) {
			String errorMessage = String.format(
				"差异入库失败：SKU ID [%d] 的差异量无法完全被提供的原始批次吸收。原始差异量 (数量: %s, 重量: %s)，剩余未分配 (数量: %d, 重量: %s)。请检查数据。",
				skuId,
				diffQuantity != null ? diffQuantity.toString() : "未提供",
				diffWeight != null ? diffWeight.toPlainString() : "未提供",
				remainingQuantityToAdd,
				remainingWeightToAdd.toPlainString()
			);
			throw new ServiceException(errorMessage);
		}

		// 批量更新发生变动的入库批次明细
		if (Func.isNotEmpty(updateItems)) {
			return warehouseStoreItemService.updateBatchById(updateItems);
		}

		return true; // 没有条目需要更新（例如，差异量为0或无法分配），但操作本身没有失败
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deductFromBatchesFIFO(List<WarehouseStoreEntity> relevantBatches, Long skuId, Integer totalQtyToDeduct, BigDecimal totalWtToDeduct, String relatedOrderNo, Long relatedOrderId, Integer bizType, Long warehouseId) {
		// 如果总扣减数量和总扣减重量都无效（小于等于0），则直接返回
		if ((totalQtyToDeduct == null || totalQtyToDeduct <= 0) && (totalWtToDeduct == null || totalWtToDeduct.compareTo(BigDecimal.ZERO) <= 0)) {
			return;
		}
		// 如果需要扣减的数量和重量都为0或负数，则无需继续
		Integer qtyToDeduct = (totalQtyToDeduct == null) ? 0 : totalQtyToDeduct;
		BigDecimal wtToDeduct = (totalWtToDeduct == null) ? BigDecimal.ZERO : totalWtToDeduct;

		if (qtyToDeduct <= 0 && wtToDeduct.compareTo(BigDecimal.ZERO) <= 0) {
			return;
		}


		// 确保批次列表按LIFO原则排序（按创建时间降序）
		if (relevantBatches != null && relevantBatches.size() > 1) {
			// 按创建时间降序排列，null的创建时间排在最后
			relevantBatches.sort(Comparator.comparing(WarehouseStoreEntity::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder())));
		}

		int remainingQtyToDeduct = qtyToDeduct;
		BigDecimal remainingWtToDeduct = wtToDeduct;

		// 1. 首先，校验所有相关批次中该SKU的总可用库存是否充足
		int totalAvailableQtyInBatches = 0;
		BigDecimal totalAvailableWtInBatches = BigDecimal.ZERO;

		if (relevantBatches != null) {
			for (WarehouseStoreEntity batch : relevantBatches) {
				// 查询批次中特定SKU的库存明细
				WarehouseStoreItemEntity itemInBatch = warehouseStoreItemService.getOne(
					Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
						.eq(WarehouseStoreItemEntity::getWarehouseStoreId, batch.getId())
						.eq(WarehouseStoreItemEntity::getProductSkuId, skuId)
				);
				if (itemInBatch != null) {
					// 累加可用数量和可用重量
					totalAvailableQtyInBatches += Func.notNull(itemInBatch.getRemainingQuantity()) ? itemInBatch.getRemainingQuantity() : 0;
					totalAvailableWtInBatches = totalAvailableWtInBatches.add(Func.notNull(itemInBatch.getRemainingWeight()) ? itemInBatch.getRemainingWeight() : BigDecimal.ZERO);
				}
			}
		}

		// 校验总可用数量是否小于总需扣减数量
		if (qtyToDeduct > 0 && totalAvailableQtyInBatches < qtyToDeduct) {
			//库存不足，抛出业务异常
			throw new ServiceException(String.format("SKU ID %d 库存不足，需求量 %d，批次中可用总量 %d", skuId, qtyToDeduct, totalAvailableQtyInBatches));
		}
		// 可选：如果重量是独立且严格的扣减约束，也进行类似校验
		if (wtToDeduct.compareTo(BigDecimal.ZERO) > 0 && totalAvailableWtInBatches.compareTo(wtToDeduct) < 0) {
		   throw new ServiceException(String.format("SKU ID %d 重量不足，需求重量 %s，批次中可用总重量 %s",
		                                        skuId, wtToDeduct.toPlainString(), totalAvailableWtInBatches.toPlainString()));
		}

		// 2. 遍历批次，按LIFO顺序扣减库存
		if (relevantBatches != null) {
			for (WarehouseStoreEntity batch : relevantBatches) {
				// 如果剩余需扣减数量和重量均已满足，则提前结束
				if (remainingQtyToDeduct <= 0 && remainingWtToDeduct.compareTo(BigDecimal.ZERO) <= 0 ) {
					break;
				}

				// 获取当前批次中该SKU的库存明细
				WarehouseStoreItemEntity itemInBatch = warehouseStoreItemService.getOne(
					Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
						.eq(WarehouseStoreItemEntity::getWarehouseStoreId, batch.getId())
						.eq(WarehouseStoreItemEntity::getProductSkuId, skuId)
				);

				// 如果当前批次没有该SKU的库存项，或者可用数量和重量都为0，则跳过此批次
				if (itemInBatch == null || ((Func.notNull(itemInBatch.getRemainingQuantity()) ? itemInBatch.getRemainingQuantity() : 0) <= 0 && (Func.notNull(itemInBatch.getRemainingWeight()) ? itemInBatch.getRemainingWeight() : BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <=0 )) {
					continue;
				}

				int qtyDeductedFromThisBatch = 0; // 本次从当前批次扣减的数量
				BigDecimal wtDeductedFromThisBatch = BigDecimal.ZERO; // 本次从当前批次扣减的重量
				int currentBatchAvailableQty = (Func.notNull(itemInBatch.getRemainingQuantity()) ? itemInBatch.getRemainingQuantity() : 0); // 当前批次可用数量
				BigDecimal currentBatchAvailableWt = (Func.notNull(itemInBatch.getRemainingWeight()) ? itemInBatch.getRemainingWeight() : BigDecimal.ZERO); // 当前批次可用重量

				if (remainingQtyToDeduct > 0 && currentBatchAvailableQty > 0) {
					qtyDeductedFromThisBatch = Math.min(remainingQtyToDeduct, currentBatchAvailableQty);

					// 如果当前批次有可用重量，则按比例计算应扣减的重量
					if (currentBatchAvailableQty > 0 && currentBatchAvailableWt.compareTo(BigDecimal.ZERO) > 0) {
						wtDeductedFromThisBatch = currentBatchAvailableWt
							.multiply(new BigDecimal(qtyDeductedFromThisBatch)) // 使用实际从本批扣减的数量计算
							.divide(new BigDecimal(currentBatchAvailableQty), 2, BigDecimal.ROUND_HALF_UP); // 四舍五入保留两位小数
					} else {
						wtDeductedFromThisBatch = BigDecimal.ZERO; // 若批次无重量或无数量，则不扣减重量
					}
					// 如果总重量扣减有要求，且按比例计算的重量超过剩余应扣减重量，则以剩余应扣减重量为准
					if (wtToDeduct.compareTo(BigDecimal.ZERO) > 0 && remainingWtToDeduct.compareTo(BigDecimal.ZERO) > 0 && wtDeductedFromThisBatch.compareTo(remainingWtToDeduct) > 0){
						wtDeductedFromThisBatch = remainingWtToDeduct;
					}

				} else if (wtToDeduct.compareTo(BigDecimal.ZERO) > 0 && remainingWtToDeduct.compareTo(BigDecimal.ZERO) > 0 && currentBatchAvailableWt.compareTo(BigDecimal.ZERO) > 0) {
					// 此情况处理：当数量已满足或非主要扣减因素时，纯粹按剩余重量扣减
					wtDeductedFromThisBatch = currentBatchAvailableWt.min(remainingWtToDeduct);
					// qtyDeductedFromThisBatch 已经为 0 或不应再改变
				}

				// 只有当实际扣减数量或重量大于0时，才调用库存更新方法
				if (qtyDeductedFromThisBatch > 0 || wtDeductedFromThisBatch.compareTo(BigDecimal.ZERO) > 0) {
					// 调用 updateByDiff 方法。根据约定，传递负值以表示库存减少。
					this.updateByDiff(batch.getId(), skuId, -qtyDeductedFromThisBatch,
									  wtDeductedFromThisBatch.compareTo(BigDecimal.ZERO) != 0 ? wtDeductedFromThisBatch.negate() : BigDecimal.ZERO);

					// 扣减成功后，更新剩余待扣减的数量和重量
					remainingQtyToDeduct -= qtyDeductedFromThisBatch;
					remainingWtToDeduct = remainingWtToDeduct.subtract(wtDeductedFromThisBatch);
				}
			}
		}

		// 3. 最终校验：如果遍历完所有批次后，仍有未满足的扣减量，则抛出异常
		if (qtyToDeduct > 0 && remainingQtyToDeduct > 0) {
			throw new ServiceException(String.format("SKU ID %d 最终库存不足，原需求量 %d，成功扣除 %d，仍有 %d 未满足",
												skuId, qtyToDeduct, qtyToDeduct - remainingQtyToDeduct, remainingQtyToDeduct));
		}
		// 可选：对重量进行最终校验
		if (wtToDeduct.compareTo(BigDecimal.ZERO) > 0 && remainingWtToDeduct.compareTo(BigDecimal.ZERO) > 0) {
		   throw new ServiceException(String.format("SKU ID %d 最终重量不足，原需求重量 %s，成功扣除 %s，仍有 %s 未满足",
		                                        skuId, wtToDeduct.toPlainString(), wtToDeduct.subtract(remainingWtToDeduct).toPlainString(), remainingWtToDeduct.toPlainString()));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchSupplierIn(WarehouseStoreBatchSupplierInDTO dto) {
		List<WarehouseStoreBatchItemDTO> storeItemList = dto.getStoreItemList();
		// 入库主记录
		Map<Long, WarehouseStoreEntity> batchMap = new HashMap<>();
		// 入库明细记录
		Map<Long, List<WarehouseStoreItemEntity>> batchStoreItemMap = new HashMap<>();
		// 入库配套运输品品记录
		Map<Long, List<WarehouseSupportTransEntity>> batchSupportTransMap = new HashMap<>();

		Date completionTime = new Date();
		Long createTimeAt = System.currentTimeMillis();
		// 根据备货单进行分组入库
		for (WarehouseStoreBatchItemDTO itemDTO : storeItemList) {
			WarehouseStoreEntity entity = batchMap.get(itemDTO.getReplenishmentId());
			if (Func.isNull(entity)) {
				// 入库主记录
				entity = new WarehouseStoreEntity();
				entity.setWarehouseId(dto.getWarehouseId());
				entity.setStoreNo(GenerateNumberUtil.generate(StoreBizTypeEnum.PURCHASE_IN.getPrefix(), ""));
				entity.setBizType(StoreBizTypeEnum.PURCHASE_IN.getCode());
				entity.setStatus(BusinessConstant.ENABLE_STATUS);
				entity.setRelatedOrderId(itemDTO.getReplenishmentId());
				entity.setRelatedOrderNo(itemDTO.getReplenishmentNo());
				entity.setRelatedSourceId(itemDTO.getSupplierId());
				entity.setCompletionTime(completionTime);
				entity.setCreateTimeAt(createTimeAt);
				entity.setNote(dto.getNote());
				batchMap.put(itemDTO.getReplenishmentId(), entity);
			}
			// 入库明细记录
			WarehouseStoreItemEntity itemEntity = BeanUtil.copyProperties(itemDTO, WarehouseStoreItemEntity.class);
			itemEntity.setId(IdWorker.getId());
			itemEntity.setWarehouseId(dto.getWarehouseId());
			itemEntity.setWarehouseStoreNo(entity.getStoreNo());
			itemEntity.setAllowDeleted(BusinessConstant.ENABLE_STATUS);
			if (!batchStoreItemMap.containsKey(itemDTO.getReplenishmentId())) {
				batchStoreItemMap.put(itemDTO.getReplenishmentId(), new ArrayList<>());
			}
			batchStoreItemMap.get(itemDTO.getReplenishmentId()).add(itemEntity);

			if (Func.isNull(itemDTO.getSupportTransUnitId())) {
				continue;
			}
			// 入库配套运输品明细
			WarehouseSupportTransEntity supportTransEntity = itemDTO.convertToSupportTransEntity();
			supportTransEntity.setRelatedRecordItemId(itemEntity.getId());
			supportTransEntity.setWarehouseId(dto.getWarehouseId());
			if (!batchSupportTransMap.containsKey(itemDTO.getReplenishmentId())) {
				batchSupportTransMap.put(itemDTO.getReplenishmentId(), new ArrayList<>());
			}
			batchSupportTransMap.get(itemDTO.getReplenishmentId()).add(supportTransEntity);
		}

		// 批量插入入库主记录
		Collection<WarehouseStoreEntity> storeList = batchMap.values();
		this.saveBatch(storeList);
		// 备货单id对应的主键id关系
		Map<Long, Long> idMap = storeList.stream().collect(Collectors.toMap(WarehouseStoreEntity::getRelatedOrderId, WarehouseStoreEntity::getId));

		// 批量插入入库明细记录
		this.batchStoreItem(dto, idMap, batchStoreItemMap);

		if (Func.isEmpty(batchSupportTransMap)) {
			return;
		}
		// 批量插入入库配套运输品记录
		this.batchStoreSupportTrans(idMap, batchSupportTransMap);
//		throw new ServiceException("测试入库失败");
	}
	/**
	 * 获取差异入库单-根据转运单id
	 *
	 * @param transportOrderId 转运单id
	 * @return 差异入库单-根据转运单id
	 */
	@Override
	public List<WarehouseStoreItemEntity>getDiffInboundByTransportOrderId(Long transportOrderId){
		List<WarehouseStoreEntity> storeList = baseMapper.selectList(
			Wrappers.<WarehouseStoreEntity>lambdaQuery()
				.eq(WarehouseStoreEntity::getRelatedOrderId, transportOrderId)
				.eq(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode())
		);
		if (Func.isEmpty(storeList)) {
			return new ArrayList<>();
		}
		List<Long> storeIds = storeList.stream().map(WarehouseStoreEntity::getId).collect(Collectors.toList());
		return warehouseStoreItemService.list(Wrappers.<WarehouseStoreItemEntity>lambdaQuery().in(WarehouseStoreItemEntity::getWarehouseStoreId, storeIds));
	}

	@Override
	public Map<Long, List<WarehouseStoreItemEntity>> getDiffInboundByTransportOrderIds(List<Long> transportOrderIds) {
		if (Func.isEmpty(transportOrderIds)) {
			return new HashMap<>();
		}
		List<WarehouseStoreEntity> storeList = baseMapper.selectList(
			Wrappers.<WarehouseStoreEntity>lambdaQuery()
				.in(WarehouseStoreEntity::getRelatedOrderId, transportOrderIds)
				.eq(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.DIFF.getCode())
		);
		if (Func.isEmpty(storeList)) {
			return new HashMap<>();
		}
		List<Long> storeIds = storeList.stream().map(WarehouseStoreEntity::getId).collect(Collectors.toList());
		List<WarehouseStoreItemEntity> storeItems = warehouseStoreItemService.list(Wrappers.<WarehouseStoreItemEntity>lambdaQuery().in(WarehouseStoreItemEntity::getWarehouseStoreId, storeIds));

		if (Func.isEmpty(storeItems)) {
			return new HashMap<>();
		}

		Map<Long, Long> storeIdToTransportOrderIdMap = storeList.stream()
			.collect(Collectors.toMap(WarehouseStoreEntity::getId, WarehouseStoreEntity::getRelatedOrderId, (a, b) -> a));

		return storeItems.stream().collect(Collectors.groupingBy(item -> storeIdToTransportOrderIdMap.get(item.getWarehouseStoreId())));
	}

	/**
	 * 批量入库商品明细记录
	 */
	private void batchStoreItem(WarehouseStoreBatchSupplierInDTO dto, Map<Long, Long> idMap, Map<Long, List<WarehouseStoreItemEntity>> batchStoreItemMap) {
		if (Func.isEmpty(batchStoreItemMap)) {
			return;
		}
		// 设置商品明细主键id
		for (Map.Entry<Long, List<WarehouseStoreItemEntity>> entry : batchStoreItemMap.entrySet()) {
			List<WarehouseStoreItemEntity> itemList = entry.getValue();
			for (WarehouseStoreItemEntity item : itemList) {
				item.setWarehouseStoreId(idMap.get(entry.getKey()));
			}
		}
		List<WarehouseStoreItemEntity> itemList = batchStoreItemMap.values().stream().flatMap(Collection::stream).toList();
		// 批量插入入库明细记录
		this.warehouseStoreItemService.saveBatch(itemList);

		// 批量插入库存
		List<InventoryEntity> inventoryList = convertInventory(itemList);
		List<InventoryEntity> mergeItemList = mergeItem(inventoryList);
		mergeItemList.forEach(inventory -> inventory.setLockStock(inventory.getCurrentStock()));
		// 为了跨天需要将已有订单进行锁定库存，需要将通过订单来的入库量进行插入到锁定库存量
		// 在所有备货单中排除自采购单，不记入锁定数量
		List<Long> itemIds = dto.getStoreItemList().stream().map(WarehouseStoreBatchItemDTO::getPurchaseOrderItemId).distinct().toList();
		List<Long> selfPurchaserIds = purchaseOrderItemMapper.filterPurchaseOrderItem(itemIds);
		if (Func.isNotEmpty(selfPurchaserIds)) {
			log.info("冻结库存排除自采购单id:{}", Func.join(selfPurchaserIds));
			List<WarehouseStoreBatchItemDTO> filterItemList = dto.getStoreItemList().stream()
				.filter(item -> selfPurchaserIds.contains(item.getPurchaseOrderItemId()))
				.toList();
			mergeItemList.forEach(inventory -> {
				List<WarehouseStoreBatchItemDTO> filterList = filterItemList.stream()
					.filter(item -> item.getSupplierId().equals(inventory.getSupplierId()) && item.getProductSkuId().equals(inventory.getProductSkuId())).toList();
				if (Func.isNotEmpty(filterList)) {
					int filterSum = filterList.stream().mapToInt(WarehouseStoreBatchItemDTO::getActualQuantity).sum();
					if (inventory.getLockStock() > filterSum) {
						log.info("冻结库存排除自采购单数量:{}", filterSum);
						inventory.setLockStock(inventory.getLockStock() - filterSum);
					} else {
						log.info("计算出错冻结库存排除自采购单数量不足,入库数量:{},排除数量:{}", inventory.getLockStock(), filterSum);
					}
				}
			});
		}
		Assert.isTrue(inventoryService.addInventory(mergeItemList), () -> new ServiceException("更新商品库存失败"));

		// 供应商供货入库需要更新备货单状态并算出转运费
		for (Map.Entry<Long, List<WarehouseStoreItemEntity>> entry : batchStoreItemMap.entrySet()) {
			replenishmentOrderService.updateAndGenerateAmount(entry.getKey(), entry.getValue());
		}
	}

	/**
	 * 批量入库配套运输品明细
	 */
	private void batchStoreSupportTrans(Map<Long, Long> idMap, Map<Long, List<WarehouseSupportTransEntity>> batchSupportTransMap) {
		// 设置关联主键id
		for (Map.Entry<Long, List<WarehouseSupportTransEntity>> entry : batchSupportTransMap.entrySet()) {
			List<WarehouseSupportTransEntity> transList = entry.getValue();
			for (WarehouseSupportTransEntity trans : transList) {
				trans.setRelatedRecordId(idMap.get(entry.getKey()));
			}
		}
		// 批量插入入库配套运输品明细
		List<WarehouseSupportTransEntity> supportTransEntityList = batchSupportTransMap.values().stream().flatMap(Collection::stream).toList();
		Assert.isTrue(this.warehouseSupportTransService.saveBatch(supportTransEntityList),
			() -> new ServiceException("插入入库配套运输品明细失败"));

		// 批量插入库存
		Assert.isTrue(inventorySupportTransService.addInventory(mergeSupportTrans(supportTransEntityList)),
			() -> new ServiceException("更新商品配套运输品库存库存失败"));
	}

	/**
	 * 根据sku、供应商合并数据并对数量进行累加
	 */
	private List<InventoryEntity> mergeItem(List<InventoryEntity> inventoryList) {
		record CompositeKey(Long sku, Long supplierId) {}
		return inventoryList.stream().collect(Collectors.toMap(
			i -> new CompositeKey(
				i.getProductSkuId(),
				i.getSupplierId()
			),
			Function.identity(),
			(a, b) -> {
				InventoryEntity entity = new InventoryEntity();
				entity.setWarehouseId(a.getWarehouseId());
				entity.setProductId(a.getProductId());
				entity.setProductSkuId(a.getProductSkuId());
				entity.setSupplierId(a.getSupplierId());
				entity.setCurrentStock(a.getCurrentStock() + b.getCurrentStock());
				entity.setWeight(a.getWeight().add(b.getWeight()));
				entity.setSkuCode(a.getSkuCode());
				entity.setSpData(a.getSpData());
				entity.setCreateUser(a.getCreateUser());
				entity.setUpdateUser(a.getUpdateUser());
				entity.setCreateDept(a.getCreateDept());
				return entity;
			}
		)).values().stream().toList();
	}

	/**
	 * 根据sku、供应商和配套运输品合并数据并对数量进行累加
	 */
	private List<WarehouseSupportTransEntity> mergeSupportTrans(List<WarehouseSupportTransEntity> supportList) {
		record CompositeKey(Long sku, Long supplierId, Long transportId) {}
		return supportList.stream()
			.collect(Collectors.toMap(
				s -> new CompositeKey(
					s.getProductSkuId(),
					s.getSupplierId(),
					s.getSupportTransUnitId()
				),
				Function.identity(),
				(a, b) -> {
					WarehouseSupportTransEntity entity = new WarehouseSupportTransEntity();
					entity.setWarehouseId(a.getWarehouseId());
					entity.setProductSkuId(a.getProductSkuId());
					entity.setBizType(a.getBizType());
					entity.setSupplierId(a.getSupplierId());
					entity.setSupportTransUnitId(a.getSupportTransUnitId());
					entity.setSupportTransNum(a.getSupportTransNum() + b.getSupportTransNum());
					entity.setRelatedRecordId(a.getRelatedRecordId());
					entity.setSupportTransPrice(a.getSupportTransPrice());
					return entity;
				}
			))
			.values().stream().toList();
	}

	@Override
	public List<WarehouseStoreRecordVO> selectInboundRecordsForDiffDetail(Long warehouseId, Long supplierId, Long productSkuId, String startTime, String endTime) {
		return baseMapper.selectInboundRecordsForDiffDetail(warehouseId, supplierId, productSkuId, startTime, endTime);
	}

	/**
	 * =================================== 入库记录统计 ===================================
	 */

	@Override
	public IPage<PartitionSummaryVO> getPartitionSummary(IPage<PartitionSummaryVO> page, WarehouseStatisticsQueryDTO queryDTO) {
		page.setRecords(baseMapper.getPartitionSummary(page, queryDTO));
		if (page.getTotal() > 0) {
			addSummaryRow(page.getRecords(), queryDTO, PartitionSummaryVO.class);
		}
		return page;
	}

	@Override
	public IPage<ProductSummaryVO> getProductSummary(IPage<ProductSummaryVO> page, WarehouseStatisticsQueryDTO queryDTO) {
		page.setRecords(baseMapper.getProductSummary(page, queryDTO));
		if (page.getTotal() > 0) {
			addSummaryRow(page.getRecords(), queryDTO, ProductSummaryVO.class);
		}
		return page;
	}

	@Override
	public IPage<SupplierSummaryVO> getSupplierSummary(IPage<SupplierSummaryVO> page, WarehouseStatisticsQueryDTO queryDTO) {
		page.setRecords(baseMapper.getSupplierSummary(page, queryDTO));
		if (page.getTotal() > 0) {
			addSummaryRow(page.getRecords(), queryDTO, SupplierSummaryVO.class);
		}
		return page;
	}

	@Override
	public IPage<PurchaserSummaryVO> getPurchaserSummary(IPage<PurchaserSummaryVO> page, WarehouseStatisticsQueryDTO queryDTO) {
		page.setRecords(baseMapper.getPurchaserSummary(page, queryDTO));
		if (page.getTotal() > 0) {
			addSummaryRow(page.getRecords(), queryDTO, PurchaserSummaryVO.class);
		}
		return page;
	}

	@Override
	public List<PartitionSummaryVO> exportPartitionSummary(WarehouseStatisticsQueryDTO queryDTO) {
		List<PartitionSummaryVO> records = baseMapper.getPartitionSummary(null, queryDTO);
		if (records != null && !records.isEmpty()){
			addSummaryRowByCalculation(records, PartitionSummaryVO.class);
		}
		return records;
	}

	@Override
	public List<ProductSummaryVO> exportProductSummary(WarehouseStatisticsQueryDTO queryDTO) {
		List<ProductSummaryVO> records = baseMapper.getProductSummary(null, queryDTO);
		if (records != null && !records.isEmpty()){
			addSummaryRowByCalculation(records, ProductSummaryVO.class);
		}
		return records;
	}

	@Override
	public List<SupplierSummaryVO> exportSupplierSummary(WarehouseStatisticsQueryDTO queryDTO) {
		List<SupplierSummaryVO> records = baseMapper.getSupplierSummary(null, queryDTO);
		if (records != null && !records.isEmpty()){
			addSummaryRowByCalculation(records, SupplierSummaryVO.class);
		}
		return records;
	}

	@Override
	public List<PurchaserSummaryVO> exportPurchaserSummary(WarehouseStatisticsQueryDTO queryDTO) {
		List<PurchaserSummaryVO> records = baseMapper.getPurchaserSummary(null, queryDTO);
		if (!records.isEmpty()) {
			addSummaryRowByCalculation(records, PurchaserSummaryVO.class);
		}
		return records;
	}

	@Override
	public List<SupplierStatisticsVO> getSupplierDetailSummary(SupplierStatisticsQueryDTO queryDTO) {
		if (Func.isNull(queryDTO.getSupplierId()) || Func.isBlank(queryDTO.getDateTime())) {
			return List.of();
		}
		LocalDate localDate = LocalDate.parse(queryDTO.getDateTime());
		Long startTime = DateUtil.toMilliseconds(localDate.atStartOfDay());
		Long endTime = DateUtil.toMilliseconds(localDate.atTime(LocalTime.MAX));
		List<SupplierStatisticsVO> records = baseMapper.getSupplierDetailSummary(queryDTO.getWarehouseId(), queryDTO.getSupplierId(), startTime, endTime);
		if (records == null || records.isEmpty()) {
			return records;
		}

		// 创建合计行
		SupplierStatisticsVO totalRow = new SupplierStatisticsVO();

		// 初始化数值
		totalRow.setPurchaseQuantity(0);
		totalRow.setPurchaseTotalAmount(BigDecimal.ZERO);
		totalRow.setPurchaseSupportTransNum(0);
		totalRow.setPurchaseSupportTransTotalAmount(BigDecimal.ZERO);
		totalRow.setStoreQuantity(0);
		totalRow.setStoreWeight(BigDecimal.ZERO);
		totalRow.setStoreTotalAmount(BigDecimal.ZERO);
		totalRow.setDiffQuantity(0);
		totalRow.setDiffWeight(BigDecimal.ZERO);
		totalRow.setStoreSupportTransNum(0);
		totalRow.setDiffSupportTransNum(0);
		totalRow.setStoreSupportTransTotalAmount(BigDecimal.ZERO);
		totalRow.setForwardingFeeTotalAmount(BigDecimal.ZERO);

		// 累加
		for (SupplierStatisticsVO record : records) {
			totalRow.setPurchaseQuantity(totalRow.getPurchaseQuantity() + (Func.notNull(record.getPurchaseQuantity()) ? record.getPurchaseQuantity() : 0));
			totalRow.setPurchaseTotalAmount(totalRow.getPurchaseTotalAmount().add(Optional.ofNullable(record.getPurchaseTotalAmount()).orElse(BigDecimal.ZERO)));
			totalRow.setPurchaseSupportTransNum(totalRow.getPurchaseSupportTransNum() + (Func.notNull(record.getPurchaseSupportTransNum()) ? record.getPurchaseSupportTransNum() : 0));
			totalRow.setPurchaseSupportTransTotalAmount(totalRow.getPurchaseSupportTransTotalAmount().add(Optional.ofNullable(record.getPurchaseSupportTransTotalAmount()).orElse(BigDecimal.ZERO)));
			totalRow.setStoreQuantity(totalRow.getStoreQuantity() + (Func.notNull(record.getStoreQuantity()) ? record.getStoreQuantity() : 0));
			totalRow.setStoreWeight(totalRow.getStoreWeight().add(Optional.ofNullable(record.getStoreWeight()).orElse(BigDecimal.ZERO)));
			totalRow.setStoreTotalAmount(totalRow.getStoreTotalAmount().add(Optional.ofNullable(record.getStoreTotalAmount()).orElse(BigDecimal.ZERO)));
			totalRow.setDiffQuantity(totalRow.getDiffQuantity() + (Func.notNull(record.getDiffQuantity()) ? record.getDiffQuantity() : 0));
			totalRow.setDiffWeight(totalRow.getDiffWeight().add(Optional.ofNullable(record.getDiffWeight()).orElse(BigDecimal.ZERO)));
			totalRow.setStoreSupportTransNum(totalRow.getStoreSupportTransNum() + (Func.notNull(record.getStoreSupportTransNum()) ? record.getStoreSupportTransNum() : 0));
			totalRow.setDiffSupportTransNum(totalRow.getDiffSupportTransNum() + (Func.notNull(record.getDiffSupportTransNum()) ? record.getDiffSupportTransNum() : 0));
			totalRow.setStoreSupportTransTotalAmount(totalRow.getStoreSupportTransTotalAmount().add(Optional.ofNullable(record.getStoreSupportTransTotalAmount()).orElse(BigDecimal.ZERO)));
			totalRow.setForwardingFeeTotalAmount(totalRow.getForwardingFeeTotalAmount().add(Optional.ofNullable(record.getForwardingFeeTotalAmount()).orElse(BigDecimal.ZERO)));
		}

		records.add(totalRow);
		return records;
	}

	/**
	 * 查询汇总
	 */
	private <T extends ProductSummaryVO> void addSummaryRow(List<T> records, WarehouseStatisticsQueryDTO queryDTO, Class<T> clazz) {
		try {
			TotalSummaryVO summary = baseMapper.getStatisticsTotal(queryDTO);
			T summaryRow = clazz.getDeclaredConstructor().newInstance();
			summaryRow.setPurchaseQuantity(summary.getPurchaseQuantity());
			summaryRow.setPurchaseTotalAmount(summary.getPurchaseTotalAmount());
			summaryRow.setStoreQuantity(summary.getStoreQuantity());
			summaryRow.setStoreWeight(summary.getStoreWeight());
			summaryRow.setStoreTotalAmount(summary.getStoreTotalAmount());
			summaryRow.setPurchaseSupportTransNum(summary.getPurchaseSupportTransNum());
			summaryRow.setPurchaseSupportTransTotalAmount(summary.getPurchaseSupportTransTotalAmount());
			summaryRow.setStoreSupportTransNum(summary.getStoreSupportTransNum());
			summaryRow.setStoreSupportTransTotalAmount(summary.getStoreSupportTransTotalAmount());
			records.add(summaryRow);
		} catch (Exception e) {
			log.error("添加汇总行失败", e);
		}
	}

	/**
	 * 导出添加汇总行
	 */
	private <T extends ProductSummaryVO> void addSummaryRowByCalculation(List<T> records, Class<T> clazz) {
		if (records == null || records.isEmpty()) {
			return;
		}
		// 定义初始值
		int purchaseQuantity = 0;
		BigDecimal purchaseTotalAmount = BigDecimal.ZERO;
		int storeQuantity = 0;
		BigDecimal storeWeight = BigDecimal.ZERO;
		BigDecimal storeTotalAmount = BigDecimal.ZERO;
		int purchaseSupportTransNum = 0;
		BigDecimal purchaseSupportTransTotalAmount = BigDecimal.ZERO;
		int storeSupportTransNum = 0;
		BigDecimal storeSupportTransTotalAmount = BigDecimal.ZERO;

		JsonValueExtractor extractor = new JsonValueExtractor();
		// 循环累加
		for (T record : records) {
			purchaseQuantity = purchaseQuantity + (Optional.ofNullable(record.getPurchaseQuantity()).orElse(0));
			purchaseTotalAmount = purchaseTotalAmount.add(Optional.ofNullable(record.getPurchaseTotalAmount()).orElse(BigDecimal.ZERO));
			storeQuantity = storeQuantity + (Optional.ofNullable(record.getStoreQuantity()).orElse(0));
			storeWeight = storeWeight.add(Optional.ofNullable(record.getStoreWeight()).orElse(BigDecimal.ZERO));
			storeTotalAmount = storeTotalAmount.add(Optional.ofNullable(record.getStoreTotalAmount()).orElse(BigDecimal.ZERO));
			purchaseSupportTransNum += Optional.ofNullable(record.getPurchaseSupportTransNum()).orElse(0);
			purchaseSupportTransTotalAmount = purchaseSupportTransTotalAmount.add(Optional.ofNullable(record.getPurchaseSupportTransTotalAmount()).orElse(BigDecimal.ZERO));
			storeSupportTransNum += Optional.ofNullable(record.getStoreSupportTransNum()).orElse(0);
			storeSupportTransTotalAmount = storeSupportTransTotalAmount.add(Optional.ofNullable(record.getStoreSupportTransTotalAmount()).orElse(BigDecimal.ZERO));
			record.setSpData(extractor.extractAndJoinJsonValues(record.getSpData()));
		}

		try {
			T summaryRow = clazz.getDeclaredConstructor().newInstance();
			if (summaryRow instanceof PartitionSummaryVO) {
				((PartitionSummaryVO) summaryRow).setWarehouseName("总计");
			} else if (summaryRow instanceof SupplierSummaryVO) {
				((SupplierSummaryVO) summaryRow).setSupplierName("总计");
			} else if (summaryRow instanceof PurchaserSummaryVO) {
				((PurchaserSummaryVO) summaryRow).setPurchaserName("总计");
			} else {
				summaryRow.setSkuCode("总计");
			}

			summaryRow.setPurchaseQuantity(purchaseQuantity);
			summaryRow.setPurchaseTotalAmount(purchaseTotalAmount);
			summaryRow.setStoreQuantity(storeQuantity);
			summaryRow.setStoreWeight(storeWeight);
			summaryRow.setStoreTotalAmount(storeTotalAmount);
			summaryRow.setPurchaseSupportTransNum(purchaseSupportTransNum);
			summaryRow.setPurchaseSupportTransTotalAmount(purchaseSupportTransTotalAmount);
			summaryRow.setStoreSupportTransNum(storeSupportTransNum);
			summaryRow.setStoreSupportTransTotalAmount(storeSupportTransTotalAmount);
			records.add(summaryRow);
		} catch (Exception e) {
			log.error("创建合计行失败", e);
		}
	}
}
