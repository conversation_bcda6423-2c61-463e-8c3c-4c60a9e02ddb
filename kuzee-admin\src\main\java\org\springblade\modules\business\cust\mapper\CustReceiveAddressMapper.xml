<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustReceiveAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custReceiveAddressResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustReceiveAddressEntity">
        <result column="cust_id" property="custId"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="default_status" property="defaultStatus"/>
        <result column="post_code" property="postCode"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="region" property="region"/>
        <result column="region_name" property="regionName"/>
        <result column="detail_address" property="detailAddress"/>
    </resultMap>

    <select id="selectCustReceiveAddressPage" resultMap="custReceiveAddressResultMap">
        select * from chy_cust_receive_address where is_deleted = 0
    </select>

    <select id="exportCustReceiveAddress" resultType="org.springblade.modules.business.cust.excel.CustReceiveAddressExcel">
        SELECT * FROM chy_cust_receive_address ${ew.customSqlSegment}
    </select>

</mapper>
