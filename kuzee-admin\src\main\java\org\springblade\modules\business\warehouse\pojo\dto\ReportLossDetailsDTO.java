package org.springblade.modules.business.warehouse.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ReportLossDetailsDTO {
	//时间
	@Schema(description = "时间")
	private String reportLossTime;
	//供应商ID
	@Schema(description = "供应商ID")
	private Long supplierId;

	@Schema(description = "仓库ID")
	private Long warehouseId;

	@Schema(description = "商品sku")
	private Long productSkuId;
}
