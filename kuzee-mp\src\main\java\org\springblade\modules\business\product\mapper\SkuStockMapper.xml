<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.product.mapper.SkuStockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="skuStockResultMap" type="org.springblade.modules.business.product.pojo.entity.SkuStockEntity">
        <result column="product_id" property="productId"/>
        <result column="sku_code" property="skuCode"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="pic" property="pic"/>
        <result column="sale" property="sale"/>
        <result column="sp_data" property="spData"/>
        <result column="is_service_fee" property="isServiceFee"/>
        <result column="is_business" property="isBusiness"/>
        <result column="business_start_time" property="businessStartTime"/>
        <result column="business_end_time" property="businessEndTime"/>
        <result column="base_unit_id" property="baseUnitId"/>
        <result column="transport_unit_id" property="transportUnitId"/>
        <result column="transport_conversion_rate" property="transportConversionRate"/>
        <result column="package_unit_id" property="packageUnitId"/>
        <result column="package_gross_conversion_rate" property="packageGrossConversionRate"/>
        <result column="package_net_conversion_rate" property="packageNetConversionRate"/>
        <result column="sort" property="sort"/>
        <result column="is_contain_box" property="isContainBox"/>
        <result column="create_time_at" property="createTimeAt"/>
    </resultMap>

    <select id="selectSkuStockPage" resultMap="skuStockResultMap">
        select * from chy_sku_stock where is_deleted = 0
    </select>


    <select id="getIds" resultType="org.springblade.modules.business.product.pojo.entity.SkuStockEntity">
        select * from chy_sku_stock where   is_deleted = 0
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="pageAuditSkuUnselect" resultType="org.springblade.modules.business.product.pojo.vo.PageAuditSkuVO">
        SELECT s.*,p.name as productName
        FROM chy_sku_stock s
        left join chy_product p
            on p.id = s.product_id
        WHERE s.is_deleted = 0
          AND s.audit_status = 1
          AND NOT EXISTS (
            SELECT 1
            FROM chy_sku_warehouse_relation r
            WHERE r.sku_id = s.id
              AND r.warehouse_id = #{warehouseId}
              AND r.is_deleted = 0
              AND r.is_show = 1
        )
        <if test="selectCode != null and selectCode != ''">
            AND (s.sku_code LIKE CONCAT('%',#{selectCode},'%')  or p.product_code LIKE CONCAT('%',#{selectCode},'%') or p.name LIKE CONCAT('%',#{selectCode},'%') )
        </if>
    </select>

    <select id="pageAuditSkuSelect" resultType="org.springblade.modules.business.product.pojo.vo.PageAuditSkuVO">
        SELECT s.*,p.name as productName
        FROM chy_sku_stock s
        INNER JOIN chy_sku_warehouse_relation r
                ON s.id = r.sku_id
        left join chy_product p
                on p.id = s.product_id
        WHERE s.is_deleted = 0
          AND s.audit_status = 1
          AND r.warehouse_id = #{warehouseId}
          AND r.is_deleted = 0
        <if test="selectCode != null and selectCode != ''">
            AND (s.sku_code LIKE CONCAT('%',#{selectCode},'%')  or p.product_code LIKE CONCAT('%',#{selectCode},'%') or p.name LIKE CONCAT('%',#{selectCode},'%') )
        </if>
          AND r.is_show = 1
    </select>



</mapper>
