/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.handle.AmountTypeHandle;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 出入库记录配套运输表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@TableName(value = "chy_warehouse_support_trans", autoResultMap = true)
@Schema(description = "WarehouseStoreSupportEntity对象")
@EqualsAndHashCode(callSuper = true)
public class WarehouseSupportTransEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 仓库id
	 */
	@Schema(description = "仓库id")
	private Long warehouseId;

	@Schema(description = "商品SKU")
	private Long productSkuId;
	/**
	 * 供应商ID（关联供应商表）
	 */
	@Schema(description = "供应商ID（关联供应商表）")
	private Long supplierId;

	@Schema(description = "业务类型:1=入库 2=出库")
	private Integer bizType;

	/**
	 * 关联业务记录id
	 */
	@Schema(description = "关联业务记录id")
	private Long relatedRecordId;
	@Schema(description = "关联业务明细id")
	private Long relatedRecordItemId;
	/**
	 * 运输单位
	 */
	@Schema(description = "运输单位")
	private Long supportTransUnitId;

	/**
	 * 配套运输单价
	 */
	@Schema(description = "配套运输单价")
	@TableField(typeHandler = AmountTypeHandle.class)
	private BigDecimal supportTransPrice;

	/**
	 * 配套运输数量
	 */
	@Schema(description = "配套运输数量")
	private Integer supportTransNum;

	@Schema(description = "预期配套运输数量")
	private Integer expectSupportTransNum;

	@Schema(description = "是否允许删除(0-允许 1-不允许)")
	private Integer allowDeleted;
}
