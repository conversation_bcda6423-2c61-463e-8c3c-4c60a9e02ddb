/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.service.impl;

import org.springblade.modules.business.order.pojo.entity.SaleOrderItemSourceEntity;
import org.springblade.modules.business.order.pojo.vo.SaleOrderItemSourceVO;
import org.springblade.modules.business.order.excel.SaleOrderItemSourceExcel;
import org.springblade.modules.business.order.mapper.SaleOrderItemSourceMapper;
import org.springblade.modules.business.order.service.ISaleOrderItemSourceService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 销售订单详情原始数据表-支付成功时候的数据 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class SaleOrderItemSourceServiceImpl extends BaseServiceImpl<SaleOrderItemSourceMapper, SaleOrderItemSourceEntity> implements ISaleOrderItemSourceService {

	@Override
	public IPage<SaleOrderItemSourceVO> selectSaleOrderItemSourcePage(IPage<SaleOrderItemSourceVO> page, SaleOrderItemSourceVO saleOrderItemSource) {
		return page.setRecords(baseMapper.selectSaleOrderItemSourcePage(page, saleOrderItemSource));
	}

	@Override
	public List<SaleOrderItemSourceExcel> exportSaleOrderItemSource(Wrapper<SaleOrderItemSourceEntity> queryWrapper) {
		List<SaleOrderItemSourceExcel> saleOrderItemSourceList = baseMapper.exportSaleOrderItemSource(queryWrapper);
		//saleOrderItemSourceList.forEach(saleOrderItemSource -> {
		//	saleOrderItemSource.setTypeName(DictCache.getValue(DictEnum.YES_NO, SaleOrderItemSourceEntity.getType()));
		//});
		return saleOrderItemSourceList;
	}

}
