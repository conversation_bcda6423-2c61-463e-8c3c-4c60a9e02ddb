/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.service.impl;


import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.common.enums.BaseEnum;
import org.springblade.common.enums.StoreBizTypeEnum;
import org.springblade.common.enums.SupplierReconciliationStatusEnum;
import org.springblade.common.enums.SupplyWayEnum;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.*;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.excel.SupplierReconciliationExcel;
import org.springblade.modules.business.order.mapper.SupplierReconciliationMapper;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.product.pojo.entity.ProductEntity;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.warehouse.pojo.entity.*;
import org.springblade.modules.business.warehouse.service.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.common.utills.PriceCalculator;
import org.springframework.transaction.annotation.Transactional;
import org.springblade.common.enums.SupplierReconciliationRecordEnum;
import org.springblade.modules.business.product.pojo.entity.SkuForwardingChargesEntity;
import org.springblade.modules.business.product.service.ISkuForwardingChargesService;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springblade.common.enums.OutboundBizTypeEnum;
import org.springblade.modules.business.order.pojo.vo.SupplierReconciliationListVO;
import org.springblade.modules.business.order.pojo.vo.SupplierReconciliationPageVO;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.order.pojo.vo.RealtimeReconciliationPageVO;
import org.springblade.modules.business.order.pojo.vo.RealtimeReconciliationVO;
import org.springblade.modules.business.warehouse.mapper.WarehouseMapper;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.order.service.IOrderAfterSalesServiceItemMoneyService;
import org.springblade.modules.business.order.service.IOrderAfterSalesServiceItemService;
import org.springblade.modules.business.order.service.IOrderAfterSalesServiceItemSupplierService;
import org.springblade.core.mp.support.Query;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.CollectionUtils;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
@AllArgsConstructor
@Slf4j
public class SupplierReconciliationServiceImpl extends BaseServiceImpl<SupplierReconciliationMapper, SupplierReconciliationEntity> implements ISupplierReconciliationService {
	private final ISupplierService supplierService;
	private final IWarehouseStoreService warehouseStoreService;
	private final IWarehouseStoreItemService warehouseStoreItemService;
	private final ISupplierReconciliationRecordService supplierReconciliationRecordService;
	private final ISupplierReconciliationPaymentService supplierReconciliationPaymentService;
	private final ISupplierReconciliationItemService supplierReconciliationItemService;
	private final IWarehouseService warehouseService;
	private final ISkuStockService skuStockService;
	private final IOrderAfterSalesServiceService orderAfterSalesService;
	private final ITransportFeeService transportFeeService;
	private final IWarehouseSupportTransService warehouseSupportTransService;
	private final IReplenishmentOrderService replenishmentOrderService;
	private final ISkuForwardingChargesService skuForwardingChargesService;
	private final IReplenishmentOrderItemService replenishmentOrderItemService;
	private final IWarehouseOutboundService warehouseOutboundService;
	private final IWarehouseOutboundItemService warehouseOutboundItemService;
	private final WarehouseMapper warehouseMapper;
	private final IOrderAfterSalesServiceItemService orderAfterSalesServiceItemService;
	private final IOrderAfterSalesServiceItemSupplierService orderAfterSalesServiceItemSupplierService;
	private final IOrderAfterSalesServiceItemMoneyService orderAfterSalesServiceItemMoneyService;
	private final IProductService productService;

	/**
	 * 获取详情
	 *
	 * @param id z主键id
	 * @return SupplierReconciliationDetailsVO
	 */
	public SupplierReconciliationDetailsVO getDetails(Long id) {
		log.info("开始获取供应商对账单详情, id: {}", id);

		try {
			// 1. 获取对账单主表信息
			SupplierReconciliationEntity reconciliationEntity = Optional.ofNullable(getById(id))
				.orElseThrow(() -> new ServiceException("对账单不存在"));

			// 2. 获取供应商信息
			SupplierEntity supplier = Optional.ofNullable(supplierService.getById(reconciliationEntity.getSupplierId()))
				.orElseThrow(() -> new ServiceException("供应商信息不存在"));

			// 3. 获取对账单明细信息
			List<SupplierReconciliationItemEntity> itemList = getReconciliationItems(id);
			if (itemList.isEmpty()) {
				log.warn("对账单明细为空, id: {}", id);
			}

			// 4. 构建返回对象
			SupplierReconciliationDetailsVO detailsVO = new SupplierReconciliationDetailsVO();

			// 5. 设置基本信息
			setBasicInfo(detailsVO, reconciliationEntity, supplier);

			// 6. 设置金额相关信息
			setAmountInfo(detailsVO, reconciliationEntity);

			// 7. 处理明细信息
			processDetailItems(detailsVO, itemList);

			// 8. 处理发票记录
			processInvoiceRecord(detailsVO, reconciliationEntity);

			log.info("供应商对账单详情获取成功, id: {}", id);
			return detailsVO;

		} catch (ServiceException e) {
			log.error("获取供应商对账单详情失败: {}", e.getMessage());
			throw e;
		} catch (Exception e) {
			log.error("获取供应商对账单详情发生未知错误, id: {}", id, e);
			throw new ServiceException("获取对账单详情失败");
		}
	}

	/**
	 * 获取对账单明细列表
	 */
	private List<SupplierReconciliationItemEntity> getReconciliationItems(Long reconciliationId) {
		LambdaQueryWrapper<SupplierReconciliationItemEntity> itemWrapper = new LambdaQueryWrapper<>();
		itemWrapper.eq(SupplierReconciliationItemEntity::getReconciliationId, reconciliationId);
		return supplierReconciliationItemService.list(itemWrapper);
	}

	/**
	 * 设置基本信息
	 */
	private void setBasicInfo(SupplierReconciliationDetailsVO detailsVO,
							  SupplierReconciliationEntity reconciliation,
							  SupplierEntity supplier) {
		detailsVO.setId(reconciliation.getId());
		detailsVO.setCycleEndTime(reconciliation.getCycleEndTime());
		detailsVO.setCycleStartTime(reconciliation.getCycleStartTime());
		detailsVO.setSupplierId(reconciliation.getSupplierId());
		detailsVO.setStatus(reconciliation.getStatus());
		detailsVO.setStatusStr(SupplierReconciliationStatusEnum.getNameByCode(reconciliation.getStatus()));
		detailsVO.setSupplierName(supplier.getFullName());
		detailsVO.setPhone(supplier.getPhone());
		detailsVO.setCreatedTime(reconciliation.getCreateTime());
		detailsVO.setBankAddress(supplier.getAddress());
		// 如果只有一个账户信息显示一个，如果公账私账都有则显示公账
		if (Func.isAllEmpty(supplier.getPublicBankName(), supplier.getPublicBank(), supplier.getPublicBankNo())) {
			detailsVO.setBank(supplier.getPrivateBank());
			detailsVO.setBankName(supplier.getPrivateBankName());
			detailsVO.setBankNumber(supplier.getPrivateBankNo());
		} else {
			detailsVO.setBank(supplier.getPublicBank());
			detailsVO.setBankName(supplier.getPublicBankName());
			detailsVO.setBankNumber(supplier.getPublicBankNo());
		}
	}

	/**
	 * 设置金额相关信息
	 */
	private void setAmountInfo(SupplierReconciliationDetailsVO detailsVO,
							   SupplierReconciliationEntity reconciliation) {
		detailsVO.setFinalPayableAmount(Optional.ofNullable(reconciliation.getFinalPayableAmount())
			.orElse(BigDecimal.ZERO));
		detailsVO.setPaidAmount(Optional.ofNullable(reconciliation.getPaidAmount())
			.orElse(BigDecimal.ZERO));

		// 计算剩余应付金额
		BigDecimal finalPayable = Optional.ofNullable(reconciliation.getFinalPayableAmount())
			.orElse(BigDecimal.ZERO);
		BigDecimal paidAmount = Optional.ofNullable(reconciliation.getPaidAmount())
			.orElse(BigDecimal.ZERO);
		detailsVO.setRemainingPayableAmount(finalPayable.subtract(paidAmount));
	}

	/**
	 * 处理明细信息
	 */
	private void processDetailItems(SupplierReconciliationDetailsVO detailsVO,
									List<SupplierReconciliationItemEntity> itemList) {
		// 处理交易明细
		List<SupplierReconciliationTradeVO> tradeList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_TRADE.equals(item.getType()))
			.map(this::convertToTradeVO)
			.collect(Collectors.toList());
		detailsVO.setTradeList(tradeList);

		// 处理费用明细
		List<SupplierReconciliationFeeVO> feeList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_TRANSFER.equals(item.getType()))
			.map(this::convertToFeeVO)
			.collect(Collectors.toList());
		detailsVO.setFeeList(feeList);

		// 处理售后明细
		List<SupplierReconciliationAfterVO> afterList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_RETURN.equals(item.getType()))
			.map(this::convertToAfterVO)
			.collect(Collectors.toList());
		detailsVO.setAfterList(afterList);

		//处理报损
		List<SupplierReconciliationAfterVO> lossList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_LOSS.equals(item.getType()))
			.map(this::convertToAfterVO)
			.toList();
		detailsVO.setLossList(lossList);
	}

	/**
	 * 转换为交易VO
	 */
	private SupplierReconciliationTradeVO convertToTradeVO(SupplierReconciliationItemEntity item) {
		SupplierReconciliationTradeVO tradeVO = new SupplierReconciliationTradeVO();
		tradeVO.setId(item.getId());
		tradeVO.setSupplierId(item.getSupplierId());
		tradeVO.setThirdId(item.getThirdId());
		tradeVO.setStockOrderNumber(Func.isNotEmpty(item.getStockOrderNumber()) ? item.getStockOrderNumber() : item.getStoreNo());
		tradeVO.setTotalAmount(Optional.ofNullable(item.getTotalAmount()).orElse(BigDecimal.ZERO));
		tradeVO.setWarehousingTime(item.getWarehousingTime());
		tradeVO.setWarehouseId(item.getWarehouseId());
		tradeVO.setBizType(item.getBizType());
		tradeVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
		return tradeVO;
	}

	/**
	 * 转换为费用VO
	 */
	private SupplierReconciliationFeeVO convertToFeeVO(SupplierReconciliationItemEntity item) {
		SupplierReconciliationFeeVO feeVO = new SupplierReconciliationFeeVO();
		feeVO.setId(item.getId());
		feeVO.setSupplierId(item.getSupplierId());
		feeVO.setThirdId(item.getThirdId());
		feeVO.setStockOrderNumber(item.getStockOrderNumber());
		feeVO.setNumber(item.getNumber());
		feeVO.setWeight(item.getWeight());
		feeVO.setForwardingCharges(item.getForwardingCharges());
		feeVO.setWarehousingTime(item.getWarehousingTime());
		feeVO.setWarehouseId(item.getWarehouseId());
		feeVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
		return feeVO;
	}

	/**
	 * 转换为售后VO
	 */
	private SupplierReconciliationAfterVO convertToAfterVO(SupplierReconciliationItemEntity item) {
		SupplierReconciliationAfterVO afterVO = new SupplierReconciliationAfterVO();
		afterVO.setId(item.getId());
		afterVO.setSupplierId(item.getSupplierId());
		afterVO.setThirdId(item.getThirdId());
		afterVO.setWeight(item.getWeight());
		afterVO.setReturnAmount(Optional.ofNullable(item.getReturnAmount()).orElse(BigDecimal.ZERO));
		afterVO.setFinesTotal(Optional.ofNullable(item.getFinesTotal()).orElse(BigDecimal.ZERO));
		afterVO.setTotalAmount(Optional.ofNullable(item.getTotalAmount()).orElse(BigDecimal.ZERO));
		afterVO.setWarehousingTime(item.getWarehousingTime());
		afterVO.setWarehouseId(item.getWarehouseId());
		afterVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
		return afterVO;
	}

	/**
	 * 处理发票记录
	 */
	private void processInvoiceRecord(SupplierReconciliationDetailsVO detailsVO,
									  SupplierReconciliationEntity reconciliation) {
		List<SupplierReconciliationRecordEntity> records = supplierReconciliationRecordService.list(
			Wrappers.<SupplierReconciliationRecordEntity>lambdaQuery()
				.eq(SupplierReconciliationRecordEntity::getType, SupplierReconciliationRecordEnum.INVOICE_RECORD.getCode())
				.eq(SupplierReconciliationRecordEntity::getReconciliationId, reconciliation.getId())
			.orderByDesc(SupplierReconciliationRecordEntity::getId)
		);
		SupplierReconciliationRecordEntity reconciliationRecord = CollectionUtils.isEmpty(records) ? null : records.get(0);
		detailsVO.setCertificatePath(Func.isNull(reconciliationRecord) ? "" :
			reconciliationRecord.getCertificatePath());
	}

	/**
	 * 获取详情
	 *
	 * @param id z主键id
	 * @return SupplierReconciliationDetailsVO
	 */
	@Override
	public SupplierReconciliationInfoVO getInfo(Long id) {
		SupplierReconciliationInfoVO infoVO = new SupplierReconciliationInfoVO();
		infoVO.setDetailsVO(getDetails(id));

		infoVO.setRecordVO(getRecordsByType(id, SupplierReconciliationRecordEnum.INVOICE_RECORD.getCode(), false));
		infoVO.setFeeVO(getRecordsByType(id, SupplierReconciliationRecordEnum.INVOICE_RECORD.getCode(), true));

		return infoVO;
	}

	/**
	 * 根据类型获取对账记录
	 *
	 * @param id        对账单ID
	 * @param type      记录类型
	 * @param isInvoice 是否为发票记录
	 * @return 对账记录列表
	 */
	private List<SupplierReconciliationRecordVO> getRecordsByType(Long id, int type, boolean isInvoice) {
		LambdaQueryWrapper<SupplierReconciliationRecordEntity> wrapper = Wrappers.lambdaQuery(SupplierReconciliationRecordEntity.class)
			.eq(SupplierReconciliationRecordEntity::getReconciliationId, id)
			.eq(SupplierReconciliationRecordEntity::getStatus, BusinessConstant.ENABLE_STATUS);

		if (isInvoice) {
			wrapper.eq(SupplierReconciliationRecordEntity::getType, type);
		} else {
			wrapper.ne(SupplierReconciliationRecordEntity::getType, type);
		}
		List<SupplierReconciliationRecordEntity> entities = supplierReconciliationRecordService.list(wrapper);
		return entities.stream()
			.map(this::convertToRecordVO)
			.collect(Collectors.toList());
	}

	/**
	 * 将 SupplierReconciliationRecordEntity 转换为 SupplierReconciliationRecordVO
	 *
	 * @param entity 对账记录实体
	 * @return 对账记录视图对象
	 */
	private SupplierReconciliationRecordVO convertToRecordVO(SupplierReconciliationRecordEntity entity) {
		SupplierReconciliationRecordVO vo = new SupplierReconciliationRecordVO();
		vo.setReconciliationId(entity.getReconciliationId());
		vo.setCreateUser(entity.getCreateUser());
		vo.setCreateDept(entity.getCreateDept());
		vo.setType(entity.getType());
		vo.setCreateUserName(entity.getCreateUserName());
		vo.setCertificatePath(entity.getCertificatePath());
		vo.setPaymentAmount(entity.getPaymentAmount());
		vo.setCreateTime(entity.getCreateTime());
		vo.setTypeName(SupplierReconciliationRecordEnum.getNameByCode(entity.getType()));
		return vo;
	}

	@Override
	public IPage<SupplierReconciliationPageVO> selectSupplierReconciliationPage(IPage<SupplierReconciliationPageVO> page, SupplierReconciliationPageDTO supplierReconciliation) {
		if (supplierReconciliation.getIsWx() == 1) {
			//如果是小程序
			if (Func.isNull(supplierReconciliation.getUserId())) {
				supplierReconciliation.setUserId(AuthUtil.getUserId());
			}
		}
		IPage<SupplierReconciliationPageVO> pages = page.setRecords(baseMapper.selectSupplierReconciliationPage(page, supplierReconciliation));
		if (!pages.getRecords().isEmpty()) {
			pages.getRecords()
				.forEach(vo -> {
					vo.setStatusStr(SupplierReconciliationStatusEnum.getNameByCode(vo.getStatus()));
					vo.setSupplyAmount(CommonUtil.ConvertToBigDecimal(vo.getSupplyAmount()));
					vo.setFinalPayableAmount(CommonUtil.ConvertToBigDecimal(vo.getFinalPayableAmount()));
					vo.setPaidAmount(CommonUtil.ConvertToBigDecimal(vo.getPaidAmount()));
					vo.setRemainingPayableAmount(CommonUtil.ConvertToBigDecimal(vo.getRemainingPayableAmount()));
					vo.setReturnAmount(CommonUtil.ConvertToBigDecimal(vo.getReturnAmount()));
					vo.setPenaltyAmount(CommonUtil.ConvertToBigDecimal(vo.getPenaltyAmount()));
					vo.setFeeAmount(CommonUtil.ConvertToBigDecimal(vo.getFeeAmount()));
				});
		}
		return pages;
	}

	@Override
	public IPage<SupplierReconciliationListVO> selectReconciliationPageByQuery(IPage<SupplierReconciliationListVO> page, SupplierReconciliationQueryDTO queryDTO) {
		if (queryDTO.getIsWx() == 1) {
				queryDTO.setUserId(AuthUtil.getUserId());
		}
		IPage<SupplierReconciliationListVO> resultPage = page.setRecords(baseMapper.selectReconciliationPageByQuery(page, queryDTO));
		if (resultPage.getRecords().isEmpty()) {
			return resultPage;
		}
		resultPage.getRecords().forEach(vo -> {
			vo.setSupplyAmount(CommonUtil.ConvertToBigDecimal(vo.getSupplyAmount()));
			vo.setSettlementAmount(CommonUtil.ConvertToBigDecimal(vo.getSettlementAmount()));
			vo.setAfterDeduction(CommonUtil.ConvertToBigDecimal(vo.getAfterDeduction()));
			vo.setAfterFines(CommonUtil.ConvertToBigDecimal(vo.getAfterFines()));
			vo.setReportLossReturn(CommonUtil.ConvertToBigDecimal(vo.getReportLossReturn()));
			vo.setForwardingCharges(CommonUtil.ConvertToBigDecimal(vo.getForwardingCharges()));
			vo.setPayableAmount(CommonUtil.ConvertToBigDecimal(vo.getPayableAmount()));
			vo.setActualSettlementAmount(CommonUtil.ConvertToBigDecimal(vo.getActualSettlementAmount()));
			vo.setReturnOutboundAmount(CommonUtil.ConvertToBigDecimal(vo.getReturnOutboundAmount()));
			vo.setStatusStr(SupplierReconciliationStatusEnum.getNameByCode(vo.getStatus()));
			SupplierReconciliationEntity reconciliation = new SupplierReconciliationEntity();
			reconciliation.setId(vo.getId());
		});
		return resultPage;
	}

	/**
	 * 定时任务
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void job() {
		// 1. 判断今天有对账账期的供应商
		LocalDate nowDate = LocalDate.from(LocalDateTime.now().minusMinutes(10));
		Integer day = nowDate.getDayOfMonth();
		// 设置开始时间为上个月的下一天的00:00:00
		Date startDate = Date.from(nowDate.minusMonths(1).plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
		// 设置结束时间为当天的23:59:59
		Date endDate = Date.from(nowDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

		LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(SupplierEntity::getReconciliationDay, day);
		wrapper.eq(SupplierEntity::getStatus, BusinessConstant.ENABLE_STATUS);
		List<SupplierEntity> supplierList = supplierService.list(wrapper);

		if (supplierList.isEmpty()) {
			log.info("今天 {} 没有找到需要对账的供应商", nowDate);
			return;
		}
		log.info("找到 {} 个供应商的对账账期在今天", supplierList.size());
		List<Long> supplierIds = supplierList.stream().map(SupplierEntity::getId).toList();

		// --- 优化：批量数据预拉取 ---
		// 1. 批量获取已存在的对账单
		Map<Long, SupplierReconciliationEntity> existingReconciliationsMap = getExistingReconciliations(supplierIds, day);

		// 2. 批量获取所有供应商的入库、入库明细、SKU汇率
		List<WarehouseStoreEntity> allWarehouseStores = getWarehouseStoresForSuppliers(supplierIds, startDate, endDate);
		Map<Long, List<WarehouseStoreEntity>> storesBySupplier = allWarehouseStores.stream()
			.collect(Collectors.groupingBy(WarehouseStoreEntity::getRelatedSourceId));

		List<Long> allStoreIds = allWarehouseStores.stream().map(WarehouseStoreEntity::getId).toList();
		Map<Long, List<WarehouseStoreItemEntity>> itemsByStore = allStoreIds.isEmpty() ? Collections.emptyMap() :
			getWarehouseStoreItems(allStoreIds).stream()
				.collect(Collectors.groupingBy(WarehouseStoreItemEntity::getWarehouseStoreId));

		List<WarehouseStoreItemEntity> allItems = new ArrayList<>(itemsByStore.values()).stream().flatMap(Collection::stream).toList();
		List<Long> allSkuIds = allItems.stream().map(WarehouseStoreItemEntity::getProductSkuId).distinct().toList();
		Map<Long, SkuStockEntity> skuRateMap = allSkuIds.isEmpty() ? Collections.emptyMap() : skuStockService.getRateByIds(allSkuIds);

		// 新增：批量获取所有入库记录关联的配套运输流水
		Map<Long, List<WarehouseSupportTransEntity>> supportTransByStoreId = allStoreIds.isEmpty() ? Collections.emptyMap() :
			warehouseSupportTransService.list(Wrappers.<WarehouseSupportTransEntity>lambdaQuery()
					.in(WarehouseSupportTransEntity::getRelatedRecordId, allStoreIds)
					.eq(WarehouseSupportTransEntity::getBizType, 1) // 1=入库
				).stream()
				.collect(Collectors.groupingBy(WarehouseSupportTransEntity::getRelatedRecordId));

		// 3. 新业务逻辑：计算公司转运费
		// 3.1 获取所有相关的备货单号
		List<String> allRelatedOrderNos = allWarehouseStores.stream()
			.map(WarehouseStoreEntity::getRelatedOrderNo)
			.filter(Objects::nonNull).distinct().toList();

		// 3.2 批量查询备货单，并筛选出"公司转运"的备货单
		final Map<String, ReplenishmentOrderEntity> companyTransportOrdersMap;
		if (!allRelatedOrderNos.isEmpty()) {
			List<ReplenishmentOrderEntity> allReplenishmentOrders = replenishmentOrderService.list(
				Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
					.in(ReplenishmentOrderEntity::getReplenishmentNo, allRelatedOrderNos)
			);
			companyTransportOrdersMap = allReplenishmentOrders.stream()
				.filter(order -> SupplyWayEnum.PICKUP.getCode().equals(order.getSupplyWay()))
				.collect(Collectors.toMap(ReplenishmentOrderEntity::getReplenishmentNo, Function.identity(), (a, b) -> a));
		} else {
			companyTransportOrdersMap = Collections.emptyMap();
		}

		// 3.3 筛选出与"公司转运"备货单关联的入库记录, 并按供应商分组
		Map<Long, List<WarehouseStoreEntity>> companyTransportStoresBySupplier = allWarehouseStores.stream()
			.filter(store -> companyTransportOrdersMap.containsKey(store.getRelatedOrderNo()))
			.collect(Collectors.groupingBy(WarehouseStoreEntity::getRelatedSourceId));

		// 3.4 收集所有需要计算转运费的 SKU ID
		Set<Long> skusForForwardingCharge = new HashSet<>();
		companyTransportStoresBySupplier.values().stream()
			.flatMap(List::stream)
			.map(WarehouseStoreEntity::getId)
			.forEach(storeId -> {
				List<WarehouseStoreItemEntity> storeItems = itemsByStore.get(storeId);
				if (storeItems != null) {
					storeItems.forEach(item -> skusForForwardingCharge.add(item.getProductSkuId()));
				}
			});

		// 3.5 批量获取 SKU 的转运费配置
		Map<Long, SkuForwardingChargesEntity> forwardingChargesMap = new HashMap<>();
		if (!skusForForwardingCharge.isEmpty()) {
			List<SkuForwardingChargesEntity> charges = skuForwardingChargesService.list(
				Wrappers.<SkuForwardingChargesEntity>lambdaQuery()
					.in(SkuForwardingChargesEntity::getSkuId, skusForForwardingCharge)
			);
			forwardingChargesMap = charges.stream()
				.collect(Collectors.toMap(SkuForwardingChargesEntity::getSkuId, Function.identity(), (a, b) -> a));
		}

		// 4. 批量获取所有售后/罚款数据
		List<OrderAfterSalesByWarehouseListVO> allAfterSales = getOrderAfterSalesByWarehouseListForSuppliers(supplierIds, startDate, endDate);
		Map<Long, List<OrderAfterSalesByWarehouseListVO>> afterSalesBySupplier = allAfterSales.stream()
			.collect(Collectors.groupingBy(OrderAfterSalesByWarehouseListVO::getSupplierId));

		// 5. 批量获取所有报损数据
		Map<Long, List<WarehouseOutboundItemEntity>> reportLossBySupplier = getReportLossItemsForSuppliers(supplierIds, startDate, endDate);

		// --- 核心处理循环 ---
		List<SupplierReconciliationEntity> insertSupplierReconciliationList = new ArrayList<>();
		List<SupplierReconciliationItemEntity> insertSupplierReconciliationItemList = new ArrayList<>();

		for (SupplierEntity supplier : supplierList) {
			if (existingReconciliationsMap.containsKey(supplier.getId())) {
				log.info("供应商 ID {} 在今天 {} 已经存在对账记录", supplier.getId(), day);
				continue;
			}
			// 3. 新增各供应商的对账单主表
			SupplierReconciliationEntity reconciliation = createInitialReconciliation(supplier, endDate);
			log.info("为供应商 ID {} 创建新的对账单", supplier.getId());

			List<WarehouseStoreEntity> supplierStores = storesBySupplier.getOrDefault(supplier.getId(), Collections.emptyList());

			// 5. 处理入库信息 (使用预拉取数据)
			processWarehouseData(reconciliation, supplier, supplierStores, itemsByStore, supportTransByStoreId, insertSupplierReconciliationItemList);

			// 6. 处理退货和罚款信息 (使用预拉取数据)
			List<OrderAfterSalesByWarehouseListVO> supplierAfterSales = afterSalesBySupplier.getOrDefault(supplier.getId(), Collections.emptyList());
			if (!supplierAfterSales.isEmpty()) {
				processReturnsAndFines(reconciliation, supplierAfterSales, insertSupplierReconciliationItemList);
			}

			// 7. 新业务逻辑: 处理公司转运费
			processCompanyTransportFees(reconciliation,
				companyTransportStoresBySupplier.getOrDefault(supplier.getId(), Collections.emptyList()),
				itemsByStore,
				forwardingChargesMap,
				insertSupplierReconciliationItemList);
			// 8. 计算报损
			List<WarehouseOutboundItemEntity> supplierReportLosses = reportLossBySupplier.getOrDefault(supplier.getId(), Collections.emptyList());
			if (!supplierReportLosses.isEmpty()) {
				processReportLoss(reconciliation, supplierReportLosses, insertSupplierReconciliationItemList);
			}

			// 9. 计算最终应付金额
			calculateFinalAmount(reconciliation);
			reconciliation.setStatus(SupplierReconciliationStatusEnum.PENDING_RECONCILIATION.getCode());
			insertSupplierReconciliationList.add(reconciliation);
		}


		// 9. 保存供应商主表
		if (!insertSupplierReconciliationList.isEmpty()) {
			log.info("保存 {} 个供应商对账单", insertSupplierReconciliationList.size());
			this.updateBatchById(insertSupplierReconciliationList);
		}

		// 10. 保存供应商明细详情
		if (!insertSupplierReconciliationItemList.isEmpty()) {
			log.info("保存 {} 个供应商对账明细", insertSupplierReconciliationItemList.size());
			supplierReconciliationItemService.saveBatch(insertSupplierReconciliationItemList);
		}
	}


	/**
	 * 处理供应商的入库数据
	 *
	 * @param reconciliation 对账单实体
	 * @param supplier       供应商实体
	 * @param itemList       对账明细列表
	 */
	private void processWarehouseData(SupplierReconciliationEntity reconciliation,
									  SupplierEntity supplier,
									  List<WarehouseStoreEntity> supplierStores,
									  Map<Long, List<WarehouseStoreItemEntity>> itemsByStore,
									  Map<Long, List<WarehouseSupportTransEntity>> supportTransByStoreId,
									  List<SupplierReconciliationItemEntity> itemList) {
		if (supplierStores.isEmpty()) {
			reconciliation.setSupplyAmount(BigDecimal.ZERO);
			log.info("供应商 ID {} 没有找到入库信息", supplier.getId());
			return;
		}

		List<WarehouseStoreItemEntity> supplierItems = supplierStores.stream()
			.flatMap(store -> itemsByStore.getOrDefault(store.getId(), Collections.emptyList()).stream())
			.toList();

		if (supplierItems.isEmpty()) {
			reconciliation.setSupplyAmount(BigDecimal.ZERO);
			log.info("供应商 ID {} 没有找到入库详情", supplier.getId());
			return;
		}
		processWarehouseItems(reconciliation, supplierStores, supplierItems, itemList, supportTransByStoreId);
	}

	/**
	 * 处理供应商的退货和罚款数据
	 *
	 * @param reconciliation 对账单实体
	 * @param itemList       对账明细列表
	 */
	private void processReturnsAndFines(SupplierReconciliationEntity reconciliation,
										List<OrderAfterSalesByWarehouseListVO> orderAfterSalesList,
										List<SupplierReconciliationItemEntity> itemList) {
		for (OrderAfterSalesByWarehouseListVO orderAfterSales : orderAfterSalesList) {
			SupplierReconciliationItemEntity item = createReconciliationItemEntity(reconciliation, orderAfterSales);
			itemList.add(item);
			// 确保 BigDecimal 对象不为 null
			BigDecimal examineFinanceAmount = Optional.ofNullable(orderAfterSales.getExamineFinanceAmount()).orElse(BigDecimal.ZERO);
			BigDecimal examineFineAmount = Optional.ofNullable(orderAfterSales.getExamineFineAmount()).orElse(BigDecimal.ZERO);

			reconciliation.setReturnAmount(reconciliation.getReturnAmount().add(examineFinanceAmount));
			reconciliation.setPenaltyAmount(reconciliation.getPenaltyAmount().add(examineFineAmount));
			log.info("为供应商 ID {} 增加退货金额 {} 和罚款金额 {}",
				reconciliation.getSupplierId(),
				orderAfterSales.getExamineFinanceAmount(),
				orderAfterSales.getExamineFineAmount());
		}
	}

	private SupplierReconciliationItemEntity createReconciliationItemEntity(SupplierReconciliationEntity reconciliation, OrderAfterSalesByWarehouseListVO orderAfterSales) {
		SupplierReconciliationItemEntity item = new SupplierReconciliationItemEntity();
		item.setReconciliationId(reconciliation.getId());
		item.setSupplierId(reconciliation.getSupplierId());
		item.setThirdId(orderAfterSales.getId());
		item.setWeight(orderAfterSales.getExamineFinanceWeight());
		item.setNumber(orderAfterSales.getFinanceQuantity());
		item.setThirdId(orderAfterSales.getId());
		item.setSupplierId(reconciliation.getSupplierId());
		// 确保 BigDecimal 对象不为 null
		BigDecimal examineFinanceAmount = Optional.ofNullable(orderAfterSales.getExamineFinanceAmount()).orElse(BigDecimal.ZERO);
		BigDecimal examineFineAmount = Optional.ofNullable(orderAfterSales.getExamineFineAmount()).orElse(BigDecimal.ZERO);
		item.setWarehouseId(orderAfterSales.getWarehouseId());
		item.setWarehouseName(orderAfterSales.getWarehouseName());
		item.setReturnAmount(examineFinanceAmount);
		item.setFinesTotal(examineFineAmount);
		item.setTotalAmount(item.getReturnAmount().add(item.getFinesTotal()));
		item.setType(BusinessConstant.ORDER_TYPE_RETURN);
		return item;
	}

	/**
	 * 处理供应商的公司转运费数据
	 *
	 * @param reconciliation         对账单实体
	 * @param companyTransportStores "公司转运"的入库记录
	 * @param allItemsByStore        所有的入库明细项
	 * @param forwardingChargesMap   SKU转运费配置
	 * @param fullItemList           对账明细列表
	 */
	private void processCompanyTransportFees(SupplierReconciliationEntity reconciliation,
											 List<WarehouseStoreEntity> companyTransportStores,
											 Map<Long, List<WarehouseStoreItemEntity>> allItemsByStore,
											 Map<Long, SkuForwardingChargesEntity> forwardingChargesMap,
											 List<SupplierReconciliationItemEntity> fullItemList) {
		if (companyTransportStores.isEmpty()) {
			return;
		}

		BigDecimal totalFeeForSupplier = BigDecimal.ZERO;

		for (WarehouseStoreEntity store : companyTransportStores) {
			List<WarehouseStoreItemEntity> storeItems = allItemsByStore.get(store.getId());
			if (storeItems == null || storeItems.isEmpty()) {
				continue;
			}

			BigDecimal totalFeeForStore = calculateFeeForStore(storeItems, forwardingChargesMap);

			if (totalFeeForStore.compareTo(BigDecimal.ZERO) > 0) {
				// 创建对账明细
				SupplierReconciliationItemEntity feeItem = createFeeReconciliationItem(reconciliation, store, totalFeeForStore, storeItems);
				fullItemList.add(feeItem);
				totalFeeForSupplier = totalFeeForSupplier.add(totalFeeForStore);
			}
		}

		reconciliation.setFeeAmount(reconciliation.getFeeAmount().add(totalFeeForSupplier));
		if (totalFeeForSupplier.compareTo(BigDecimal.ZERO) > 0) {
			log.info("为供应商 ID {} 增加公司转运费 {}", reconciliation.getSupplierId(), totalFeeForSupplier);
		}
	}

	/**
	 * 为单个入库记录计算转运费总额
	 *
	 * @param storeItems           该入库记录下的所有明细
	 * @param forwardingChargesMap SKU转运费配置
	 * @return 该笔入库的转运费总额
	 */
	private BigDecimal calculateFeeForStore(List<WarehouseStoreItemEntity> storeItems, Map<Long, SkuForwardingChargesEntity> forwardingChargesMap) {
		// forwardingChargesMap 在新方法中会重新获取，所以这里用不上，可以直接调用新方法
		return calculateForwardingChargesForItems(storeItems);
	}

	/**
	 * 创建转运费的对账明细记录
	 *
	 * @param reconciliation 对账单
	 * @param store          入库记录
	 * @param totalFee       该笔入库的总转运费
	 * @param storeItems     该笔入库的所有明细项（用于聚合重量）
	 * @return 对账明细实体
	 */
	private SupplierReconciliationItemEntity createFeeReconciliationItem(SupplierReconciliationEntity reconciliation, WarehouseStoreEntity store, BigDecimal totalFee, List<WarehouseStoreItemEntity> storeItems) {
		SupplierReconciliationItemEntity item = new SupplierReconciliationItemEntity();
		item.setReconciliationId(reconciliation.getId());
		item.setSupplierId(reconciliation.getSupplierId());
		item.setThirdId(store.getId());
		item.setStockOrderNumber(store.getRelatedOrderNo());
		item.setForwardingCharges(totalFee);
		item.setType(BusinessConstant.ORDER_TYPE_TRANSFER);
		item.setWarehousingTime(DateUtil.fromDate(store.getCompletionTime()));
		item.setWarehouseId(store.getWarehouseId());
		item.setWarehouseName(warehouseService.getWarehouseName(store.getWarehouseId()));
		// 聚合重量
		BigDecimal totalWeight = storeItems.stream()
			.map(WarehouseStoreItemEntity::getActualWeight)
			.filter(Objects::nonNull)
			.reduce(BigDecimal.ZERO, BigDecimal::add);
		item.setWeight(totalWeight);
		return item;
	}

	/**
	 * 创建初始的对账单实体
	 *
	 * @param supplier 供应商实体
	 * @param date     当前日期
	 * @return 初始化后的对账单实体
	 */
	private SupplierReconciliationEntity createInitialReconciliation(SupplierEntity supplier, Date date) {
		SupplierReconciliationEntity entity = new SupplierReconciliationEntity();
		LocalDate nowDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		entity.setSupplierId(supplier.getId());
		entity.setCycleStartTime(nowDate.minusMonths(1).plusDays(1));
		entity.setCycleEndTime(nowDate);
		entity.setReconciliationDay(nowDate.getDayOfMonth());
		entity.setSupplyAmount(BigDecimal.ZERO);
		entity.setReturnAmount(BigDecimal.ZERO);
		entity.setPenaltyAmount(BigDecimal.ZERO);
		entity.setFeeAmount(BigDecimal.ZERO);
		entity.setPayableAmount(BigDecimal.ZERO);
		entity.setFinalPayableAmount(BigDecimal.ZERO);
		// 保存实体以生成主键 id
		this.save(entity);
		// 获取生成的主键 ID
		Long generatedId = entity.getId();
		log.info("为供应商 ID {} 创建新的对账单，生成的主键 ID 为 {}", supplier.getId(), generatedId);
		return entity;
	}

	/**
	 * 批量获取供应商的入库信息
	 */
	private List<WarehouseStoreEntity> getWarehouseStoresForSuppliers(List<Long> supplierIds, Date startDate, Date endDate) {
		LambdaQueryWrapper<WarehouseStoreEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.in(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.PURCHASE_IN.getCode(), StoreBizTypeEnum.TEMP_PURCHASE_IN.getCode())
			.in(WarehouseStoreEntity::getRelatedSourceId, supplierIds)
			.eq(WarehouseStoreEntity::getStatus, BusinessConstant.ENABLE_STATUS)
			.ge(WarehouseStoreEntity::getCompletionTime, startDate)
			.lt(WarehouseStoreEntity::getCompletionTime, endDate);

		return warehouseStoreService.list(wrapper);
	}

	/**
	 * 获取入库详情信息
	 *
	 * @param storeIds 入库ID列表
	 * @return 入库详情信息列表
	 */
	private List<WarehouseStoreItemEntity> getWarehouseStoreItems(List<Long> storeIds) {
		LambdaQueryWrapper<WarehouseStoreItemEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.in(WarehouseStoreItemEntity::getWarehouseStoreId, storeIds)
			.eq(WarehouseStoreItemEntity::getStatus, BusinessConstant.ENABLE_STATUS);
		return warehouseStoreItemService.list(wrapper);
	}

	/**
	 * 处理入库信息并更新对账单
	 *
	 * @param reconciliation         对账单实体
	 * @param warehouseStoreList     入库信息列表
	 * @param warehouseStoreItemList 入库详情信息列表
	 * @param itemList               对账明细列表
	 */
	private void processWarehouseItems(SupplierReconciliationEntity reconciliation,
									   List<WarehouseStoreEntity> warehouseStoreList,
									   List<WarehouseStoreItemEntity> warehouseStoreItemList,
									   List<SupplierReconciliationItemEntity> itemList,
									   Map<Long, List<WarehouseSupportTransEntity>> supportTransByStoreId) {
		// 优化：按 warehouseStoreId 对入库明细进行分组，避免在循环中过滤
		Map<Long, List<WarehouseStoreItemEntity>> itemsByStoreId = warehouseStoreItemList.stream()
			.collect(Collectors.groupingBy(WarehouseStoreItemEntity::getWarehouseStoreId));

		for (WarehouseStoreEntity warehouseStore : warehouseStoreList) {
			List<WarehouseStoreItemEntity> productItems = itemsByStoreId.get(warehouseStore.getId());
			List<WarehouseSupportTransEntity> supportTransItems = supportTransByStoreId.get(warehouseStore.getId());

			// 计算商品总金额
			BigDecimal productTotalAmount = calculateProductAmount(productItems);
			// 计算配套运输品总金额
			BigDecimal supportTransTotalAmount = calculateSupportTransAmount(supportTransItems);
			// 商品金额 + 配套运输品金额
			BigDecimal totalAmount = productTotalAmount.add(supportTransTotalAmount);

			if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
				SupplierReconciliationItemEntity item = createReconciliationItemEntity(reconciliation, warehouseStore, totalAmount);
				Set<Long> uniqueProductSkuIds = productItems != null ? productItems.stream()
					.map(WarehouseStoreItemEntity::getProductSkuId)
					.collect(Collectors.toSet()) : Collections.emptySet();

				item.setNumber(uniqueProductSkuIds.size());
				itemList.add(item);

				reconciliation.setSupplyAmount(reconciliation.getSupplyAmount().add(totalAmount));
				log.info("为供应商 ID {} 增加供应金额 {}", reconciliation.getSupplierId(), totalAmount);
			}
		}
	}

	private SupplierReconciliationItemEntity createReconciliationItemEntity(SupplierReconciliationEntity reconciliation, WarehouseStoreEntity warehouseStore, BigDecimal totalAmount) {
		SupplierReconciliationItemEntity item = new SupplierReconciliationItemEntity();
		item.setReconciliationId(reconciliation.getId());
		item.setSupplierId(reconciliation.getSupplierId());
		item.setThirdId(warehouseStore.getId());
		item.setStockOrderNumber(warehouseStore.getRelatedOrderNo());
		item.setStoreNo(warehouseStore.getStoreNo());
		item.setBizType(warehouseStore.getBizType());
		item.setWeight(BigDecimal.ZERO);
		item.setNumber(0);
		item.setTotalAmount(totalAmount);
		item.setWarehousingTime(DateUtil.fromDate(warehouseStore.getCompletionTime()));
		item.setWarehouseId(warehouseStore.getWarehouseId());
		item.setWarehouseName(warehouseService.getWarehouseName(warehouseStore.getWarehouseId()));
		item.setType(BusinessConstant.ORDER_TYPE_TRADE);
		item.setReturnAmount(BigDecimal.ZERO);
		item.setFinesTotal(BigDecimal.ZERO);
		return item;
	}

	/**
	 * 计算最终应付金额并更新对账单
	 *
	 * @param reconciliation 对账单实体
	 */
	private void calculateFinalAmount(SupplierReconciliationEntity reconciliation) {
		// 应付金额 = 结算金额 - 售后扣款 - 售后罚款 - 报损退货 - 转运费 - 退货出库金额
		BigDecimal payableAmount = reconciliation.getSettlementAmount()
			.subtract(reconciliation.getAfterDeduction())
			.subtract(reconciliation.getAfterFines())
			.subtract(reconciliation.getReportLossReturn())
			.subtract(reconciliation.getForwardingCharges())
			.subtract(reconciliation.getReturnOutboundAmount());
		reconciliation.setPayableAmount(payableAmount);
		reconciliation.setFinalPayableAmount(reconciliation.getPayableAmount());
		log.info("为供应商 ID {} 计算最终应付金额 {}", reconciliation.getSupplierId(), reconciliation.getPayableAmount());
	}

	/**
	 * 计算入库商品总金额
	 *
	 * @param items 入库详情信息列表
	 * @return 总金额
	 */
	private BigDecimal calculateProductAmount(List<WarehouseStoreItemEntity> items) {
		BigDecimal total = BigDecimal.ZERO;
		if (items == null || items.isEmpty()) {
			return total;
		}

		for (WarehouseStoreItemEntity item : items) {
			BigDecimal itemAmount = BigDecimal.ZERO;
			// 优先按重量计价
			BigDecimal unitPrice = item.getUnitPrice();
			BigDecimal actualWeight = item.getActualWeight();
			if (Func.notNull(unitPrice) && Func.notNull(actualWeight) &&
				unitPrice.compareTo(BigDecimal.ZERO) > 0 && actualWeight.compareTo(BigDecimal.ZERO) > 0) {
				itemAmount = actualWeight.multiply(unitPrice);
			} else {
				// 其次按整件计价
				BigDecimal wholePrice = item.getWholePrice();
				if (Func.notNull(wholePrice) && wholePrice.compareTo(BigDecimal.ZERO) > 0) {
					if (wholePrice != null && item.getActualQuantity() != null) {
						itemAmount = wholePrice.multiply(new BigDecimal(item.getActualQuantity()));
					}
				}
			}
			total = total.add(itemAmount);
		}

		return total.setScale(2, RoundingMode.HALF_UP);
	}

	/**
	 * 计算配套运输品总金额
	 *
	 * @param supportItems 配套运输品列表
	 * @return 总金额
	 */
	private BigDecimal calculateSupportTransAmount(List<WarehouseSupportTransEntity> supportItems) {
		if (supportItems == null || supportItems.isEmpty()) {
			return BigDecimal.ZERO;
		}
		BigDecimal total = BigDecimal.ZERO;
		for (WarehouseSupportTransEntity item : supportItems) {
			BigDecimal price = item.getSupportTransPrice();
			Integer quantity = item.getSupportTransNum();
			if (price != null && quantity != null && quantity > 0) {
				total = total.add(price.multiply(new BigDecimal(quantity)));
			}
		}
		return total.setScale(2, RoundingMode.HALF_UP);
	}

	/**
	 * 批量获取已存在的对账单
	 */
	private Map<Long, SupplierReconciliationEntity> getExistingReconciliations(List<Long> supplierIds, int day) {
		if (supplierIds.isEmpty()) {
			return Collections.emptyMap();
		}
		LambdaQueryWrapper<SupplierReconciliationEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.in(SupplierReconciliationEntity::getSupplierId, supplierIds)
			.eq(SupplierReconciliationEntity::getReconciliationDay, day);
		return this.list(wrapper).stream()
			.collect(Collectors.toMap(SupplierReconciliationEntity::getSupplierId, e -> e));
	}

	@Override
	public List<SupplierReconciliationExcel> exportSupplierReconciliation(Wrapper<SupplierReconciliationEntity> queryWrapper) {
		List<SupplierReconciliationExcel> supplierReconciliationList = baseMapper.exportSupplierReconciliation(queryWrapper);
		//supplierReconciliationList.forEach(supplierReconciliation -> {
		//	supplierReconciliation.setTypeName(DictCache.getValue(DictEnum.YES_NO, SupplierReconciliationEntity.getType()));
		//});
		return supplierReconciliationList;
	}

	/**
	 * 同步给供应商
	 *
	 * @param dto 查询条件
	 * @return Boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean synchronous(SupplierReconciliationSyncDTO dto) {
		SupplierReconciliationEntity supplierReconciliation = baseMapper.selectOne(Wrappers.<SupplierReconciliationEntity>lambdaQuery()
			.eq(SupplierReconciliationEntity::getId, dto.getId())
		);
		supplierReconciliation.setFinalPayableAmount(dto.getFinalPayableAmount());
		supplierReconciliation.setStatus(SupplierReconciliationStatusEnum.PENDING_CONFIRMATION.getCode());
		supplierReconciliation.setChangeNote(dto.getChangeNote());
		//添加记录
		SupplierReconciliationRecordEntity warehouseStoreRecordEntity = new SupplierReconciliationRecordEntity();
		warehouseStoreRecordEntity.setReconciliationId(supplierReconciliation.getId());
		warehouseStoreRecordEntity.setType(SupplierReconciliationRecordEnum.SYNCHRONOUS_RECONCILIATION.getCode());
		warehouseStoreRecordEntity.setPaymentAmount(dto.getFinalPayableAmount());
		warehouseStoreRecordEntity.setCreateUserName(AuthUtil.getNickName());
		warehouseStoreRecordEntity.setChangeNote(dto.getChangeNote());
		supplierReconciliationRecordService.save(warehouseStoreRecordEntity);
		return updateById(supplierReconciliation);
	}

	/**
	 * 小程序-确认对账
	 *
	 * @param id 供应商主键id
	 * @return Boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean confirmReconciliation(Long id) {
		SupplierReconciliationEntity supplierReconciliation = baseMapper.selectOne(Wrappers.<SupplierReconciliationEntity>lambdaQuery()
			.eq(SupplierReconciliationEntity::getId, id)
			.eq(SupplierReconciliationEntity::getStatus, SupplierReconciliationStatusEnum.PENDING_CONFIRMATION.getCode())
		);
		if (Func.isNull(supplierReconciliation)) {
			throw new ServiceException("未找到该对账单！");
		}
		supplierReconciliation.setStatus(SupplierReconciliationStatusEnum.PENDING_INVOICING.getCode());
		updateById(supplierReconciliation);
		//添加记录
		SupplierReconciliationRecordEntity warehouseStoreRecordEntity = new SupplierReconciliationRecordEntity();
		SupplierEntity serviceRecordEntity = supplierService.getSupplier();
		warehouseStoreRecordEntity.setCreateUserName(serviceRecordEntity.getFullName());
		warehouseStoreRecordEntity.setReconciliationId(supplierReconciliation.getId());
		warehouseStoreRecordEntity.setType(SupplierReconciliationRecordEnum.CONFIRM_RECONCILIATION.getCode());
		return supplierReconciliationRecordService.save(warehouseStoreRecordEntity);
	}

	/**
	 * 上传发票
	 *
	 * @param id              主键id
	 * @param certificatePath 图片路径
	 * @return Boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean uploadInvoice(Long id, String certificatePath) {
		SupplierReconciliationEntity supplierReconciliation = baseMapper.selectOne(Wrappers.<SupplierReconciliationEntity>lambdaQuery()
			.eq(SupplierReconciliationEntity::getId, id)
			.in(SupplierReconciliationEntity::getStatus, SupplierReconciliationStatusEnum.PENDING_INVOICING.getCode(), SupplierReconciliationStatusEnum.PENDING_RETRANSMISSION.getCode())
		);
		if (Func.isNull(supplierReconciliation)) {
			throw new ServiceException("未找到该对账单！");
		}
		supplierReconciliation.setStatus(SupplierReconciliationStatusEnum.INVOICED.getCode());
		updateById(supplierReconciliation);
		//添加记录
		SupplierReconciliationRecordEntity warehouseStoreRecordEntity = new SupplierReconciliationRecordEntity();
		SupplierEntity serviceRecordEntity = supplierService.getSupplier();
		warehouseStoreRecordEntity.setCreateUserName(serviceRecordEntity.getFullName());
		warehouseStoreRecordEntity.setReconciliationId(supplierReconciliation.getId());
		warehouseStoreRecordEntity.setType(SupplierReconciliationRecordEnum.INVOICE_RECORD.getCode());
		warehouseStoreRecordEntity.setCertificatePath(certificatePath);
		return supplierReconciliationRecordService.save(warehouseStoreRecordEntity);
	}

	/**
	 * 驳回
	 *
	 * @param id 主键id
	 * @return Boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean reject(Long id) {
		SupplierReconciliationEntity supplierReconciliation = baseMapper.selectOne(Wrappers.<SupplierReconciliationEntity>lambdaQuery()
			.eq(SupplierReconciliationEntity::getId, id)
		);
		supplierReconciliation.setStatus(SupplierReconciliationStatusEnum.PENDING_RETRANSMISSION.getCode());
		updateById(supplierReconciliation);
		//添加记录
		SupplierReconciliationRecordEntity warehouseStoreRecordEntity = new SupplierReconciliationRecordEntity();
		warehouseStoreRecordEntity.setCreateUserName(AuthUtil.getNickName());
		warehouseStoreRecordEntity.setReconciliationId(supplierReconciliation.getId());
		warehouseStoreRecordEntity.setType(SupplierReconciliationRecordEnum.REJECT_RECORD.getCode());
		supplierReconciliationRecordService.save(warehouseStoreRecordEntity);
		return updateById(supplierReconciliation);
	}

	/**
	 * 供应商打款
	 *
	 * @param dto 请求参数
	 * @return Boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean makePayment(SupplierReconciliationMakeDTO dto) {
		SupplierReconciliationEntity supplierReconciliation = baseMapper.selectOne(Wrappers.<SupplierReconciliationEntity>lambdaQuery()
			.eq(SupplierReconciliationEntity::getId, dto.getReconciliationId())
		);
		supplierReconciliation.setStatus(SupplierReconciliationStatusEnum.COMPLETED.getCode());
		updateById(supplierReconciliation);
		//添加打款日志
		SupplierReconciliationPaymentEntity supplierReconciliationPaymentEntity = new SupplierReconciliationPaymentEntity();
		supplierReconciliationPaymentEntity.setSupplierId(dto.getSupplierId());
		supplierReconciliationPaymentEntity.setPaymentAmount(dto.getPaymentAmount());
		supplierReconciliationPaymentEntity.setPaymentTime(dto.getPaymentTime());
		supplierReconciliationPaymentEntity.setPaymentVoucher(dto.getPaymentVoucher());
		supplierReconciliationPaymentEntity.setReconciliationId(dto.getReconciliationId());
		supplierReconciliationPaymentService.save(supplierReconciliationPaymentEntity);
		//添加记录
		SupplierReconciliationRecordEntity warehouseStoreRecordEntity = new SupplierReconciliationRecordEntity();
		warehouseStoreRecordEntity.setReconciliationId(dto.getReconciliationId());
		warehouseStoreRecordEntity.setType(SupplierReconciliationRecordEnum.PAYMENT_RECORD.getCode());
		warehouseStoreRecordEntity.setPaymentAmount(dto.getPaymentAmount());
		warehouseStoreRecordEntity.setCreateUserName(AuthUtil.getNickName());
		warehouseStoreRecordEntity.setCertificatePath(dto.getPaymentVoucher());
		return supplierReconciliationRecordService.save(warehouseStoreRecordEntity);
	}

	private List<OrderAfterSalesByWarehouseListVO> getOrderAfterSalesByWarehouseListForSuppliers(List<Long> supplierIds, Date startDate, Date endDate) {
		return orderAfterSalesService.getOrderAfterSalesByWarehouseListForSuppliers(supplierIds, startDate, endDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void generateReconciliation(SupplierReconciliationGenerateDTO dto) {
		if (Func.isEmpty(dto.getSupplierIds()) || Func.isEmpty(dto.getStartTime()) || Func.isEmpty(dto.getEndTime())) {
			throw new ServiceException("参数不能为空");
		}
		List<SupplierEntity> suppliers = supplierService.listByIds(dto.getSupplierIds());
		for (SupplierEntity supplier : suppliers) {
			// 查找该供应商最新的对账单，以确定新的开始日期
			SupplierReconciliationEntity lastReconciliation = this.getOne(
				Wrappers.<SupplierReconciliationEntity>lambdaQuery()
					.eq(SupplierReconciliationEntity::getSupplierId, supplier.getId())
					.orderByDesc(SupplierReconciliationEntity::getCycleEndTime)
					.last("LIMIT 1")
			);

			LocalDate effectiveStartTime = dto.getStartTime();
			if (lastReconciliation != null && lastReconciliation.getCycleEndTime() != null) {
				LocalDate nextDayToReconcile = lastReconciliation.getCycleEndTime().plusDays(1);
				// 新的开始时间应该是 "上次对账结束日期的后一天" 和 "本次请求的开始时间" 中的较晚者。
				if (dto.getStartTime().isBefore(nextDayToReconcile)) {
					effectiveStartTime = nextDayToReconcile;
				}
			}

			// 如果计算出的有效开始时间晚于请求的结束时间，则说明没有需要对账的新的时间段
			if (effectiveStartTime.isAfter(dto.getEndTime())) {
				log.warn("供应商ID {} 已在 {} 或之后对过账，本次请求的对账范围 {} 到 {} 无需生成新账单。",
					supplier.getId(), effectiveStartTime, dto.getStartTime(), dto.getEndTime());
				continue; // 处理下一个供应商
			}

			// 1. 创建主对账单
			SupplierReconciliationEntity reconciliation = new SupplierReconciliationEntity();
			reconciliation.setSupplierId(supplier.getId());
			reconciliation.setCycleStartTime(effectiveStartTime);
			reconciliation.setCycleEndTime(dto.getEndTime());
			String statementNo = "SS" + DateUtil.format(new Date(), "yyyyMMdd") + supplier.getId();
			reconciliation.setStatementNo(statementNo);
			reconciliation.setStatus(SupplierReconciliationStatusEnum.PENDING_RECONCILIATION.getCode());

			// 先保存主表以获取ID
			this.save(reconciliation);

			List<SupplierReconciliationItemEntity> itemsToSave = new ArrayList<>();

			// 从所有相关业务中获取仓库ID
			List<Long> replenishmentWarehouseIds = findWarehousesForSupplier(supplier.getId(), effectiveStartTime, dto.getEndTime());
			List<Long> storeWarehouseIds = findWarehousesFromStores(supplier.getId(), effectiveStartTime, dto.getEndTime());
			List<Long> afterSalesWarehouseIds = findWarehousesForAfterSales(supplier.getId(), effectiveStartTime, dto.getEndTime());
			List<Long> reportLossWarehouseIds = findWarehousesFromReportLoss(supplier.getId(), effectiveStartTime, dto.getEndTime());

			// 合并并去重所有相关的仓库
			Set<Long> allWarehouseIdsSet = new HashSet<>();
			allWarehouseIdsSet.addAll(replenishmentWarehouseIds);
			allWarehouseIdsSet.addAll(storeWarehouseIds);
			allWarehouseIdsSet.addAll(afterSalesWarehouseIds);
			allWarehouseIdsSet.addAll(reportLossWarehouseIds);


			if (allWarehouseIdsSet.isEmpty()) {
				log.warn("供应商ID {} 在指定时间段内没有任何活动的仓库，跳过生成对账明细。", supplier.getId());
				// 即使没有明细，也要确保主表金额被正确初始化为0并更新
				aggregateDailyItemsToMain(reconciliation, Collections.emptyList());
				this.updateById(reconciliation);
				log.info("为供应商 ID {} 生成空的对账单 {} 成功", supplier.getId(), statementNo);
				continue;
			}
			List<Long> allWarehouseIds = new ArrayList<>(allWarehouseIdsSet);
			List<WarehouseEntity> warehouses = warehouseService.listByIds(allWarehouseIds);
			Map<Long, WarehouseEntity> warehouseMap = warehouses.stream().collect(Collectors.toMap(WarehouseEntity::getId, Function.identity(), (a, b) -> a));


			// 2. 按仓库和天循环生成明细
			for (Long warehouseId : allWarehouseIds) {
				// 防御性编程，如果warehouse因为某些原因（如被禁用）查不出来，就跳过
				if (!warehouseMap.containsKey(warehouseId)) {
					continue;
				}
				WarehouseEntity warehouse = warehouseMap.get(warehouseId);
				for (LocalDate date = effectiveStartTime; !date.isAfter(dto.getEndTime()); date = date.plusDays(1)) {
					SupplierReconciliationItemEntity dailyItem = createDailyItem(reconciliation.getId(), supplier.getId(), warehouse, date);

					// 计算每日、每仓库的各项金额
					calculateDailyAmounts(dailyItem, supplier.getId(), warehouse.getId(), date);
					// 无论金额是否为0，都添加入库，以保证数据完整性
					itemsToSave.add(dailyItem);
				}
			}


			// 3. 聚合日明细到主表
			aggregateDailyItemsToMain(reconciliation, itemsToSave);


			// 4. 更新主表和批量保存子表
			this.updateById(reconciliation);
			if (!itemsToSave.isEmpty()) {
				supplierReconciliationItemService.saveBatch(itemsToSave);
			}
			log.info("为供应商 ID {} 生成对账单 {} 成功", supplier.getId(), statementNo);
		}
	}

	/**
	 * 查找供应商历史上所有合作过的仓库ID列表
	 */
	private List<Long> findAllWarehousesForSupplier(Long supplierId) {
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.select(ReplenishmentOrderEntity::getWarehouseId)
				.eq(ReplenishmentOrderEntity::getSupplierId, supplierId)
				.groupBy(ReplenishmentOrderEntity::getWarehouseId)
		);
		return orders.stream().map(ReplenishmentOrderEntity::getWarehouseId).distinct().toList();
	}

	/**
	 * 查找供应商在指定时间段内有备货记录的仓库ID列表
	 */
	private List<Long> findWarehousesForSupplier(Long supplierId, LocalDate startTime, LocalDate endTime) {
		LocalDateTime startDateTime = startTime.atStartOfDay();
		LocalDateTime endDateTime = endTime.atTime(23, 59, 59);

		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.select(ReplenishmentOrderEntity::getWarehouseId)
				.eq(ReplenishmentOrderEntity::getSupplierId, supplierId)
				.between(ReplenishmentOrderEntity::getCreateTime, startDateTime, endDateTime)
				.groupBy(ReplenishmentOrderEntity::getWarehouseId)
		);
		return orders.stream().map(ReplenishmentOrderEntity::getWarehouseId).distinct().toList();
	}

	/**
	 * 查找供应商在指定时间段内有入库记录的仓库ID列表
	 */
	private List<Long> findWarehousesFromStores(Long supplierId, LocalDate startTime, LocalDate endTime) {
		LocalDateTime startDateTime = startTime.atStartOfDay();
		LocalDateTime endDateTime = endTime.atTime(23, 59, 59);

		List<WarehouseStoreItemEntity> storeItems = warehouseStoreItemService.list(
			Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
				.select(WarehouseStoreItemEntity::getWarehouseId)
				.eq(WarehouseStoreItemEntity::getSupplierId, supplierId)
				.between(WarehouseStoreItemEntity::getCreateTime, startDateTime, endDateTime)
				.groupBy(WarehouseStoreItemEntity::getWarehouseId)
		);
		return storeItems.stream().map(WarehouseStoreItemEntity::getWarehouseId).distinct().toList();
	}

	/**
	 * 查找供应商在指定时间段内有报损记录的仓库ID列表
	 */
	private List<Long> findWarehousesFromReportLoss(Long supplierId, LocalDate startTime, LocalDate endTime) {
		LocalDateTime startDateTime = startTime.atStartOfDay();
		LocalDateTime endDateTime = endTime.atTime(23, 59, 59);

		// 1. 找出指定时间范围内的所有差异报损出库主单
		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.select(WarehouseOutboundEntity::getId)
				.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode())
				.between(WarehouseOutboundEntity::getCompletionTime, startDateTime, endDateTime)
		);
		if (outbounds.isEmpty()) {
			return Collections.emptyList();
		}
		List<Long> outboundIds = outbounds.stream().map(WarehouseOutboundEntity::getId).toList();

		// 2. 根据主单ID和供应商ID，从出库明细中找出相关的仓库ID
		List<WarehouseOutboundItemEntity> items = warehouseOutboundItemService.list(
			Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
				.select(WarehouseOutboundItemEntity::getWarehouseId)
				.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
				.eq(WarehouseOutboundItemEntity::getSupplierId, supplierId)
				.groupBy(WarehouseOutboundItemEntity::getWarehouseId)
		);

		return items.stream().map(WarehouseOutboundItemEntity::getWarehouseId).distinct().toList();
	}

	private List<Long> findWarehousesForAfterSales(Long supplierId, LocalDate startTime, LocalDate endTime) {
		LocalDateTime startDateTime = startTime.atStartOfDay();
		LocalDateTime endDateTime = endTime.atTime(23, 59, 59);

		// 1. Find all after-sales item IDs related to the supplier from both deduction and fine tables
		List<OrderAfterSalesServiceItemSupplierEntity> supplierDeductions = orderAfterSalesServiceItemSupplierService.list(
			Wrappers.<OrderAfterSalesServiceItemSupplierEntity>lambdaQuery()
				.select(OrderAfterSalesServiceItemSupplierEntity::getAfterSalesItemId)
				.eq(OrderAfterSalesServiceItemSupplierEntity::getSupplierId, supplierId)
		);
		List<OrderAfterSalesServiceItemMoneyEntity> supplierFines = orderAfterSalesServiceItemMoneyService.list(
			Wrappers.<OrderAfterSalesServiceItemMoneyEntity>lambdaQuery()
				.select(OrderAfterSalesServiceItemMoneyEntity::getAfterSalesItemId)
				.eq(OrderAfterSalesServiceItemMoneyEntity::getSupplierId, supplierId)
		);

		Set<Long> itemIds = new HashSet<>();
		supplierDeductions.forEach(item -> itemIds.add(item.getAfterSalesItemId()));
		supplierFines.forEach(fine -> itemIds.add(fine.getAfterSalesItemId()));

		if (itemIds.isEmpty()) {
			return Collections.emptyList();
		}

		// 2. Find all after-sales main IDs from the items
		List<OrderAfterSalesServiceItemEntity> serviceItems = orderAfterSalesServiceItemService.list(
			Wrappers.<OrderAfterSalesServiceItemEntity>lambdaQuery()
				.select(OrderAfterSalesServiceItemEntity::getAfterSalesId)
				.in(OrderAfterSalesServiceItemEntity::getId, itemIds)
		);
		if (serviceItems.isEmpty()) {
			return Collections.emptyList();
		}
		List<Long> serviceIds = serviceItems.stream()
			.map(OrderAfterSalesServiceItemEntity::getAfterSalesId)
			.distinct()
			.toList();

		// 3. Find all warehouse IDs from the main after-sales services within the date range
		List<OrderAfterSalesServiceEntity> services = orderAfterSalesService.list(
			Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery()
				.select(OrderAfterSalesServiceEntity::getWarehouseId)
				.in(OrderAfterSalesServiceEntity::getId, serviceIds)
				.between(OrderAfterSalesServiceEntity::getCreateTime, startDateTime, endDateTime)
				.groupBy(OrderAfterSalesServiceEntity::getWarehouseId)
		);

		return services.stream().map(OrderAfterSalesServiceEntity::getWarehouseId).distinct().toList();
	}

	private SupplierReconciliationItemEntity createDailyItem(Long reconciliationId, Long supplierId, LocalDate date) {
		SupplierReconciliationItemEntity dailyItem = new SupplierReconciliationItemEntity();
		dailyItem.setReconciliationId(reconciliationId);
		dailyItem.setSupplierId(supplierId);
		dailyItem.setReconciliationTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		// 初始化所有金额字段
		dailyItem.setSupplyAmount(BigDecimal.ZERO);
		dailyItem.setSettlementAmount(BigDecimal.ZERO);
		dailyItem.setAfterDeduction(BigDecimal.ZERO);
		dailyItem.setAfterFines(BigDecimal.ZERO);
		dailyItem.setReportLossReturn(BigDecimal.ZERO);
		dailyItem.setForwardingCharges(BigDecimal.ZERO);
		return dailyItem;
	}

	private SupplierReconciliationItemEntity createDailyItem(Long reconciliationId, Long supplierId, WarehouseEntity warehouse, LocalDate date) {
		SupplierReconciliationItemEntity dailyItem = createDailyItem(reconciliationId, supplierId, date);
		if (warehouse != null) {
			dailyItem.setWarehouseId(warehouse.getId());
			dailyItem.setWarehouseName(warehouse.getWarehouseName());
		}
		return dailyItem;
	}


	private void calculateDailyAmounts(SupplierReconciliationItemEntity dailyItem, Long supplierId, Long warehouseId, LocalDate date) {
		// a. 供货金额
		dailyItem.setSupplyAmount(getDailySupplyAmount(supplierId, warehouseId, date));

		// 获取当日当仓入库记录
		List<WarehouseStoreEntity> dailyStores = getDailyWarehouseStores(supplierId, warehouseId, date);
		List<WarehouseStoreItemEntity> dailyStoreItems;
		if (!dailyStores.isEmpty()) {
			List<Long> storeIds = dailyStores.stream().map(WarehouseStoreEntity::getId).toList();
			dailyStoreItems = getWarehouseStoreItems(storeIds);
		} else {
			dailyStoreItems = Collections.emptyList();
		}

		// b. 结算金额
		dailyItem.setSettlementAmount(calculateDailySettlementAmount(dailyStoreItems));

		// c. 售后扣款 & d. 售后罚款
		AfterSalesAmounts dailyAfterSales = getDailyAfterSalesAmounts(supplierId, warehouseId, date);
		dailyItem.setAfterDeduction(dailyAfterSales.getDeduction());
		dailyItem.setAfterFines(dailyAfterSales.getFines());

		// e. 报损退货
		BigDecimal reportLossReturn = getDailyReportLossReturnAmount(supplierId, warehouseId, date);
		reportLossReturn = Optional.ofNullable(reportLossReturn).orElse(BigDecimal.ZERO);
		dailyItem.setReportLossReturn(reportLossReturn.abs());

		// f. 转运费
		dailyItem.setForwardingCharges(calculateDailyForwardingCharges(dailyStores, dailyStoreItems));
		dailyItem.setReturnOutboundAmount(getDailyReturnOutboundAmount(supplierId, warehouseId, date));
	}

	/**
	 * 计算每日供货金额
	 * 来源: 备货单 (ReplenishmentOrder)
	 * 逻辑: SUM(备货单明细.quantity * 备货单明细.unitPrice)
	 */
	private BigDecimal getDailySupplyAmount(Long supplierId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 查询当天的备货单主表
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.eq(ReplenishmentOrderEntity::getSupplierId, supplierId)
				.between(ReplenishmentOrderEntity::getCreateTime, startOfDay, endOfDay)
		);

		if (orders.isEmpty()) {
			return BigDecimal.ZERO;
		}
		List<Long> orderIds = orders.stream().map(ReplenishmentOrderEntity::getId).toList();

		// 2. 查询对应的所有备货单明细
		List<ReplenishmentOrderItemEntity> items = replenishmentOrderItemService.list(
			Wrappers.<ReplenishmentOrderItemEntity>lambdaQuery()
				.in(ReplenishmentOrderItemEntity::getReplenishmentOrderId, orderIds)
		);

		if (items.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 3. 聚合计算总金额
		BigDecimal totalAmount = BigDecimal.ZERO;
		for (ReplenishmentOrderItemEntity item : items) {
			BigDecimal quantity = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
			BigDecimal unitPrice = Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO);

			if (quantity.compareTo(BigDecimal.ZERO) > 0 && unitPrice.compareTo(BigDecimal.ZERO) > 0) {
				totalAmount = totalAmount.add(quantity.multiply(unitPrice));
			}
		}
		return totalAmount.setScale(2, RoundingMode.HALF_UP);
	}

	private BigDecimal getDailySupplyAmount(Long supplierId, Long warehouseId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 查询当天的备货单主表
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.eq(ReplenishmentOrderEntity::getSupplierId, supplierId)
				.eq(ReplenishmentOrderEntity::getWarehouseId, warehouseId)
				.between(ReplenishmentOrderEntity::getCreateTime, startOfDay, endOfDay)
		);

		if (orders.isEmpty()) {
			return BigDecimal.ZERO;
		}
		List<Long> orderIds = orders.stream().map(ReplenishmentOrderEntity::getId).toList();

		// 2. 查询对应的所有备货单明细
		List<ReplenishmentOrderItemEntity> items = replenishmentOrderItemService.list(
			Wrappers.<ReplenishmentOrderItemEntity>lambdaQuery()
				.in(ReplenishmentOrderItemEntity::getReplenishmentOrderId, orderIds)
		);

		if (items.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 3. 聚合计算总金额
		BigDecimal totalAmount = BigDecimal.ZERO;
		for (ReplenishmentOrderItemEntity item : items) {
			// 商品金额：单价 * 重量
			BigDecimal unitPrice = Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO);
			BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
			BigDecimal productAmount = unitPrice.multiply(weight);

			// 配套运输金额：数量 * 单价
			Integer supportNum = Optional.ofNullable(item.getSupportTransNum()).orElse(0);
			BigDecimal supportPrice = Optional.ofNullable(item.getSupportTransPrice()).orElse(BigDecimal.ZERO);
			BigDecimal supportAmount = BigDecimal.ZERO;
			if (supportNum > 0 && !supportPrice.equals(BigDecimal.ZERO)) {
				supportAmount = new BigDecimal(supportNum)
					.multiply(supportPrice);
			}

			totalAmount = totalAmount.add(productAmount).add(supportAmount);

		}
		return totalAmount.setScale(2, RoundingMode.HALF_UP);
	}

	/**
	 * 获取每日的入库记录
	 */
	private List<WarehouseStoreEntity> getDailyWarehouseStores(Long supplierId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);
		Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

		LambdaQueryWrapper<WarehouseStoreEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.in(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.PURCHASE_IN.getCode(), StoreBizTypeEnum.TEMP_PURCHASE_IN.getCode())
			.eq(WarehouseStoreEntity::getRelatedSourceId, supplierId) // relatedSourceId 是供应商ID
			.eq(WarehouseStoreEntity::getStatus, BusinessConstant.ENABLE_STATUS)
			.between(WarehouseStoreEntity::getCompletionTime, startDate, endDate);

		return warehouseStoreService.list(wrapper);
	}

	private List<WarehouseStoreEntity> getDailyWarehouseStores(Long supplierId, Long warehouseId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);
		Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
		Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

		LambdaQueryWrapper<WarehouseStoreEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.in(WarehouseStoreEntity::getBizType, StoreBizTypeEnum.PURCHASE_IN.getCode(), StoreBizTypeEnum.TEMP_PURCHASE_IN.getCode())
			.eq(WarehouseStoreEntity::getRelatedSourceId, supplierId) // relatedSourceId 是供应商ID
			.eq(WarehouseStoreEntity::getWarehouseId, warehouseId)
			.eq(WarehouseStoreEntity::getStatus, BusinessConstant.ENABLE_STATUS)
			.between(WarehouseStoreEntity::getCompletionTime, startDate, endDate);

		return warehouseStoreService.list(wrapper);
	}

	/**
	 * 计算每日结算金额
	 * 来源: 入库记录 (WarehouseStore)
	 * 逻辑: SUM(入库明细.actual_weight * 入库明细.unitPrice)
	 */
	private BigDecimal calculateDailySettlementAmount(List<WarehouseStoreItemEntity> items) {
		if (items == null || items.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 1. 计算商品结算总额
		BigDecimal productAmount = items.stream()
			.filter(item -> item.getActualWeight() != null && item.getUnitPrice() != null)
			.map(item -> item.getActualWeight().multiply(item.getUnitPrice()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 2. 提取所有关联的入库主单ID
		List<Long> storeIds = items.stream()
			.map(WarehouseStoreItemEntity::getWarehouseStoreId)
			.distinct()
			.toList();

		if (storeIds.isEmpty()) {
			return productAmount.setScale(2, RoundingMode.HALF_UP);
		}

		// 3. 根据入库主单ID，批量查询关联的配套运输记录
		List<WarehouseSupportTransEntity> supportTransList = warehouseSupportTransService.list(
			Wrappers.<WarehouseSupportTransEntity>lambdaQuery()
				.in(WarehouseSupportTransEntity::getRelatedRecordId, storeIds)
				.eq(WarehouseSupportTransEntity::getBizType, 1) // 1=入库
		);

		// 4. 计算配套运输总额
		BigDecimal supportTransAmount = supportTransList.stream()
			.map(support -> {
				BigDecimal num = new BigDecimal(Optional.ofNullable(support.getSupportTransNum()).orElse(0));
				BigDecimal price = Optional.ofNullable(support.getSupportTransPrice()).orElse(BigDecimal.ZERO);
				return num.multiply(price);
			})
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 5. 返回总金额
		return productAmount.add(supportTransAmount).setScale(2, RoundingMode.HALF_UP);
	}

	/**
	 * 用于返回售后扣款和罚款的内部类
	 */
	@lombok.Data
	@lombok.AllArgsConstructor
	private static class AfterSalesAmounts {
		private BigDecimal deduction;
		private BigDecimal fines;
	}

	/**
	 * 获取每日售后扣款和罚款
	 * 来源: 售后单 (OrderAfterSales)
	 * 逻辑: 分别聚合供应商售后扣款和供应商售后罚款
	 */
	private AfterSalesAmounts getDailyAfterSalesAmounts(Long supplierId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 批量获取当天的售后主单
		List<OrderAfterSalesServiceEntity> services = orderAfterSalesService.list(
			Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery()
				.between(OrderAfterSalesServiceEntity::getCreateTime, startOfDay, endOfDay)
		);
		if (services.isEmpty()) {
			return new AfterSalesAmounts(BigDecimal.ZERO, BigDecimal.ZERO);
		}
		List<Long> serviceIds = services.stream().map(OrderAfterSalesServiceEntity::getId).toList();

		// 2. 批量获取售后详情
		List<OrderAfterSalesServiceItemEntity> items = orderAfterSalesServiceItemService.list(
			Wrappers.<OrderAfterSalesServiceItemEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemEntity::getAfterSalesId, serviceIds)
		);
		if (items.isEmpty()) {
			return new AfterSalesAmounts(BigDecimal.ZERO, BigDecimal.ZERO);
		}
		List<Long> itemIds = items.stream().map(OrderAfterSalesServiceItemEntity::getId).toList();

		// 3. 批量获取供应商扣款记录
		List<OrderAfterSalesServiceItemSupplierEntity> deductions = orderAfterSalesServiceItemSupplierService.list(
			Wrappers.<OrderAfterSalesServiceItemSupplierEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemSupplierEntity::getAfterSalesItemId, itemIds)
				.eq(OrderAfterSalesServiceItemSupplierEntity::getSupplierId, supplierId)
		);

		// 4. 批量获取供应商罚款记录
		List<OrderAfterSalesServiceItemMoneyEntity> fines = orderAfterSalesServiceItemMoneyService.list(
			Wrappers.<OrderAfterSalesServiceItemMoneyEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemMoneyEntity::getAfterSalesItemId, itemIds)
				.eq(OrderAfterSalesServiceItemMoneyEntity::getSupplierId, supplierId)
		);

		// 5. 聚合扣款
		BigDecimal totalDeduction = deductions.stream()
			.map(deduction -> CommonUtil.ConvertIntBigDecimal(deduction.getFinanceAmount()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 6. 聚合罚款
		BigDecimal totalFines = fines.stream()
			.map(fine -> CommonUtil.ConvertIntBigDecimal(fine.getFinanceAmount()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		return new AfterSalesAmounts(totalDeduction, totalFines);
	}

	private AfterSalesAmounts getDailyAfterSalesAmounts(Long supplierId, Long warehouseId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 批量获取当天的售后主单 (增加了仓库过滤)
		List<OrderAfterSalesServiceEntity> services = orderAfterSalesService.list(
			Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery()
				.eq(OrderAfterSalesServiceEntity::getWarehouseId, warehouseId)
				.between(OrderAfterSalesServiceEntity::getCreateTime, startOfDay, endOfDay)
		);
		if (services.isEmpty()) {
			return new AfterSalesAmounts(BigDecimal.ZERO, BigDecimal.ZERO);
		}
		List<Long> serviceIds = services.stream().map(OrderAfterSalesServiceEntity::getId).toList();

		// 2. 批量获取售后详情
		List<OrderAfterSalesServiceItemEntity> items = orderAfterSalesServiceItemService.list(
			Wrappers.<OrderAfterSalesServiceItemEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemEntity::getAfterSalesId, serviceIds)
		);
		if (items.isEmpty()) {
			return new AfterSalesAmounts(BigDecimal.ZERO, BigDecimal.ZERO);
		}
		List<Long> itemIds = items.stream().map(OrderAfterSalesServiceItemEntity::getId).toList();

		// 3. 批量获取供应商扣款记录
		List<OrderAfterSalesServiceItemSupplierEntity> deductions = orderAfterSalesServiceItemSupplierService.list(
			Wrappers.<OrderAfterSalesServiceItemSupplierEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemSupplierEntity::getAfterSalesItemId, itemIds)
				.eq(OrderAfterSalesServiceItemSupplierEntity::getSupplierId, supplierId)
		);

		// 4. 批量获取供应商罚款记录
		List<OrderAfterSalesServiceItemMoneyEntity> fines = orderAfterSalesServiceItemMoneyService.list(
			Wrappers.<OrderAfterSalesServiceItemMoneyEntity>lambdaQuery()
				.in(OrderAfterSalesServiceItemMoneyEntity::getAfterSalesItemId, itemIds)
				.eq(OrderAfterSalesServiceItemMoneyEntity::getSupplierId, supplierId)
		);

		// 5. 聚合扣款
		BigDecimal totalDeduction = deductions.stream()
			.map(deduction -> CommonUtil.ConvertIntBigDecimal(deduction.getFinanceAmount()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 6. 聚合罚款
		BigDecimal totalFines = fines.stream()
			.map(fine -> CommonUtil.ConvertIntBigDecimal(fine.getFinanceAmount()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		return new AfterSalesAmounts(totalDeduction, totalFines);
	}

	/**
	 * 获取每日报损退货金额
	 * 来源: 出库记录 (WarehouseOutbound)
	 * 逻辑: SUM(出库明细.weight * 出库明细.productSalePrice) where bizType = 'DIFF_BS'
	 */
	private BigDecimal getDailyReportLossReturnAmount(Long supplierId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 查询当天的差异报损出库单
		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode()) // 差异报损出库
				.between(WarehouseOutboundEntity::getCompletionTime, startOfDay, endOfDay)
		);

		if (outbounds.isEmpty()) {
			return BigDecimal.ZERO;
		}

		List<Long> outboundIds = outbounds.stream().map(WarehouseOutboundEntity::getId).toList();

		// 2. 查询相关的出库明细，并按供应商ID过滤
		List<WarehouseOutboundItemEntity> items = warehouseOutboundItemService.list(
			Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
				.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
				.eq(WarehouseOutboundItemEntity::getSupplierId, supplierId)
		);

		// 3. 计算报损商品总额
		BigDecimal productLossAmount = BigDecimal.ZERO;
		if (!items.isEmpty()) {
			productLossAmount = items.stream()
				.map(item -> {
					BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
					BigDecimal salePriceInYuan = new BigDecimal(Optional.ofNullable(item.getProductSalePrice()).orElse(0))
						.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
					return weight.multiply(salePriceInYuan);
				})
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		}

		// 4. 计算关联的配套运输费用
		// 找出与该供应商相关的出库ID
		Set<Long> relatedOutboundIds = items.stream()
			.map(WarehouseOutboundItemEntity::getWarehouseOutboundId)
			.collect(Collectors.toSet());

		BigDecimal supportLossAmount = BigDecimal.ZERO;
		if (!relatedOutboundIds.isEmpty()) {
			List<WarehouseSupportTransEntity> supportTransItems = warehouseSupportTransService.list(
				Wrappers.<WarehouseSupportTransEntity>lambdaQuery()
					.in(WarehouseSupportTransEntity::getRelatedRecordId, relatedOutboundIds)
					.eq(WarehouseSupportTransEntity::getBizType, 2) // 2=出库
					.eq(WarehouseSupportTransEntity::getSupplierId, supplierId) // 修复：必须按供应商ID过滤
			);

			if (!supportTransItems.isEmpty()) {
				supportLossAmount = supportTransItems.stream()
					.map(supportItem -> {
						Integer num = Optional.ofNullable(supportItem.getSupportTransNum()).orElse(0);
						BigDecimal price = Optional.ofNullable(supportItem.getSupportTransPrice()).orElse(BigDecimal.ZERO);
						return price.multiply(new BigDecimal(num));
					})
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
		}

		// 5. 返回总金额
		return productLossAmount.add(supportLossAmount).setScale(2, RoundingMode.HALF_UP);
	}

	private BigDecimal getDailyReportLossReturnAmount(Long supplierId, Long warehouseId, LocalDate date) {
		LocalDateTime startOfDay = date.atStartOfDay();
		LocalDateTime endOfDay = date.atTime(23, 59, 59);

		// 1. 查询当天的差异报损出库单
		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode()) // 差异报损出库
				.eq(WarehouseOutboundEntity::getWarehouseId, warehouseId)
				.between(WarehouseOutboundEntity::getCompletionTime, startOfDay, endOfDay)
		);

		if (outbounds.isEmpty()) {
			return BigDecimal.ZERO;
		}

		List<Long> outboundIds = outbounds.stream().map(WarehouseOutboundEntity::getId).toList();

		// 2. 查询相关的出库明细，并按供应商ID过滤
		List<WarehouseOutboundItemEntity> items = warehouseOutboundItemService.list(
			Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
				.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
				.eq(WarehouseOutboundItemEntity::getSupplierId, supplierId)
		);

		// 3. 聚合计算商品总金额
		BigDecimal productLossAmount = BigDecimal.ZERO;
		if (!items.isEmpty()) {
			productLossAmount = items.stream()
				.map(item -> {
					BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
					BigDecimal salePriceInYuan = new BigDecimal(Optional.ofNullable(item.getProductSalePrice()).orElse(0))
						.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
					return weight.multiply(salePriceInYuan);
				})
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		}

		// 4. 计算配套运输总额
		BigDecimal supportLossAmount = BigDecimal.ZERO;
		if (!outboundIds.isEmpty()){
			List<WarehouseSupportTransEntity> supportTransItems = warehouseSupportTransService.list(
				Wrappers.<WarehouseSupportTransEntity>lambdaQuery()
					.in(WarehouseSupportTransEntity::getRelatedRecordId, outboundIds)
					.eq(WarehouseSupportTransEntity::getBizType, 2) // 2=出库
					.eq(WarehouseSupportTransEntity::getSupplierId, supplierId) // 修复：必须按供应商ID过滤
			);

			if (!supportTransItems.isEmpty()) {
				supportLossAmount = supportTransItems.stream()
					.map(supportItem -> {
						Integer num = Optional.ofNullable(supportItem.getSupportTransNum()).orElse(0);
						BigDecimal price = Optional.ofNullable(supportItem.getSupportTransPrice()).orElse(BigDecimal.ZERO);
						return price.multiply(new BigDecimal(num));
					})
					.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
		}

		// 5. 返回总额
		return productLossAmount.add(supportLossAmount).setScale(2, RoundingMode.HALF_UP);
	}

	private BigDecimal calculateDailyForwardingCharges(List<WarehouseStoreEntity> dailyStores, List<WarehouseStoreItemEntity> dailyStoreItems) {
		if (dailyStores == null || dailyStores.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 1. 从入库记录中获取所有关联的备货单号
		List<String> replenishmentNos = dailyStores.stream()
			.map(WarehouseStoreEntity::getRelatedOrderNo)
			.filter(Func::isNotEmpty)
			.distinct()
			.collect(Collectors.toList());

		if (replenishmentNos.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 2. 根据备货单号批量查询转运费流水
		List<TransportFeeEntity> transportFees = transportFeeService.list(
			Wrappers.<TransportFeeEntity>lambdaQuery()
				.in(TransportFeeEntity::getReplenishmentNo, replenishmentNos)
		);

		if (transportFees.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 3. 聚合计算总转运费 (金额单位是分，需要转为元)
		return transportFees.stream()
			.map(fee -> CommonUtil.ConvertIntBigDecimal(fee.getAmount()))
			.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	private BigDecimal calculateDailyForwardingChargesByItems(List<WarehouseStoreItemEntity> storeItems) {
		return calculateForwardingChargesForItems(storeItems);
	}

	/**
	 * [公共方法] 根据入库商品列表计算总转运费 - 已根据正确的业务逻辑重构
	 *
	 * @param items 入库商品明细列表
	 * @return 转运费总额
	 */
	private BigDecimal calculateForwardingChargesForItems(List<WarehouseStoreItemEntity> items) {
		if (Func.isEmpty(items)) {
			return BigDecimal.ZERO;
		}

		// 1. 获取所有不重复的SKU ID
		List<Long> skuIds = items.stream()
			.map(WarehouseStoreItemEntity::getProductSkuId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		if (skuIds.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 2. 一次性获取所有SKU的转运费配置
		Map<Long, SkuForwardingChargesEntity> chargesMap = skuForwardingChargesService.listBySkuIds(skuIds);

		if (chargesMap.isEmpty()) {
			return BigDecimal.ZERO;
		}

		// 3. 遍历商品列表，根据 priceType 计算总费用
		BigDecimal totalFee = BigDecimal.ZERO;
		for (WarehouseStoreItemEntity item : items) {
			SkuForwardingChargesEntity chargeConfig = chargesMap.get(item.getProductSkuId());

			// 检查配置是否存在且有效
			if (chargeConfig == null || chargeConfig.getPrice() == null || chargeConfig.getPriceType() == null) {
				continue;
			}

			// 价格单位为分，转换为元
			BigDecimal price = CommonUtil.ConvertIntBigDecimal(chargeConfig.getPrice());
			Integer priceType = chargeConfig.getPriceType();
			BigDecimal chargeForItem = BigDecimal.ZERO;

			// priceType: 0-按件, 1-按重量
			if (priceType == 0 && item.getActualQuantity() != null && item.getActualQuantity() > 0) {
				chargeForItem = price.multiply(new BigDecimal(item.getActualQuantity()));
			} else if (priceType == 1 && item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) > 0) {
				chargeForItem = price.multiply(item.getActualWeight());
			}

			totalFee = totalFee.add(chargeForItem);
		}
		return totalFee.setScale(2, RoundingMode.HALF_UP);
	}

	private void aggregateDailyItemsToMain(SupplierReconciliationEntity reconciliation, List<SupplierReconciliationItemEntity> items) {
		reconciliation.setSupplyAmount(items.stream().map(SupplierReconciliationItemEntity::getSupplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setSettlementAmount(items.stream().map(SupplierReconciliationItemEntity::getSettlementAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setAfterDeduction(items.stream().map(SupplierReconciliationItemEntity::getAfterDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setAfterFines(items.stream().map(SupplierReconciliationItemEntity::getAfterFines).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setReportLossReturn(items.stream().map(SupplierReconciliationItemEntity::getReportLossReturn).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setForwardingCharges(items.stream().map(SupplierReconciliationItemEntity::getForwardingCharges).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		reconciliation.setReturnOutboundAmount(items.stream().map(SupplierReconciliationItemEntity::getReturnOutboundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

		// 计算应付金额: 结算金额 - 售后扣款 - 售后罚款 - 报损退货 - 转运费-退货
		BigDecimal payableAmount = reconciliation.getSettlementAmount()
			.subtract(reconciliation.getAfterDeduction())
			.subtract(reconciliation.getAfterFines())
			.subtract(reconciliation.getReportLossReturn())
			.subtract(reconciliation.getForwardingCharges())
			.subtract(reconciliation.getReturnOutboundAmount());

		reconciliation.setPayableAmount(payableAmount);
		reconciliation.setFinalPayableAmount(payableAmount); // 初始最终应付金额等于应付金额
	}

	/**
	 * 批量获取供应商的报损出库项目
	 */
	private Map<Long, List<WarehouseOutboundItemEntity>> getReportLossItemsForSuppliers(List<Long> supplierIds, Date startDate, Date endDate) {
		// 1. 找出所有报损类型的出库主单ID
		List<WarehouseOutboundEntity> outboundList = warehouseOutboundService.list(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode())
		);
		if (outboundList.isEmpty()) {
			return Collections.emptyMap();
		}
		List<Long> outboundIds = outboundList.stream().map(WarehouseOutboundEntity::getId).toList();

		// 2. 根据主单ID、供应商和时间范围，查询所有相关的出库明细项
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(
			Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
				.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
				.in(WarehouseOutboundItemEntity::getSupplierId, supplierIds)
				.between(WarehouseOutboundItemEntity::getCreateTime, startDate, endDate)
		);

		if (outboundItems.isEmpty()) {
			return Collections.emptyMap();
		}

		// 3. 按供应商ID分组
		return outboundItems.stream().collect(Collectors.groupingBy(WarehouseOutboundItemEntity::getSupplierId));
	}

	/**
	 * 处理报损数据，生成对账明细
	 */
	private void processReportLoss(SupplierReconciliationEntity reconciliation,
								   List<WarehouseOutboundItemEntity> reportLossItems,
								   List<SupplierReconciliationItemEntity> fullItemList) {

		BigDecimal totalReportLossAmount = BigDecimal.ZERO;
		BigDecimal totalWeight = BigDecimal.ZERO;
		for (WarehouseOutboundItemEntity item : reportLossItems) {
			BigDecimal weight = Func.isEmpty(item.getWeight()) ? BigDecimal.ZERO : item.getWeight();
			BigDecimal price = Func.isEmpty(item.getProductSalePrice()) ? BigDecimal.ZERO : CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice());
			totalReportLossAmount = totalReportLossAmount.add(weight.multiply(price));
			totalWeight = totalWeight.add(weight);
		}

		SupplierReconciliationItemEntity reconciliationItem = new SupplierReconciliationItemEntity();
		reconciliationItem.setWeight(totalWeight);
		reconciliationItem.setReconciliationId(reconciliation.getId());
		reconciliationItem.setSupplierId(reconciliation.getSupplierId());
		reconciliationItem.setWarehouseId(reportLossItems.get(0).getWarehouseId());
		reconciliationItem.setReconciliationTime(Date.from(reconciliation.getCycleEndTime().atStartOfDay(ZoneId.systemDefault()).toInstant()));
		reconciliationItem.setReturnAmount(totalReportLossAmount);
		reconciliationItem.setType(BusinessConstant.ORDER_TYPE_LOSS); // 4 代表报损退款
		fullItemList.add(reconciliationItem);

	}

	@Override
	public RealtimeReconciliationPageVO queryRealtimeReconciliation(RealtimeReconciliationQueryDTO dto, Query query) {
		// 1. 参数校验和准备
		if (dto.getStartTime() == null || dto.getEndTime() == null) {
			throw new ServiceException("开始时间和结束时间均不能为空");
		}
		// 获取所有需要查询的供应商，如果前端未传入，则查询全部
		List<SupplierEntity> suppliers = Func.isNotEmpty(dto.getSupplierIds())
			? supplierService.listByIds(dto.getSupplierIds())
			: supplierService.list(Wrappers.<SupplierEntity>lambdaQuery().eq(SupplierEntity::getStatus, BusinessConstant.ENABLE_STATUS));
		if (suppliers.isEmpty()) {
			return new RealtimeReconciliationPageVO();
		}
		Map<Long, SupplierEntity> supplierMap = suppliers.stream().collect(Collectors.toMap(SupplierEntity::getId, Function.identity()));

		// 2. 创建内存聚合Map，计算出全量数据
		Map<String, RealtimeReconciliationVO> resultMap = new HashMap<>();
		processSupplyAmountAndForwardingForRealtimeQuery(resultMap, supplierMap, dto);
		processSettlementForRealtimeQuery(resultMap, supplierMap, dto);
		processAfterSalesForRealtimeQuery(resultMap, supplierMap, dto);
		processReportLossForRealtimeQuery(resultMap, supplierMap, dto);
		processReturnAmountForRealtimeQuery(resultMap, supplierMap, dto);
		List<RealtimeReconciliationVO> list = new ArrayList<>(resultMap.values());
		// 按日期和供应商排序
		list.sort(Comparator.comparing(RealtimeReconciliationVO::getDate).thenComparing(RealtimeReconciliationVO::getSupplierId));

		// 3. 计算总计行
		RealtimeReconciliationVO summary = calculateSummary(list);

		// 4. 手动进行内存分页
		IPage<RealtimeReconciliationVO> page = manualPaginate(list, query);

		// 5. 组装并返回结果
		RealtimeReconciliationPageVO result = new RealtimeReconciliationPageVO();
		result.setPage(page);
		result.setSummary(summary);
		return result;
	}






	/**
	 * [辅助方法] 创建聚合Map的Key
	 */
	private String buildMapKey(LocalDate date, Long supplierId) {
		return date.toString() + "_" + supplierId;
	}

	/**
	 * [处理步骤1] 批量处理"供货总额"和"转运费"
	 */
	private void processSupplyAmountAndForwardingForRealtimeQuery(Map<String, RealtimeReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, RealtimeReconciliationQueryDTO dto) {
		// 批量获取指定供应商和日期范围内的所有备货单
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
			.in(ReplenishmentOrderEntity::getSupplierId, supplierMap.keySet())
			.between(ReplenishmentOrderEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
			.eq(Func.isNotEmpty(dto.getWarehouseId()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseId())
			.in(Func.isNotEmpty(dto.getWarehouseIds()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseIds())
		);
		if (orders.isEmpty()) return;

		// 批量获取所有备货单的明细
		List<Long> orderIds = orders.stream().map(ReplenishmentOrderEntity::getId).collect(Collectors.toList());
		List<ReplenishmentOrderItemEntity> items = replenishmentOrderItemService.list(Wrappers.<ReplenishmentOrderItemEntity>lambdaQuery().in(ReplenishmentOrderItemEntity::getReplenishmentOrderId, orderIds));
		Map<Long, List<ReplenishmentOrderItemEntity>> itemsByOrder = items.stream().collect(Collectors.groupingBy(ReplenishmentOrderItemEntity::getReplenishmentOrderId));

		// 新逻辑：批量获取转运费
		List<String> replenishmentNos = orders.stream().map(ReplenishmentOrderEntity::getReplenishmentNo).distinct().toList();
		List<TransportFeeEntity> transportFees = transportFeeService.list(Wrappers.<TransportFeeEntity>lambdaQuery().in(TransportFeeEntity::getReplenishmentNo, replenishmentNos));
		Map<String, BigDecimal> forwardingChargesByReplenishmentNo = transportFees.stream()
			.collect(Collectors.toMap(TransportFeeEntity::getReplenishmentNo,
				fee -> CommonUtil.ConvertIntBigDecimal(fee.getAmount()), // 金额单位是分，转为元
				BigDecimal::add)); // 如果有重复的单号，则累加

		// 遍历备货单，聚合供货金额和转运费
		for (ReplenishmentOrderEntity order : orders) {
			LocalDate date = order.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();
			String key = buildMapKey(date, order.getSupplierId());
			List<ReplenishmentOrderItemEntity> orderItems = itemsByOrder.getOrDefault(order.getId(), Collections.emptyList());

			BigDecimal supplyAmount = orderItems.stream()
				.map(item -> {
					// 商品金额：单价 * 重量
					BigDecimal unitPrice = Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO);
					BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
					BigDecimal productAmount = unitPrice.multiply(weight);

					// 配套运输金额：数量 * 单价
					Integer supportNum = Optional.ofNullable(item.getSupportTransNum()).orElse(0);
					BigDecimal supportPrice = Optional.ofNullable(item.getSupportTransPrice()).orElse(BigDecimal.ZERO);
					BigDecimal supportAmount = BigDecimal.ZERO;
					if (supportNum > 0 && !supportPrice.equals(BigDecimal.ZERO)) {
						supportAmount = new BigDecimal(supportNum)
							.multiply(supportPrice)
						;
					}

					return productAmount.add(supportAmount);
				})
				.reduce(BigDecimal.ZERO, BigDecimal::add);

			BigDecimal forwardingCharges = forwardingChargesByReplenishmentNo.getOrDefault(order.getReplenishmentNo(), BigDecimal.ZERO);

			if (supplyAmount.compareTo(BigDecimal.ZERO) > 0 || forwardingCharges.compareTo(BigDecimal.ZERO) > 0) {
				RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
					SupplierEntity s = supplierMap.get(order.getSupplierId());
					RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
					newVo.setDate(date);
					newVo.setSupplierId(s.getId());
					newVo.setSupplierName(s.getFullName());
					newVo.setSupplierPhone(s.getPhone());
					return newVo;
				});
				// 累加供货金额，并保留两位小数
				vo.setSupplyAmount(
					vo.getSupplyAmount() == null
						? supplyAmount.setScale(2, RoundingMode.HALF_UP)
						: vo.getSupplyAmount().add(supplyAmount).setScale(2, RoundingMode.HALF_UP)
				);

				// 累加转运费，并保留两位小数
				vo.setForwardingCharges(
					vo.getForwardingCharges() == null
						? forwardingCharges.setScale(2, RoundingMode.HALF_UP)
						: vo.getForwardingCharges().add(forwardingCharges).setScale(2, RoundingMode.HALF_UP)
				);
			}
		}

	}

	/**
	 * [处理步骤2] 批量处理"结算金额"
	 */
	private void processSettlementForRealtimeQuery(Map<String, RealtimeReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, RealtimeReconciliationQueryDTO dto) {
		// 批量获取入库单
		List<WarehouseStoreEntity> stores = warehouseStoreService.list(Wrappers.<WarehouseStoreEntity>lambdaQuery()
			.in(WarehouseStoreEntity::getRelatedSourceId, supplierMap.keySet()) // related_source_id 存的是供应商ID
			.between(WarehouseStoreEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
			.in(Func.isNotEmpty(dto.getWarehouseIds()), WarehouseStoreEntity::getWarehouseId, dto.getWarehouseIds())
			.eq(Func.isNotEmpty(dto.getWarehouseId()), WarehouseStoreEntity::getWarehouseId, dto.getWarehouseId())
		);
		if (stores.isEmpty()) return;

		// 批量获取入库明细
		List<Long> storeIds = stores.stream().map(WarehouseStoreEntity::getId).collect(Collectors.toList());
		List<WarehouseStoreItemEntity> items = warehouseStoreItemService.list(Wrappers.<WarehouseStoreItemEntity>lambdaQuery().in(WarehouseStoreItemEntity::getWarehouseStoreId, storeIds));
		Map<Long, List<WarehouseStoreItemEntity>> itemsByStore = items.stream().collect(Collectors.groupingBy(WarehouseStoreItemEntity::getWarehouseStoreId));

		// 遍历入库单，聚合结算金额
		for (WarehouseStoreEntity store : stores) {
			LocalDate date = store.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();
			Long supplierId = store.getRelatedSourceId();
			String key = buildMapKey(date, supplierId);
			List<WarehouseStoreItemEntity> storeItems = itemsByStore.getOrDefault(store.getId(), Collections.emptyList());

			BigDecimal settlementAmount = calculateDailySettlementAmount(storeItems);

			if (settlementAmount.compareTo(BigDecimal.ZERO) > 0) {
				RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
					SupplierEntity s = supplierMap.get(supplierId);
					RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
					newVo.setDate(date);
					newVo.setSupplierId(s.getId());
					newVo.setSupplierName(s.getFullName());
					newVo.setSupplierPhone(s.getPhone());
					return newVo;
				});
				// 修正：从直接设置改为累加
				vo.setSettlementAmount(vo.getSettlementAmount() == null ? settlementAmount : vo.getSettlementAmount().add(settlementAmount));
			}
		}
	}

	/**
	 * [处理步骤3] 批量处理"售后扣款"和"售后罚款"
	 */
	private void processAfterSalesForRealtimeQuery(Map<String, RealtimeReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, RealtimeReconciliationQueryDTO dto) {
		// 1. 批量获取售后主单
		List<OrderAfterSalesServiceEntity> services = orderAfterSalesService.list(Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery()
			.between(OrderAfterSalesServiceEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
			.in(Func.isNotEmpty(dto.getWarehouseIds()), OrderAfterSalesServiceEntity::getWarehouseId, dto.getWarehouseIds())
			.eq(Func.isNotEmpty(dto.getWarehouseId()), OrderAfterSalesServiceEntity::getWarehouseId, dto.getWarehouseId())
		);
		if (services.isEmpty()) return;

		List<Long> serviceIds = services.stream().map(OrderAfterSalesServiceEntity::getId).collect(Collectors.toList());
		Map<Long, OrderAfterSalesServiceEntity> serviceMap = services.stream().collect(Collectors.toMap(OrderAfterSalesServiceEntity::getId, Function.identity()));

		// 2. 批量获取售后详情
		List<OrderAfterSalesServiceItemEntity> items = orderAfterSalesServiceItemService.list(Wrappers.<OrderAfterSalesServiceItemEntity>lambdaQuery().in(OrderAfterSalesServiceItemEntity::getAfterSalesId, serviceIds));
		if (items.isEmpty()) return;
		List<Long> itemIds = items.stream().map(OrderAfterSalesServiceItemEntity::getId).collect(Collectors.toList());
		Map<Long, Long> itemToServiceIdMap = items.stream().collect(Collectors.toMap(OrderAfterSalesServiceItemEntity::getId, OrderAfterSalesServiceItemEntity::getAfterSalesId));


		// 3. 批量获取供应商扣款和罚款记录
		List<OrderAfterSalesServiceItemSupplierEntity> deductions = orderAfterSalesServiceItemSupplierService.list(Wrappers.<OrderAfterSalesServiceItemSupplierEntity>lambdaQuery()
			.in(OrderAfterSalesServiceItemSupplierEntity::getAfterSalesItemId, itemIds)
			.in(OrderAfterSalesServiceItemSupplierEntity::getSupplierId, supplierMap.keySet())
		);
		List<OrderAfterSalesServiceItemMoneyEntity> fines = orderAfterSalesServiceItemMoneyService.list(Wrappers.<OrderAfterSalesServiceItemMoneyEntity>lambdaQuery()
			.in(OrderAfterSalesServiceItemMoneyEntity::getAfterSalesItemId, itemIds)
			.in(OrderAfterSalesServiceItemMoneyEntity::getSupplierId, supplierMap.keySet())
		);

		// 4. 聚合扣款
		for (OrderAfterSalesServiceItemSupplierEntity deduction : deductions) {
			Long serviceId = itemToServiceIdMap.get(deduction.getAfterSalesItemId());
			if (serviceId == null) continue;
			OrderAfterSalesServiceEntity service = serviceMap.get(serviceId);
			if (service == null) continue;

			LocalDate date = service.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();
			String key = buildMapKey(date, deduction.getSupplierId());

			RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
				SupplierEntity s = supplierMap.get(deduction.getSupplierId());
				RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
				newVo.setDate(date);
				newVo.setSupplierId(s.getId());
				newVo.setSupplierName(s.getFullName());
				newVo.setSupplierPhone(s.getPhone());
				return newVo;
			});
			BigDecimal amount = CommonUtil.ConvertIntBigDecimal(deduction.getFinanceAmount());
			vo.setAfterDeduction(vo.getAfterDeduction() == null ? amount : vo.getAfterDeduction().add(Func.isNotEmpty(amount) ? amount:BigDecimal.ZERO));
		}
		// 5. 聚合罚款
		for (OrderAfterSalesServiceItemMoneyEntity fine : fines) {
			Long serviceId = itemToServiceIdMap.get(fine.getAfterSalesItemId());
			if (serviceId == null) continue;
			OrderAfterSalesServiceEntity service = serviceMap.get(serviceId);
			if (service == null) continue;

			LocalDate date = service.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault())
				.toLocalDate();
			String key = buildMapKey(date, fine.getSupplierId());

			RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
				SupplierEntity s = supplierMap.get(fine.getSupplierId());
				RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
				newVo.setDate(date);
				newVo.setSupplierId(s.getId());
				newVo.setSupplierName(s.getFullName());
				newVo.setSupplierPhone(s.getPhone());
				return newVo;
			});
			BigDecimal amount = CommonUtil.ConvertIntBigDecimal(fine.getFinanceAmount());
			vo.setAfterFines(vo.getAfterFines() == null ? amount : vo.getAfterFines().add(Func.isNotEmpty(amount) ? amount:BigDecimal.ZERO));
		}
	}

	/**
	 * [处理步骤4] 批量处理"报损扣款"
	 */
	private void processReportLossForRealtimeQuery(Map<String, RealtimeReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, RealtimeReconciliationQueryDTO dto) {
		// 批量获取报损出库单
		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode())
			.eq(Func.isNotEmpty(dto.getWarehouseId()), WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseId())
			.in(Func.isNotEmpty(dto.getWarehouseIds()), WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseIds())
			.between(WarehouseOutboundEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
		);
		if (outbounds.isEmpty()) return;

		Map<Long, WarehouseOutboundEntity> outboundsMap = outbounds.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, Function.identity()));
		List<Long> outboundIds = new ArrayList<>(outboundsMap.keySet());

		// 批量获取商品报损明细
		List<WarehouseOutboundItemEntity> productLossItems = warehouseOutboundItemService.list(Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
			.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
			.in(WarehouseOutboundItemEntity::getSupplierId, supplierMap.keySet())
		);

		// 批量获取配套运输报损明细
		List<WarehouseSupportTransEntity> transportLossItems = warehouseSupportTransService.list(Wrappers.<WarehouseSupportTransEntity>lambdaQuery()
			.in(WarehouseSupportTransEntity::getRelatedRecordId, outboundIds)
			.eq(WarehouseSupportTransEntity::getBizType, BusinessConstant.WAREHOUSE_OUTBOUND)
		);


		// 1. 独立处理商品报损
		for (WarehouseOutboundItemEntity item : productLossItems) {
			WarehouseOutboundEntity parentOutbound = outboundsMap.get(item.getWarehouseOutboundId());
			if (parentOutbound == null) continue;

			LocalDate date = parentOutbound.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildMapKey(date, item.getSupplierId());
			BigDecimal amount = (item.getWeight() != null ? item.getWeight() : BigDecimal.ZERO)
				.multiply(item.getProductSalePrice() != null ? CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice()) : BigDecimal.ZERO);

				RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
					SupplierEntity s = supplierMap.get(item.getSupplierId());
					RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
					newVo.setDate(date);
					newVo.setSupplierId(s.getId());
					newVo.setSupplierName(s.getFullName());
					newVo.setSupplierPhone(s.getPhone());
					return newVo;
				});
				vo.setReportLossReturn(vo.getReportLossReturn() == null ? amount : vo.getReportLossReturn().add(amount));

		}

		// 2. 独立处理配套运输报损
		for (WarehouseSupportTransEntity supportItem : transportLossItems) {
			WarehouseOutboundEntity parentOutbound = outboundsMap.get(supportItem.getRelatedRecordId());
			// 直接从配套运输记录中获取供应商ID
			Long supplierId = supportItem.getSupplierId();

			// 如果父级出库记录不存在，或者该供应商不在查询范围内，则跳过
			if (parentOutbound == null || supplierId == null || !supplierMap.containsKey(supplierId)) {
				continue;
			}

			LocalDate date = parentOutbound.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildMapKey(date, supplierId);
			BigDecimal supAmount = (supportItem.getSupportTransNum() != null ? BigDecimal.valueOf(supportItem.getSupportTransNum()) : BigDecimal.ZERO)
				.multiply(supportItem.getSupportTransPrice() != null ? supportItem.getSupportTransPrice() : BigDecimal.ZERO);

				RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
					SupplierEntity s = supplierMap.get(supplierId);
					RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
					newVo.setDate(date);
					newVo.setSupplierId(s.getId());
					newVo.setSupplierName(s.getFullName());
					newVo.setSupplierPhone(s.getPhone());
					return newVo;
				});
				vo.setReportLossReturn(vo.getReportLossReturn() == null ? supAmount : vo.getReportLossReturn().add(supAmount));
		}
		resultMap.values().forEach(vo -> {
			if (vo.getReportLossReturn() != null) {
				BigDecimal reportLossReturn = Optional.ofNullable(vo.getReportLossReturn()).orElse(BigDecimal.ZERO);
				vo.setReportLossReturn(reportLossReturn.abs().setScale(2, RoundingMode.HALF_UP));
			}
		});
	}
	/**
	 * 计算退货出库的数据
	 */
	private void processReturnAmountForRealtimeQuery(Map<String, RealtimeReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, RealtimeReconciliationQueryDTO dto) {
		// 1. 批量获取指定时间范围和仓库的 "退货出库" 单
		List<WarehouseOutboundEntity> outboundList = warehouseOutboundService.list(new LambdaQueryWrapper<WarehouseOutboundEntity>()
			.in(Func.isNotEmpty(dto.getWarehouseIds()), WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseIds())
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.RETURN.getCode())
			.eq(Func.isNotEmpty(dto.getWarehouseId()), WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseId())
			.between(WarehouseOutboundEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(LocalTime.MAX)));

		if (CollectionUtils.isEmpty(outboundList)) {
			return;
		}

		Map<Long, WarehouseOutboundEntity> outboundsMap = outboundList.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, Function.identity()));
		List<Long> outboundIds = new ArrayList<>(outboundsMap.keySet());

		// 2. 获取商品退货明细
		List<WarehouseOutboundItemEntity> returnItems = warehouseOutboundItemService.list(new LambdaQueryWrapper<WarehouseOutboundItemEntity>()
			.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds));

		// 3. 遍历商品退货明细，聚合计算退货金额
		for (WarehouseOutboundItemEntity item : returnItems) {
			WarehouseOutboundEntity parentOutbound = outboundsMap.get(item.getWarehouseOutboundId());
			if (parentOutbound == null) continue;

			LocalDate date = parentOutbound.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault()) // 使用系统默认时区
				.toLocalDate();;
			String key = buildMapKey(date, item.getSupplierId());

			BigDecimal amount = (item.getWeight() != null ? item.getWeight() : BigDecimal.ZERO)
				.multiply(item.getProductSalePrice() != null ? CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice()) : BigDecimal.ZERO);

			RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
				RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
				newVo.setDate(date);
				if (item.getSupplierId() != null) {
					SupplierEntity supplier = supplierMap.get(item.getSupplierId());
					if (supplier != null) {
						newVo.setSupplierId(supplier.getId());
						newVo.setSupplierName(supplier.getFullName());
					}
				}
				return newVo;
			});

			vo.setReturnOutboundAmount(vo.getReturnOutboundAmount().add(amount).setScale(2, RoundingMode.HALF_UP));
		}

		// 4. 获取配套运输退货明细
		List<WarehouseSupportTransEntity> supportTransReturns = warehouseSupportTransService.list(new LambdaQueryWrapper<WarehouseSupportTransEntity>()
			.in(WarehouseSupportTransEntity::getRelatedRecordId, outboundIds));

		// 5. 遍历配套运输退货明细，聚合计算
		for (WarehouseSupportTransEntity supportItem : supportTransReturns) {
			WarehouseOutboundEntity parentOutbound = outboundsMap.get(supportItem.getRelatedRecordId());
			if (parentOutbound == null) continue;

			LocalDate date = parentOutbound.getCreateTime().toInstant()
				.atZone(ZoneId.systemDefault()) // 使用系统默认时区
				.toLocalDate();;
			String key = buildMapKey(date, supportItem.getSupplierId());

			BigDecimal amount = (supportItem.getSupportTransNum() != null ? new BigDecimal(supportItem.getSupportTransNum()) : BigDecimal.ZERO)
				.multiply(supportItem.getSupportTransPrice() != null ? supportItem.getSupportTransPrice() : BigDecimal.ZERO);

			RealtimeReconciliationVO vo = resultMap.computeIfAbsent(key, k -> {
				RealtimeReconciliationVO newVo = new RealtimeReconciliationVO();
				newVo.setDate(date);
				if (supportItem.getSupplierId() != null) {
					SupplierEntity supplier = supplierMap.get(supportItem.getSupplierId());
					if (supplier != null) {
						newVo.setSupplierId(supplier.getId());
						newVo.setSupplierName(supplier.getFullName());
					}
				}
				return newVo;
			});

			vo.setReturnOutboundAmount(vo.getReturnOutboundAmount().add(amount).setScale(2, RoundingMode.HALF_UP));
		}
	}

	/**
	 * 计算退货出库的数据
	 */
	private BigDecimal getDailyReturnOutboundAmount(Long supplierId, Long warehouseId, LocalDate date) {
		LocalDateTime startTime = date.atStartOfDay();
		LocalDateTime endTime = date.atTime(LocalTime.MAX);

		List<WarehouseOutboundEntity> outboundList = warehouseOutboundService.list(new LambdaQueryWrapper<WarehouseOutboundEntity>()
			.eq(WarehouseOutboundEntity::getWarehouseId, warehouseId)
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.RETURN.getCode()) // 退货出库
			.between(WarehouseOutboundEntity::getCreateTime, startTime, endTime));

		if (CollectionUtils.isEmpty(outboundList)) {
			return BigDecimal.ZERO;
		}

		List<Long> outboundIds = outboundList.stream().map(WarehouseOutboundEntity::getId).collect(Collectors.toList());

		// 商品退货金额
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(new LambdaQueryWrapper<WarehouseOutboundItemEntity>()
			.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
			.eq(WarehouseOutboundItemEntity::getSupplierId, supplierId)
		);

		BigDecimal productReturnAmount = outboundItems.stream()
			.map(item -> {
				BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
				BigDecimal price = Optional.ofNullable(item.getProductSalePrice()).map(p -> new BigDecimal(p).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
				return weight.multiply(price);
			})
			.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 配套运输退货金额
		List<WarehouseSupportTransEntity> supportTransReturns = warehouseSupportTransService.list(new LambdaQueryWrapper<WarehouseSupportTransEntity>()
			.in(WarehouseSupportTransEntity::getRelatedRecordId, outboundIds)
			.eq(WarehouseSupportTransEntity::getSupplierId, supplierId));

		BigDecimal supportReturnAmount = supportTransReturns.stream()
			.map(item -> {
				BigDecimal num = Optional.ofNullable(item.getSupportTransNum()).map(BigDecimal::new).orElse(BigDecimal.ZERO);
				BigDecimal price = Optional.ofNullable(item.getSupportTransPrice()).map(p -> p.divide(new BigDecimal(100),2,RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
				return num.multiply(price);
			})
			.reduce(BigDecimal.ZERO, BigDecimal::add);


		return productReturnAmount.add(supportReturnAmount).setScale(2, RoundingMode.HALF_UP);
	}

	private RealtimeReconciliationVO createEmptyVO(LocalDate date, Long supplierId, Map<Long, SupplierEntity> supplierMap) {
		RealtimeReconciliationVO vo = new RealtimeReconciliationVO();
		vo.setDate(date);
		if (supplierId != null) {
			SupplierEntity supplier = supplierMap.get(supplierId);
			if(supplier!=null) {
				vo.setSupplierId(supplier.getId());
				vo.setSupplierName(supplier.getFullName());
			}
		}
		vo.setSupplyAmount(BigDecimal.ZERO);
		vo.setSettlementAmount(BigDecimal.ZERO);
		vo.setAfterDeduction(BigDecimal.ZERO);
		vo.setAfterFines(BigDecimal.ZERO);
		vo.setReportLossReturn(BigDecimal.ZERO);
		vo.setForwardingCharges(BigDecimal.ZERO);
		vo.setReturnOutboundAmount(BigDecimal.ZERO);
		return vo;
	}

	@Override
	public SupplierProductReconciliationPageVO querySupplierProductReconciliation(SupplierProductReconciliationQueryDTO queryDTO, Query query) {
		// 1. 参数校验和准备
		if (queryDTO.getStartTime() == null || queryDTO.getEndTime() == null) {
			throw new ServiceException("开始时间和结束时间均不能为空");
		}
		// 获取所有需要查询的供应商，如果前端未传入，则查询全部
		List<SupplierEntity> suppliers = Func.isNotEmpty(queryDTO.getSupplierIds())
			? supplierService.listByIds(queryDTO.getSupplierIds())
			: supplierService.list(Wrappers.<SupplierEntity>lambdaQuery().eq(SupplierEntity::getStatus, BusinessConstant.ENABLE_STATUS));

		if (suppliers.isEmpty()) {
			return new SupplierProductReconciliationPageVO();
		}
		Map<Long, SupplierEntity> supplierMap = suppliers.stream().collect(Collectors.toMap(SupplierEntity::getId, Function.identity()));

		// 2. 创建内存聚合Map
		Map<String, SupplierProductReconciliationVO> resultMap = new HashMap<>();

		//获取所有需要查询的商品，也可以通过前端传过来的名称判断
		List<ProductEntity> products = Func.isNotEmpty(queryDTO.getProductName())?productService.list(new LambdaQueryWrapper<ProductEntity>()
			.like(ProductEntity::getName, queryDTO.getProductName())):productService.list();
		Map<Long, ProductEntity> productMap = products.stream().collect(Collectors.toMap(ProductEntity::getId, Function.identity()));
		queryDTO.setProductIds(products.stream().map(ProductEntity::getId).collect(Collectors.toList())) ;

		// 3. 按商品维度聚合数据
		processSupplyAndSettlementForProductQuery(resultMap, supplierMap, queryDTO);
		processAfterSalesForProductQuery(resultMap, supplierMap, queryDTO);
		processReportLossForProductQuery(resultMap, supplierMap, queryDTO);
		processReturnOutboundForProductQuery(resultMap, supplierMap, queryDTO);

		List<SupplierProductReconciliationVO> list = new ArrayList<>(resultMap.values());
		// 按日期、供应商、商品排序
		list.sort(Comparator.comparing(SupplierProductReconciliationVO::getDate)
			.thenComparing(SupplierProductReconciliationVO::getSupplierId)
			.thenComparing(SupplierProductReconciliationVO::getProductId, Comparator.nullsLast(Comparator.naturalOrder()))
			.thenComparing(SupplierProductReconciliationVO::getSkuId, Comparator.nullsLast(Comparator.naturalOrder())));


		// 4. 计算总计行
		RealtimeReconciliationVO summary = calculateSummary(new ArrayList<>(list));

		// 5. 手动内存分页
		IPage<SupplierProductReconciliationVO> page = manualPaginate(list, query);

		// 6. 组装并返回结果
		SupplierProductReconciliationPageVO result = new SupplierProductReconciliationPageVO();
		//查询商品名称
		if(Func.isNotEmpty(page) &&Func.isNotEmpty(page.getRecords())&&page.getRecords().size()>0){
			page.getRecords().forEach(vo -> {
				ProductEntity  product=productMap.get(vo.getProductId());
				if(Func.isNotEmpty(product)){
					vo.setProductName(product.getName());
				}
			});
		}
		result.setPage(page);
		result.setSummary(summary);
		return result;
	}


	/**
	 * [辅助方法] 按商品聚合 - 处理供货、结算、转运费
	 */
	private void processSupplyAndSettlementForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto) {
		// 1. 根据商品名模糊查询sku
		List<Long> skuIds = new ArrayList<>();
		if (Func.isNotEmpty(dto.getProductIds())) {
			List<SkuStockEntity> skuStocks = skuStockService.list(Wrappers.<SkuStockEntity>lambdaQuery()
				.in(SkuStockEntity::getProductId, dto.getProductIds()));
			if (skuStocks.isEmpty()) {
				return; // 没有匹配的商品，直接返回
			}
			skuIds = skuStocks.stream().map(SkuStockEntity::getId).collect(Collectors.toList());
		}

		// 2. 处理供货总额 - 查询备货单和明细
		processSupplyAmountForProductQuery(resultMap, supplierMap, dto, skuIds);

		// 3. 处理结算金额 - 查询入库明细
		processSettlementAmountForProductQuery(resultMap, supplierMap, dto, skuIds);

		// 4. 处理转运费
		processForwardingChargesForProductQuery(resultMap, supplierMap, dto);
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理供货总额
	 */
	private void processSupplyAmountForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto, List<Long> skuIds) {
		// 1. 查询备货单
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
			.in(Func.isNotEmpty(dto.getSupplierIds()), ReplenishmentOrderEntity::getSupplierId, dto.getSupplierIds())
			.between(ReplenishmentOrderEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
			.eq(Func.isNotEmpty(dto.getWarehouseId()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseId())
			.in(Func.isNotEmpty(dto.getWarehouseIds()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseIds())
		);
		if (orders.isEmpty()) return;

		// 2. 查询备货单明细
		List<Long> orderIds = orders.stream().map(ReplenishmentOrderEntity::getId).collect(Collectors.toList());
		LambdaQueryWrapper<ReplenishmentOrderItemEntity> itemWrapper = Wrappers.<ReplenishmentOrderItemEntity>lambdaQuery()
			.in(ReplenishmentOrderItemEntity::getReplenishmentOrderId, orderIds);

		// 如果有SKU过滤条件，则添加过滤
		if (Func.isNotEmpty(skuIds)) {
			itemWrapper.in(ReplenishmentOrderItemEntity::getProductSkuId, skuIds);
		}

		List<ReplenishmentOrderItemEntity> items = replenishmentOrderItemService.list(itemWrapper);
		if (items.isEmpty()) return;

		// 3. 按订单分组明细
		Map<Long, List<ReplenishmentOrderItemEntity>> itemsByOrder = items.stream()
			.collect(Collectors.groupingBy(ReplenishmentOrderItemEntity::getReplenishmentOrderId));

		// 4. 获取SKU信息
		Map<Long, SkuStockEntity> skuStockMap = skuStockService.listByIds(
			items.stream().map(ReplenishmentOrderItemEntity::getProductSkuId).distinct().toList()
		).stream().collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));

		// 5. 聚合计算供货总额
		for (ReplenishmentOrderEntity order : orders) {
			List<ReplenishmentOrderItemEntity> orderItems = itemsByOrder.getOrDefault(order.getId(), Collections.emptyList());
			if (orderItems.isEmpty()) continue;

			LocalDate date = order.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

			// 按商品和SKU分组计算
			for (ReplenishmentOrderItemEntity item : orderItems) {
				SkuStockEntity skuStock = skuStockMap.get(item.getProductSkuId());
				if (skuStock == null) continue;

				String key = buildProductMapKey(date, order.getSupplierId(), skuStock.getProductId(), item.getProductSkuId());

				SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k ->
					createEmptyProductVO(date, order.getSupplierId(), skuStock.getProductId(), item.getProductSkuId(), supplierMap, skuStockMap));

				// 计算商品金额：单价 * 重量
				BigDecimal unitPrice = Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO);
				BigDecimal weight = Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO);
				BigDecimal productAmount = unitPrice.multiply(weight);

				// 计算配套运输金额：数量 * 单价
				Integer supportNum = Optional.ofNullable(item.getSupportTransNum()).orElse(0);
				BigDecimal supportPrice = Optional.ofNullable(item.getSupportTransPrice()).orElse(BigDecimal.ZERO);
				BigDecimal supportAmount = BigDecimal.ZERO;
				if (supportNum > 0 && !supportPrice.equals(BigDecimal.ZERO)) {
					supportAmount = new BigDecimal(supportNum).multiply(supportPrice);
				}

				BigDecimal supplyAmount = productAmount.add(supportAmount);

				// 累加供货金额
				vo.setSupplyAmount(vo.getSupplyAmount().add(supplyAmount).setScale(2, RoundingMode.HALF_UP));
			}
		}
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理结算金额
	 */
	private void processSettlementAmountForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto, List<Long> skuIds) {
		// 查询入库明细
		LambdaQueryWrapper<WarehouseStoreItemEntity> itemWrapper = Wrappers.<WarehouseStoreItemEntity>lambdaQuery()
			.in(Func.isNotEmpty(dto.getSupplierIds()), WarehouseStoreItemEntity::getSupplierId, dto.getSupplierIds())
			.in(Func.isNotEmpty(skuIds), WarehouseStoreItemEntity::getProductSkuId, skuIds)
			.between(WarehouseStoreItemEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(LocalTime.MAX));
		if (Func.isNotEmpty(dto.getWarehouseId())) {
			itemWrapper.eq(WarehouseStoreItemEntity::getWarehouseId, dto.getWarehouseId());
		}
		if(Func.isNotEmpty(dto.getWarehouseIds())){
			itemWrapper.in(WarehouseStoreItemEntity::getWarehouseId, dto.getWarehouseIds());
		}
		List<WarehouseStoreItemEntity> storeItems = warehouseStoreItemService.list(itemWrapper);
		if (storeItems.isEmpty()) return;

		// 批量获取关联信息
		List<Long> storeIds = storeItems.stream().map(WarehouseStoreItemEntity::getWarehouseStoreId).distinct().toList();
		Map<Long, WarehouseStoreEntity> storeMap = warehouseStoreService.listByIds(storeIds).stream().collect(Collectors.toMap(WarehouseStoreEntity::getId, Function.identity()));
		Map<Long, SkuStockEntity> skuStockMap = skuStockService.listByIds(storeItems.stream().map(WarehouseStoreItemEntity::getProductSkuId).distinct().toList()).stream().collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));

		// 聚合计算结算金额
		for (WarehouseStoreItemEntity item : storeItems) {
			WarehouseStoreEntity store = storeMap.get(item.getWarehouseStoreId());
			if (store == null) continue;

			LocalDate date = store.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildProductMapKey(date, item.getSupplierId(), item.getProductId(), item.getProductSkuId());

			SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k -> createEmptyProductVO(date, item, supplierMap, skuStockMap));

			// 累加结算金额
			BigDecimal settlementAmount = calculateDailySettlementAmount(Collections.singletonList(item));
			vo.setSettlementAmount(vo.getSettlementAmount().add(settlementAmount));
		}
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理转运费
	 */
	private void processForwardingChargesForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto) {
		// 查询备货单获取备货单号
		List<ReplenishmentOrderEntity> orders = replenishmentOrderService.list(Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
			.in(Func.isNotEmpty(dto.getSupplierIds()), ReplenishmentOrderEntity::getSupplierId, dto.getSupplierIds())
			.between(ReplenishmentOrderEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(23, 59, 59))
			.eq(Func.isNotEmpty(dto.getWarehouseId()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseId())
			.in(Func.isNotEmpty(dto.getWarehouseIds()), ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseIds())
		);
		if (orders.isEmpty()) return;

		// 获取转运费
		List<String> replenishmentNos = orders.stream().map(ReplenishmentOrderEntity::getReplenishmentNo).distinct().toList();
		List<TransportFeeEntity> transportFees = transportFeeService.list(Wrappers.<TransportFeeEntity>lambdaQuery()
			.in(TransportFeeEntity::getReplenishmentNo, replenishmentNos));

		Map<String, BigDecimal> forwardingChargesByReplenishmentNo = transportFees.stream()
			.collect(Collectors.toMap(TransportFeeEntity::getReplenishmentNo,
				fee -> CommonUtil.ConvertIntBigDecimal(fee.getAmount()), // 金额单位是分，转为元
				BigDecimal::add)); // 如果有重复的单号，则累加

		// 由于转运费是按备货单计算的，无法精确分配到具体商品，这里暂时跳过
		// 如果需要分配转运费到商品，需要根据业务规则进行分摊
		// 例如：按商品金额比例分摊、按重量比例分摊等
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理售后
	 */
	private void processAfterSalesForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto) {
		// ... 逻辑与 processAfterSalesForRealtimeQuery 类似, 但需要按 skuId 分组
		// 1. 根据商品名模糊查询sku
		List<Long> skuIds = new ArrayList<>();
		if (Func.isNotEmpty(dto.getProductIds())) {
			List<SkuStockEntity> skuStocks = skuStockService.list(Wrappers.<SkuStockEntity>lambdaQuery()
				.in(SkuStockEntity::getProductId, dto.getProductIds()));
			if (skuStocks.isEmpty()) {
				return; // 没有匹配的商品，直接返回
			}
			skuIds = skuStocks.stream().map(SkuStockEntity::getId).collect(Collectors.toList());
		}

		// 2. 查询售后子项
		LambdaQueryWrapper<OrderAfterSalesServiceItemEntity> itemWrapper = Wrappers.lambdaQuery();
		if (Func.isNotEmpty(skuIds)) {
			itemWrapper.in(OrderAfterSalesServiceItemEntity::getSkuId, skuIds);
		}
		List<OrderAfterSalesServiceItemEntity> afterSalesItems = orderAfterSalesServiceItemService.list(itemWrapper);
		if (afterSalesItems.isEmpty()) return;

		// 3. 查询售后主表、供应商扣款、罚款
		List<Long> afterSalesIds = afterSalesItems.stream().map(OrderAfterSalesServiceItemEntity::getAfterSalesId).distinct().toList();
		LambdaQueryWrapper<OrderAfterSalesServiceEntity> serviceWrapper = Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery()
			.in(OrderAfterSalesServiceEntity::getId, afterSalesIds)
			.between(OrderAfterSalesServiceEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(LocalTime.MAX));
		if (Func.isNotEmpty(dto.getWarehouseId())) {
			serviceWrapper.eq(OrderAfterSalesServiceEntity::getWarehouseId, dto.getWarehouseId());
		}
		if (Func.isNotEmpty(dto.getWarehouseIds())) {
			serviceWrapper.in(OrderAfterSalesServiceEntity::getWarehouseId, dto.getWarehouseIds());
		}
		Map<Long, OrderAfterSalesServiceEntity> serviceMap = orderAfterSalesService.list(serviceWrapper)
			.stream().collect(Collectors.toMap(OrderAfterSalesServiceEntity::getId, Function.identity()));

		List<Long> itemIds = afterSalesItems.stream().map(OrderAfterSalesServiceItemEntity::getId).collect(Collectors.toList());
		List<OrderAfterSalesServiceItemSupplierEntity> deductions = orderAfterSalesServiceItemSupplierService.list(Wrappers.<OrderAfterSalesServiceItemSupplierEntity>lambdaQuery().in(OrderAfterSalesServiceItemSupplierEntity::getAfterSalesItemId, itemIds).in(Func.isNotEmpty(dto.getSupplierIds()), OrderAfterSalesServiceItemSupplierEntity::getSupplierId, dto.getSupplierIds()));
		List<OrderAfterSalesServiceItemMoneyEntity> fines = orderAfterSalesServiceItemMoneyService.list(Wrappers.<OrderAfterSalesServiceItemMoneyEntity>lambdaQuery().in(OrderAfterSalesServiceItemMoneyEntity::getAfterSalesItemId, itemIds).in(Func.isNotEmpty(dto.getSupplierIds()), OrderAfterSalesServiceItemMoneyEntity::getSupplierId, dto.getSupplierIds()));
		Map<Long, SkuStockEntity> skuStockMap = skuStockService.listByIds(afterSalesItems.stream().map(OrderAfterSalesServiceItemEntity::getSkuId).distinct().toList()).stream().collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));


		Map<Long, OrderAfterSalesServiceItemEntity> afterSalesItemMap = afterSalesItems.stream().collect(Collectors.toMap(OrderAfterSalesServiceItemEntity::getId, Function.identity()));

		// 4. 聚合扣款
		for (OrderAfterSalesServiceItemSupplierEntity deduction : deductions) {
			OrderAfterSalesServiceItemEntity item = afterSalesItemMap.get(deduction.getAfterSalesItemId());
			OrderAfterSalesServiceEntity service = serviceMap.get(item.getAfterSalesId());
			if (service == null) continue;

			// 获取SKU信息来获取正确的商品ID
			SkuStockEntity skuStock = skuStockMap.get(item.getSkuId());
			if (skuStock == null) continue;

			LocalDate date = service.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildProductMapKey(date, deduction.getSupplierId(), skuStock.getProductId(), item.getSkuId());
			SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k -> createEmptyProductVO(date, deduction.getSupplierId(), skuStock.getProductId(), item.getSkuId(), supplierMap, skuStockMap));
			BigDecimal amount = CommonUtil.ConvertIntBigDecimal(deduction.getFinanceAmount());
			vo.setAfterDeduction(vo.getAfterDeduction().add(Optional.ofNullable(amount).orElse(BigDecimal.ZERO)));
		}

		// 5. 聚合罚款
		for (OrderAfterSalesServiceItemMoneyEntity fine : fines) {
			OrderAfterSalesServiceItemEntity item = afterSalesItemMap.get(fine.getAfterSalesItemId());
			OrderAfterSalesServiceEntity service = serviceMap.get(item.getAfterSalesId());
			if (service == null) continue;

			// 获取SKU信息来获取正确的商品ID
			SkuStockEntity skuStock = skuStockMap.get(item.getSkuId());
			if (skuStock == null) continue;

			LocalDate date = service.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildProductMapKey(date, fine.getSupplierId(), skuStock.getProductId(), item.getSkuId());
			SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k -> createEmptyProductVO(date, fine.getSupplierId(), skuStock.getProductId(), item.getSkuId(), supplierMap, skuStockMap));
			BigDecimal amount = CommonUtil.ConvertIntBigDecimal(fine.getFinanceAmount());
			vo.setAfterFines(vo.getAfterFines().add(Optional.ofNullable(amount).orElse(BigDecimal.ZERO)));
		}
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理报损
	 */
	private void processReportLossForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto) {
		// ... 逻辑与 processReportLossForRealtimeQuery 类似，但需要按 skuId 分组
		// 1. 根据商品名模糊查询sku
		List<Long> skuIds = new ArrayList<>();
		if (Func.isNotEmpty(dto.getProductIds())) {
			List<SkuStockEntity> skuStocks = skuStockService.list(Wrappers.<SkuStockEntity>lambdaQuery()
				.in(SkuStockEntity::getProductId, dto.getProductIds()));
			if (skuStocks.isEmpty()) {
				return; // 没有匹配的商品，直接返回
			}
			skuIds = skuStocks.stream().map(SkuStockEntity::getId).collect(Collectors.toList());
		}

		// 2. 查出库主表
		LambdaQueryWrapper<WarehouseOutboundEntity> outboundWrapper = Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF_BS.getCode())
			.between(WarehouseOutboundEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(LocalTime.MAX));
		if (Func.isNotEmpty(dto.getWarehouseId())) {
			outboundWrapper.eq(WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseId());
		}
		if (Func.isNotEmpty(dto.getWarehouseIds())) {
			outboundWrapper.in(WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseIds());
		}

		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(outboundWrapper);
		if (outbounds.isEmpty()) return;
		Map<Long, WarehouseOutboundEntity> outboundMap = outbounds.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, Function.identity()));
		List<Long> outboundIds = new ArrayList<>(outboundMap.keySet());


		// 3. 查出库明细
		LambdaQueryWrapper<WarehouseOutboundItemEntity> itemWrapper = Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
			.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
			.in(Func.isNotEmpty(dto.getSupplierIds()), WarehouseOutboundItemEntity::getSupplierId, dto.getSupplierIds());
		if (Func.isNotEmpty(skuIds)) {
			itemWrapper.in(WarehouseOutboundItemEntity::getProductSkuId, skuIds);
		}
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(itemWrapper);
		Map<Long, SkuStockEntity> skuStockMap = skuStockService.listByIds(outboundItems.stream().map(WarehouseOutboundItemEntity::getProductSkuId).distinct().toList()).stream().collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));


		// 4. 聚合
		for (WarehouseOutboundItemEntity item : outboundItems) {
			WarehouseOutboundEntity outbound = outboundMap.get(item.getWarehouseOutboundId());
			LocalDate date = outbound.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildProductMapKey(date, item.getSupplierId(), item.getProductId(), item.getProductSkuId());
			SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k -> createEmptyProductVO(date, item.getSupplierId(), item.getProductId(), item.getProductSkuId(), supplierMap, skuStockMap));

			BigDecimal amount = (item.getWeight() != null ? item.getWeight() : BigDecimal.ZERO)
				.multiply(item.getProductSalePrice() != null ? CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice()) : BigDecimal.ZERO);
			vo.setReportLossReturn(vo.getReportLossReturn().add(amount));
		}
	}

	/**
	 * [辅助方法] 按商品聚合 - 处理退货出库
	 */
	private void processReturnOutboundForProductQuery(Map<String, SupplierProductReconciliationVO> resultMap, Map<Long, SupplierEntity> supplierMap, SupplierProductReconciliationQueryDTO dto) {
		// ... 逻辑与 processReturnAmountForRealtimeQuery 类似，但需要按 skuId 分组
		// 1. 根据商品名模糊查询sku
		List<Long> skuIds = new ArrayList<>();
		if (Func.isNotEmpty(dto.getProductIds())) {
			List<SkuStockEntity> skuStocks = skuStockService.list(Wrappers.<SkuStockEntity>lambdaQuery()
				.in(SkuStockEntity::getProductId, dto.getProductIds()));
			if (skuStocks.isEmpty()) {
				return; // 没有匹配的商品，直接返回
			}
			skuIds = skuStocks.stream().map(SkuStockEntity::getId).collect(Collectors.toList());
		}

		// 2. 查出库主表
		LambdaQueryWrapper<WarehouseOutboundEntity> outboundWrapper = Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.RETURN.getCode())
			.between(WarehouseOutboundEntity::getCreateTime, dto.getStartTime().atStartOfDay(), dto.getEndTime().atTime(LocalTime.MAX));
		if (Func.isNotEmpty(dto.getWarehouseId())) {
			outboundWrapper.eq(WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseId());
		}
		if (Func.isNotEmpty(dto.getWarehouseIds())) {
			outboundWrapper.in(WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseIds());
		}

		List<WarehouseOutboundEntity> outbounds = warehouseOutboundService.list(outboundWrapper);
		if (outbounds.isEmpty()) return;
		Map<Long, WarehouseOutboundEntity> outboundMap = outbounds.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, Function.identity()));
		List<Long> outboundIds = new ArrayList<>(outboundMap.keySet());


		// 3. 查出库明细
		LambdaQueryWrapper<WarehouseOutboundItemEntity> itemWrapper = Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
			.in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
			.in(Func.isNotEmpty(dto.getSupplierIds()), WarehouseOutboundItemEntity::getSupplierId, dto.getSupplierIds());
		if (Func.isNotEmpty(skuIds)) {
			itemWrapper.in(WarehouseOutboundItemEntity::getProductSkuId, skuIds);
		}
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(itemWrapper);
		Map<Long, SkuStockEntity> skuStockMap = skuStockService.listByIds(outboundItems.stream().map(WarehouseOutboundItemEntity::getProductSkuId).distinct().toList()).stream().collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));

		// 4. 聚合
		for (WarehouseOutboundItemEntity item : outboundItems) {
			WarehouseOutboundEntity outbound = outboundMap.get(item.getWarehouseOutboundId());
			LocalDate date = outbound.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			String key = buildProductMapKey(date, item.getSupplierId(), item.getProductId(), item.getProductSkuId());
			SupplierProductReconciliationVO vo = resultMap.computeIfAbsent(key, k -> createEmptyProductVO(date, item.getSupplierId(), item.getProductId(), item.getProductSkuId(), supplierMap, skuStockMap));

			BigDecimal amount = (item.getWeight() != null ? item.getWeight() : BigDecimal.ZERO)
				.multiply(item.getProductSalePrice() != null ? CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice()) : BigDecimal.ZERO);
			vo.setReturnOutboundAmount(vo.getReturnOutboundAmount().add(amount));
		}

	}


	/**
	 * [辅助方法] 创建商品聚合Map的Key
	 */
	private String buildProductMapKey(LocalDate date, Long supplierId, Long productId, Long skuId) {
		return date.toString() + "_" + supplierId + "_" + productId + "_" + skuId;
	}

	/**
	 * [辅助方法] 创建一个空的带商品信息的VO
	 */
	private SupplierProductReconciliationVO createEmptyProductVO(LocalDate date, WarehouseStoreItemEntity item, Map<Long, SupplierEntity> supplierMap, Map<Long, SkuStockEntity> skuStockMap) {
		SupplierProductReconciliationVO vo = new SupplierProductReconciliationVO();
		vo.setDate(date);

		SupplierEntity supplier = supplierMap.get(item.getSupplierId());
		if (supplier != null) {
			vo.setSupplierId(supplier.getId());
			vo.setSupplierName(supplier.getFullName());
			vo.setSupplierPhone(supplier.getPhone());
		}

		SkuStockEntity skuStock = skuStockMap.get(item.getProductSkuId());
		if (skuStock != null) {
			vo.setProductId(skuStock.getProductId());
			vo.setSkuId(skuStock.getId());
			vo.setSkuCode(skuStock.getSkuCode());
			vo.setSpData(skuStock.getSpData());
		}

		// 初始化所有金额字段为0
		vo.setSupplyAmount(BigDecimal.ZERO);
		vo.setSettlementAmount(BigDecimal.ZERO);
		vo.setAfterDeduction(BigDecimal.ZERO);
		vo.setAfterFines(BigDecimal.ZERO);
		vo.setReportLossReturn(BigDecimal.ZERO);
		vo.setForwardingCharges(BigDecimal.ZERO);
		vo.setReturnOutboundAmount(BigDecimal.ZERO);

		return vo;
	}

	/**
	 * [辅助方法] 创建一个空的带商品信息的VO for AfterSales
	 */
	private SupplierProductReconciliationVO createEmptyProductVO(LocalDate date, Long supplierId, Long productId, Long skuId, Map<Long, SupplierEntity> supplierMap, Map<Long, SkuStockEntity> skuStockMap) {
		SupplierProductReconciliationVO vo = new SupplierProductReconciliationVO();
		vo.setDate(date);

		SupplierEntity supplier = supplierMap.get(supplierId);
		if (supplier != null) {
			vo.setSupplierId(supplier.getId());
			vo.setSupplierName(supplier.getFullName());
			vo.setSupplierPhone(supplier.getPhone());
		}

		SkuStockEntity skuStock = skuStockMap.get(skuId);
		if (skuStock != null) {
			vo.setProductId(skuStock.getProductId());
			vo.setSkuId(skuStock.getId());
			vo.setSkuCode(skuStock.getSkuCode());
			vo.setSpData(skuStock.getSpData());
		}

		// 初始化所有金额字段为0
		vo.setSupplyAmount(BigDecimal.ZERO);
		vo.setSettlementAmount(BigDecimal.ZERO);
		vo.setAfterDeduction(BigDecimal.ZERO);
		vo.setAfterFines(BigDecimal.ZERO);
		vo.setReportLossReturn(BigDecimal.ZERO);
		vo.setForwardingCharges(BigDecimal.ZERO);
		vo.setReturnOutboundAmount(BigDecimal.ZERO);

		return vo;
	}

	/**
	 * [辅助方法] 对列表进行手动内存分页 (泛型)
	 */
	private <T> IPage<T> manualPaginate(List<T> fullList, Query query) {
		long current = (query.getCurrent() == null) ? 1L : query.getCurrent();
		long size = (query.getSize() == null) ? 10L : query.getSize();

		Page<T> page = new Page<>(current, size);
		page.setTotal(fullList.size());
		int fromIndex = (int) page.offset();
		int toIndex = (int) Math.min(fromIndex + page.getSize(), fullList.size());
		if (fromIndex >= fullList.size()) { // 修复边界条件
			page.setRecords(Collections.emptyList());
		} else {
			page.setRecords(fullList.subList(fromIndex, toIndex));
		}
		return page;
	}

	/**
	 * [辅助方法] 计算总计行
	 */
	private RealtimeReconciliationVO calculateSummary(List<? extends RealtimeReconciliationVO> list) {
		if (list.isEmpty()) {
			return new RealtimeReconciliationVO();
		}
		RealtimeReconciliationVO summary = new RealtimeReconciliationVO();
		summary.setSupplyAmount(list.stream().map(RealtimeReconciliationVO::getSupplyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setSettlementAmount(list.stream().map(RealtimeReconciliationVO::getSettlementAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setAfterDeduction(list.stream().map(RealtimeReconciliationVO::getAfterDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setAfterFines(list.stream().map(RealtimeReconciliationVO::getAfterFines).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setReportLossReturn(list.stream().map(RealtimeReconciliationVO::getReportLossReturn).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setForwardingCharges(list.stream().map(RealtimeReconciliationVO::getForwardingCharges).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		summary.setReturnOutboundAmount(list.stream().map(RealtimeReconciliationVO::getReturnOutboundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
		return summary;
	}
}
