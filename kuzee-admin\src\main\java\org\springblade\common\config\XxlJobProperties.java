//package org.springblade.common.config;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
///**
// * xxl-job 配置参数
// */
//@Getter
//@Setter
//@ConfigurationProperties(prefix = "xxl.job")
//public class XxlJobProperties {
//
//	private Admin admin;
//	private Executor executor;
//	private String accessToken;
//
//	@Getter
//	@Setter
//	public static class Admin {
//		private String addresses;
//	}
//
//	@Getter
//	@Setter
//	public static class Executor {
//		private String appname;
//		private String ip;
//		private int port;
//		private String logpath;
//		private int logretentiondays;
//	}
//}
