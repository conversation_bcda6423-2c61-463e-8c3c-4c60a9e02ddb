<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.warehouse.mapper.TransportOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="transportOrderResultMap"
               type="org.springblade.modules.business.warehouse.pojo.entity.TransportOrderEntity">
        <result column="quantity" property="quantity"/>
        <result column="actual_quantity" property="actualQuantity"/>
        <result column="create_time_at" property="createTimeAt"/>
        <result column="transport_no" property="transportNo"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="note" property="note"/>
        <result column="transport_warehouse_id" property="transportWarehouseId"/>
    </resultMap>

    <select id="selectTransportOrderPage" resultType="org.springblade.modules.business.warehouse.pojo.vo.TransportOrderPageVO">
        SELECT o.id,o.transport_no,
        o.warehouse_id,
        o.transport_warehouse_id,
        o.quantity,
        o.weight,
        o.transport_out_time,
        o.update_time,
        o.status
        FROM chy_transport_order o
        WHERE o.is_deleted = 0
        <choose>
            <when test="ew.warehouseId != null">
                AND o.warehouse_id = #{ew.warehouseId}
            </when>
            <otherwise>
                AND o.transport_warehouse_id = #{ew.transportWarehouseId}
            </otherwise>
        </choose>
        <if test="ew.transportNo != null and ew.transportNo != ''">
            AND o.transport_no = #{ew.transportNo}
        </if>
    </select>

    <select id="exportTransportOrder" resultType="org.springblade.modules.business.warehouse.excel.TransportOrderExcel">
        SELECT *
        FROM chy_transport_order ${ew.customSqlSegment}
    </select>

    <select id="getOutboundList" resultType="org.springblade.common.pojo.vo.PrintOutboundVO">
        select o.product_name,t.transport_warehouse_id as warehouseId,w.warehouse_name, SUM(o.quantity) as totalOrder  from chy_transport_order t
        LEFT JOIN chy_transport_order_item o on t.id=o.transport_order_id
        LEFT JOIN chy_warehouse w on w.id=t.transport_warehouse_id
        where t.is_deleted=0 and o.is_deleted=0 and DATE(t.create_time)=DATE(#{dto.screeningTime}) and t.warehouse_id=#{dto.warehouseId}
        GROUP BY t.transport_warehouse_id,o.product_name
    </select>

    <select id="printOutWarechouse" resultType="org.springblade.common.pojo.vo.PrintOutboundVO">
        select o.product_name,t.transport_warehouse_id as warehouseId,w.warehouse_name, SUM(o.quantity) as totalOrder  from chy_transport_order t
        LEFT JOIN chy_transport_order_item o on t.id=o.transport_order_id
        LEFT JOIN chy_warehouse w on w.id=t.transport_warehouse_id
        where t.is_deleted=0 and o.is_deleted=0 AND (t.create_time_at BETWEEN #{dto.startTime} AND #{dto.endTime})
        and t.warehouse_id=#{dto.warehouseId}
        GROUP BY t.transport_warehouse_id,o.product_name
    </select>
    <select id="getReportLossDetails" resultType="org.springblade.modules.business.warehouse.pojo.vo.ReportLossDetailsVO">
        select t.create_time as reportLossTime,p.`name` as productName,i.sp_data as spData,u.unit_name as unitName,
        p.is_standard as isStandard ,i.weight ,i.quantity ,o.transport_no as transportNo,i.product_sku_id as skuId,
        i.product_sale_price as productSalePrice,t.id from  chy_warehouse_outbound t
        LEFT JOIN chy_warehouse_outbound_item i on t.id = i.warehouse_outbound_id
        LEFT JOIN chy_sku_stock st on st.id=i.product_sku_id
        LEFT JOIN chy_base_unit u on st.base_unit_id = u.id
        LEFT JOIN chy_product p on p.id = st.product_id
        LEFT JOIN chy_transport_order o on t.related_order_id=o.id
        where  t.is_deleted=0 and i.is_deleted=0  and DATE(i.create_time) = DATE(#{dto.reportLossTime})
        and t.biz_type=10 and i.supplier_id=#{dto.supplierId}
        <if test="dto.warehouseId != null">
            and t.warehouse_id= #{dto.warehouseId}
        </if>
        <if test="dto.productSkuId != null">
            and i.product_sku_id= #{dto.productSkuId}
        </if>
    </select>
    <select id="getReportLossTranDetails"
            resultType="org.springblade.modules.business.warehouse.pojo.vo.ReportLossDetailsVO">
        select t.create_time as reportLossTime, i.product_sku_id as skuId,t.id,
        i.support_trans_price ,i.support_trans_num,i.support_trans_unit_id,ut.transport_name as supportTrans,
        p.`name` as productName,st.sp_data as spData,u.unit_name as unitName,
        p.is_standard as isStandard , o.transport_no as transportNo
        from  chy_warehouse_outbound t
        LEFT JOIN chy_warehouse_support_trans i on t.id = i.related_record_id
        LEFT JOIN chy_sku_stock st on st.id=i.product_sku_id
        LEFT JOIN chy_base_unit u on st.base_unit_id = u.id
        LEFT JOIN chy_product p on p.id = st.product_id
        LEFT JOIN chy_transport_order o on t.related_order_id=o.id
        LEFT JOIN chy_transport_unit ut on i.support_trans_unit_id = ut.id
        where  t.is_deleted=0 and i.is_deleted=0  and t.biz_type=10  and DATE(i.create_time) = DATE(#{dto.reportLossTime})
        and i.supplier_id=#{dto.supplierId}
        <if test="dto.warehouseId != null">
            and t.warehouse_id= #{dto.warehouseId}
        </if>
        <if test="dto.productSkuId != null">
            and i.product_sku_id= #{dto.productSkuId}
        </if>
    </select>

</mapper>
