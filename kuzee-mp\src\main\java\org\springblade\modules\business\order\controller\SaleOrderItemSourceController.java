/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.business.order.pojo.entity.SaleOrderItemSourceEntity;
import org.springblade.modules.business.order.pojo.vo.SaleOrderItemSourceVO;
import org.springblade.modules.business.order.excel.SaleOrderItemSourceExcel;
import org.springblade.modules.business.order.wrapper.SaleOrderItemSourceWrapper;
import org.springblade.modules.business.order.service.ISaleOrderItemSourceService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 销售订单详情原始数据表-支付成功时候的数据 控制器
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/saleOrderItemSource")
@Tag(name = "销售订单详情原始数据表-支付成功时候的数据", description = "销售订单详情原始数据表-支付成功时候的数据接口")
public class SaleOrderItemSourceController extends BladeController {

	private final ISaleOrderItemSourceService saleOrderItemSourceService;

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入saleOrderItemSource")
	public R<SaleOrderItemSourceVO> detail(SaleOrderItemSourceEntity saleOrderItemSource) {
		SaleOrderItemSourceEntity detail = saleOrderItemSourceService.getOne(Condition.getQueryWrapper(saleOrderItemSource));
		return R.data(SaleOrderItemSourceWrapper.build().entityVO(detail));
	}

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入saleOrderItemSource")
	public R<IPage<SaleOrderItemSourceVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> saleOrderItemSource, Query query) {
		IPage<SaleOrderItemSourceEntity> pages = saleOrderItemSourceService.page(Condition.getPage(query), Condition.getQueryWrapper(saleOrderItemSource, SaleOrderItemSourceEntity.class));
		return R.data(SaleOrderItemSourceWrapper.build().pageVO(pages));
	}


	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入saleOrderItemSource")
	public R<IPage<SaleOrderItemSourceVO>> page(SaleOrderItemSourceVO saleOrderItemSource, Query query) {
		IPage<SaleOrderItemSourceVO> pages = saleOrderItemSourceService.selectSaleOrderItemSourcePage(Condition.getPage(query), saleOrderItemSource);
		return R.data(pages);
	}

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入saleOrderItemSource")
	public R save(@Valid @RequestBody SaleOrderItemSourceEntity saleOrderItemSource) {
		return R.status(saleOrderItemSourceService.save(saleOrderItemSource));
	}

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入saleOrderItemSource")
	public R update(@Valid @RequestBody SaleOrderItemSourceEntity saleOrderItemSource) {
		return R.status(saleOrderItemSourceService.updateById(saleOrderItemSource));
	}

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入saleOrderItemSource")
	public R submit(@Valid @RequestBody SaleOrderItemSourceEntity saleOrderItemSource) {
		return R.status(saleOrderItemSourceService.saveOrUpdate(saleOrderItemSource));
	}

	/**
	 * 销售订单详情原始数据表-支付成功时候的数据 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(saleOrderItemSourceService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-saleOrderItemSource")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "导出数据", description  = "传入saleOrderItemSource")
	public void exportSaleOrderItemSource(@Parameter(hidden = true) @RequestParam Map<String, Object> saleOrderItemSource, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SaleOrderItemSourceEntity> queryWrapper = Condition.getQueryWrapper(saleOrderItemSource, SaleOrderItemSourceEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SaleOrderItemSourceEntity::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SaleOrderItemSourceEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SaleOrderItemSourceExcel> list = saleOrderItemSourceService.exportSaleOrderItemSource(queryWrapper);
		ExcelUtil.export(response, "销售订单详情原始数据表-支付成功时候的数据数据" + DateUtil.time(), "销售订单详情原始数据表-支付成功时候的数据数据表", list, SaleOrderItemSourceExcel.class);
	}

}
