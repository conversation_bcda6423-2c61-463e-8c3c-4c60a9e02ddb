/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ProductGetCache;
import org.springblade.common.cache.RegionCache;
import org.springblade.common.cache.TransportUnitCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.config.CustomProperties;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.pdf.PDFServerUtil;
import org.springblade.common.pojo.vo.*;
import org.springblade.common.utills.CommonUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.DateTimeUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.message.enums.MessageJumpEnum;
import org.springblade.message.enums.MessageTypeEnum;
import org.springblade.modules.business.cust.mapper.CustMapper;
import org.springblade.modules.business.cust.pojo.entity.CustEntity;
import org.springblade.modules.business.cust.service.impl.MessageSenderService;
import org.springblade.modules.business.order.pojo.dto.BatchOutboundDTO;
import org.springblade.modules.business.order.pojo.dto.OrderProductInfo;
import org.springblade.modules.business.order.pojo.dto.OrderTransportInfo;
import org.springblade.modules.business.order.pojo.entity.OrderAfterSalesServiceEntity;
import org.springblade.modules.business.order.pojo.entity.SaleOrderEntity;
import org.springblade.modules.business.order.pojo.entity.SaleOrderItemEntity;
import org.springblade.modules.business.order.service.IOrderAfterSalesServiceService;
import org.springblade.modules.business.order.service.ISaleOrderItemService;
import org.springblade.modules.business.order.service.ISaleOrderService;
import org.springblade.modules.business.order.service.ISupplierService;
import org.springblade.modules.business.product.mapper.SkuStockMapper;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.pojo.entity.TransportUnitEntity;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.product.service.ISkuWarehouseRelationService;
import org.springblade.modules.business.product.service.ITransportUnitService;
import org.springblade.modules.business.warehouse.excel.WarehouseOutboundExcel;
import org.springblade.modules.business.warehouse.mapper.InventorySupportTransMapper;
import org.springblade.modules.business.warehouse.mapper.WarehouseOutboundMapper;
import org.springblade.modules.business.warehouse.pojo.dto.*;
import org.springblade.modules.business.warehouse.pojo.entity.*;
import org.springblade.modules.business.warehouse.pojo.vo.*;
import org.springblade.modules.business.warehouse.service.*;
import org.springblade.modules.business.warehouse.wrapper.WarehouseOutboundItemWrapper;
import org.springblade.modules.business.warehouse.wrapper.WarehouseOutboundWrapper;
import org.springblade.modules.system.mapper.UserMapper;
import org.springblade.modules.system.pojo.entity.Region;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 出库记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Slf4j
@Service
@AllArgsConstructor
public class WarehouseOutboundServiceImpl extends BaseServiceImpl<WarehouseOutboundMapper, WarehouseOutboundEntity> implements IWarehouseOutboundService {

	private final IWarehouseOutboundItemService warehouseOutboundItemService;
	private final IInventoryService inventoryService;
	private final IWarehouseSupportTransService warehouseSupportTransService;
	private final IInventorySupportTransService inventorySupportTransService;
	private final IProductService productService;
	private final IWarehouseService warehouseService;
	private final IWarehouseStoreService warehouseStoreService;
	private final ITransportOrderService transportOrderService;
	private final ISaleOrderService saleOrderService;
	private final IOrderAfterSalesServiceService orderAfterSalesServiceService;
	private final ISaleOrderItemService saleOrderItemService;
	private final IWarehouseStoreOutboundRelationService warehouseStoreOutboundRelationService;
	private final MessageSenderService messageSenderService;
	private final CustomProperties customProperties;
	private final ISkuStockService skuStockService;
	private final InventorySupportTransMapper inventorySupportTransMapper;
	private final CustMapper custMapper;
	private final UserMapper userMapper;
	private final ProductGetCache productGetCache;
	private final ISupplierService supplierService;
	private final ITransportUnitService transportUnitService;
	private final SkuStockMapper skuStockMapper;
	private final ISkuWarehouseRelationService skuWarehouseRelationService;

	@Override
	public WarehouseOutboundVO getDetail(Long outboundId) {
		WarehouseOutboundEntity entity = getById(outboundId);
		if (Func.isNull(entity)) {
			return null;
		}
		WarehouseOutboundVO vo = WarehouseOutboundWrapper.build().entityVO(entity);
		vo.setBizTypeName(OutboundBizTypeEnum.getNameByCode(entity.getBizType()));
		if (OutboundBizTypeEnum.relationStoreType(vo.getBizType())) {
			Map<Long, String> map = warehouseService.listNameByIds(Lists.newArrayList(vo.getRelatedSourceId()));
			vo.setRelatedSourceName(map.get(vo.getRelatedSourceId()));
		} else {
			if (Func.isNotEmpty(vo.getWarehouseId())) {
				Map<Long, String> map = warehouseService.listNameByIds(Lists.newArrayList(vo.getWarehouseId()));
				vo.setRelatedSourceName(map.get(vo.getWarehouseId()));
			}
		}
		if (Func.notNull(vo.getCompletionTime())) {
			vo.setUpdateUserName(UserCache.getUserName(entity.getUpdateUser()));
		}
		return vo;
	}

	@Override
	public IPage<WarehouseOutboundPageVO> selectWarehouseOutboundPage(IPage<WarehouseOutboundPageVO> page, WarehouseOutboundQueryDTO dto) {
		IPage<WarehouseOutboundPageVO> resultPage = page.setRecords(baseMapper.selectWarehouseOutboundPage(page, dto, OutboundBizTypeEnum.getExcludeBizTypes()));

		if (resultPage.getTotal() < 1) {
			return resultPage;
		}

		// 数据转换
		List<WarehouseOutboundPageVO> records = resultPage.getRecords();
		// 关联仓库及入库批次转换
		convertWarehouseName(records);
		// 商品信息转换
//		productService.assembleProductVO(records);
		return resultPage;
	}

	/**
	 * 获取差异出库记录
	 *
	 * @param relatedId 出库id
	 * @param skuId     商品SKU ID（可选）
	 * @return 出库记录
	 */
	@Override
	public WarehouseOutboundDiffVO diffList(Long relatedId, Long skuId) {
		// 1. 查询所有符合条件的出库记录
		List<WarehouseOutboundEntity> outboundEntityList = list(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getRelatedOrderId, relatedId)
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF.getCode())
			.eq(WarehouseOutboundEntity::getStatus, BusinessConstant.ENABLE_STATUS));

		if (Func.isEmpty(outboundEntityList)) {
			return null;
		}

		// 2. 批量获取所有相关数据
		List<Long> outboundIds = outboundEntityList.stream().map(WarehouseOutboundEntity::getId).collect(Collectors.toList());

		// 批量获取商品明细
		List<WarehouseOutboundItemEntity> itemEntities = warehouseOutboundItemService.list(
			Wrappers.<WarehouseOutboundItemEntity>lambdaQuery().in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds)
		);
		List<WarehouseOutboundItemVO> allItemsList = WarehouseOutboundItemWrapper.build().listVO(itemEntities);
		productService.assembleProductVO(allItemsList);

		// 批量获取聚合后的配套运输品信息
		List<WarehouseSupportTransVO> supportTransDetails = warehouseSupportTransService.getAggregatedByOutboundIds(outboundIds);

		// 3. 将数据转换为Map以便高效查找
		Map<Long, WarehouseOutboundEntity> outboundEntityMap = outboundEntityList.stream()
			.collect(Collectors.toMap(WarehouseOutboundEntity::getId, Function.identity()));

		// 配套运输品信息Map: Map<OutboundId, Map<SkuId, SupportTransVO>>
		Map<Long, Map<Long, WarehouseSupportTransVO>> supportTransMap = supportTransDetails.stream()
			.filter(vo -> vo.getProductSkuId() != null && vo.getRelatedRecordId() != null)
			.collect(Collectors.groupingBy(
				WarehouseSupportTransVO::getRelatedRecordId,
				Collectors.toMap(WarehouseSupportTransVO::getProductSkuId, Function.identity(), (a, b) -> a)
			));

		// 4. 遍历商品明细，为其填充父级信息和配套运输品信息
		for (WarehouseOutboundItemVO itemVO : allItemsList) {
			// 填充父级出库单信息
			WarehouseOutboundEntity parentOutbound = outboundEntityMap.get(itemVO.getWarehouseOutboundId());
			if (parentOutbound != null) {
				itemVO.setCompletionTime(parentOutbound.getCompletionTime());
				itemVO.setUpdateUser(parentOutbound.getUpdateUser());
				itemVO.setUpdateUserName(UserCache.getUserName(parentOutbound.getUpdateUser()));
			}

			// 填充配套运输品信息
			WarehouseSupportTransVO supportInfo = Optional.ofNullable(supportTransMap.get(itemVO.getWarehouseOutboundId()))
				.map(skuMap -> skuMap.get(itemVO.getProductSkuId()))
				.orElse(null);

			if (supportInfo != null) {
				itemVO.setSupportTransNum(supportInfo.getSupportTransNum());
				itemVO.setSupportTransUnitName(supportInfo.getSupportTransUnitName());
			}
			if (Func.isNotEmpty(itemVO.getSupplierId())) {
				Long supplierId = itemVO.getSupplierId();
				Map<Long, String> supplierMap = supplierService.listNameByIds(Collections.singletonList(supplierId));
				if (supplierMap != null && supplierMap.containsKey(supplierId)) {
					itemVO.setSupplierName(supplierMap.get(supplierId));
				}
			}
		}

		// 5. 根据传入的skuId（如果存在）进行最终过滤
		List<WarehouseOutboundItemVO> finalItemsList = allItemsList;
		if (Func.notNull(skuId)) {
			finalItemsList = allItemsList.stream()
				.filter(item -> skuId.equals(item.getProductSkuId()))
				.collect(Collectors.toList());
		}

		// 6. 组装并返回最终结果
		WarehouseOutboundDiffVO diffVO = new WarehouseOutboundDiffVO();
		// 使用最新的出库记录作为VO的抬头信息
		outboundEntityList.sort(Comparator.comparing(WarehouseOutboundEntity::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder())));
		WarehouseOutboundEntity latestOutbound = outboundEntityList.get(0);
		diffVO.setId(relatedId);
		diffVO.setOutboundNo(latestOutbound.getOutboundNo());
		diffVO.setCompletionTime(latestOutbound.getCompletionTime());
		diffVO.setUpdateUser(latestOutbound.getUpdateUser());
		diffVO.setUpdateUserName(UserCache.getUserName(latestOutbound.getUpdateUser()));
		diffVO.setWarehouseOutboundItemVOList(finalItemsList);

		return diffVO;
	}

	/**
	 * 关联仓库及入库批次转换
	 */
	private void convertWarehouseName(List<WarehouseOutboundPageVO> records) {
		List<Long> warehouseIdList = records.stream().map(WarehouseOutboundPageVO::getWarehouseId).toList();
		Map<Long, String> warehouseMap = warehouseService.listNameByIds(warehouseIdList);
		for (WarehouseOutboundPageVO record : records) {
			record.setBizTypeName(OutboundBizTypeEnum.getNameByCode(record.getBizType()));
			record.setWarehouseName(warehouseMap.get(record.getWarehouseId()));
			if (Func.notNull(record.getCompletionTime())) {
				record.setUpdateUserName(UserCache.getUserName(record.getUpdateUser()));
			}
			if (Func.notNull(record.getDeliveryCost())) {
				record.setDeliveryCost(CommonUtil.ConvertToBigDecimal(record.getDeliveryCost()));
			}
			record.setPayAmount(CommonUtil.ConvertToBigDecimal(record.getPayAmount()));
			if (Func.notNull(record.getPayType())) {
				record.setPayTypeName(PayTypeEnum.getNameByCode(record.getPayType()));
			}
			// 由于先前的逻辑临时销售出库将客户id和名称映射到RelatedOrderId和RelatedOrderNo字段，前端替换不好处理，这里重新处理哈映射到对应字段,回显的时候将字段反向设置回去
			if (OutboundBizTypeEnum.isTempSale(record.getBizType())) {
				record.setRelatedOrderId(record.getCustId());
				record.setRelatedOrderNo(record.getCustName());
				record.setCustId(null);
				record.setCustName(null);
			}
		}
	}

	@Override
	public List<WarehouseOutboundExcel> exportWarehouseOutbound(Wrapper<WarehouseOutboundEntity> queryWrapper) {
		List<WarehouseOutboundExcel> warehouseOutboundList = baseMapper.exportWarehouseOutbound(queryWrapper);
		//warehouseOutboundList.forEach(warehouseOutbound -> {
		//	warehouseOutbound.setTypeName(DictCache.getValue(DictEnum.YES_NO, WarehouseOutboundEntity.getType()));
		//});
		return warehouseOutboundList;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveOrUpdate(WarehouseOutboundDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseOutboundEntity entity = BeanUtil.copyProperties(dto, WarehouseOutboundEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 出库完成时间
		entity.setCompletionTime(Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) ? DateUtil.now() : null);
		// 生成出库单号
		if (Func.isNull(dto.getId())) {
			// 同一业务单只允许一条出库记录
			if (Func.notNull(dto.getRelatedOrderId()) && dto.getExcludeVerify()) {
				long count = count(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
					.eq(WarehouseOutboundEntity::getBizType, dto.getBizType())
					.notIn(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.getExcludeBizTypes())
					.eq(WarehouseOutboundEntity::getWarehouseId, dto.getWarehouseId())
					.eq(WarehouseOutboundEntity::getRelatedOrderId, dto.getRelatedOrderId()));
				Assert.isTrue(count < 1, () -> new ServiceException("该业务单已存在出库记录"));
			}
			entity.setOutboundNo(GenerateNumberUtil.generate(OutboundBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
			// 设置一个默认值
			if (Func.isNull(entity.getRelatedOrderId())) {
				entity.setRelatedOrderId(System.currentTimeMillis());
			}
			entity.setCreateTimeAt(System.currentTimeMillis());
			Assert.isTrue(save(entity), () -> new ServiceException("新增仓库出库记录失败"));
		} else {
			Assert.isTrue(updateById(entity), () -> new ServiceException("更新仓库出库记录失败"));
			WarehouseOutboundEntity outbound = getById(entity.getId());
			entity.setOutboundNo(outbound.getOutboundNo());
		}

		if (Func.isEmpty(dto.getOutboundItemList()) && Func.isEmpty(dto.getStoreSupportList())) {
			throw new ServiceException("数据异常,商品和配套运输品必须填写一个");
		}

		// 2.新增或更新仓库入库商品明细
		List<WarehouseOutboundItemEntity> outboundItemList = new ArrayList<>();
		if (Func.isNotEmpty(dto.getOutboundItemList())) {
			outboundItemList = this.saveOrUpdateOutboundItem(dto, entity.getId(), entity.getOutboundNo());
		}

		// 3.新增或更新仓库入库配套明细
		List<WarehouseSupportTransEntity> supportTransList = new ArrayList<>();
		if (Func.isNotEmpty(dto.getStoreSupportList())) {
			supportTransList = this.saveOrUpdateSupportTrans(dto, entity.getId());
		}

		// 4.转运出库需要更新转运单状态和时间
		if (Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) && OutboundBizTypeEnum.isTransport(dto.getBizType())) {
			transportOrderService.updateTransportOrderOutbound(dto.getRelatedOrderId(), outboundItemList, supportTransList);
		}
		// 5.销售出库需要比价格是否需要退差补差
		if (Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) && OutboundBizTypeEnum.isSale(dto.getBizType())) {
			SaleOrderEntity order = saleOrderService.checkAndGenerateOrderDiff(dto.getRelatedOrderId(), outboundItemList, supportTransList);
			// 更新出库商品明细差异金额
			warehouseOutboundItemService.updateDiffAmount(outboundItemList);
			// 推送消息
			messageSenderService.pushMessage(
				MessageTypeEnum.ORDER_STATUS,
				order.getCustId(),
				MessageJumpEnum.ORDER,
				String.valueOf(order.getId()),
				order.getOrderNo(), customProperties.getMerchantName(),
				DateUtil.formatDateTime(order.getOrderTime()),
				OrderEnum.PENDING_PICKUP.getMessage(),
				"订单出库"
			);
//			throw new ServiceException("测试回滚数据");
		}
		// 6.临时销售出库
		if (Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) && OutboundBizTypeEnum.isTempSale(dto.getBizType())) {
			// 合并订单明细(同一个sku不同供应商)
			List<WarehouseOutboundItemEntity> mergeOutboundItemList = mergeOutboundItem(outboundItemList);
			// 合并配套运输品明细
			List<WarehouseSupportTransEntity> mergeSupportTrans = mergeSupportTrans(supportTransList);
			// 创建临时订单
			SaleOrderEntity order = saleOrderService.createTempSaleOrder(dto.getCustType(), dto.getPayType(), dto.getCustId(), dto.getCustName(), mergeOutboundItemList, mergeSupportTrans);

			WarehouseOutboundEntity update = new WarehouseOutboundEntity();
			update.setId(entity.getId());
			update.setPayAmount(CommonUtil.ConvertIntBigDecimal(order.getTotalAmount()));
			update.setRelatedOrderId(order.getId());
			update.setRelatedOrderNo(order.getOrderNo());
			this.updateById(update);

			// 推送订单消息
			if (dto.getCustType() == 0 && (dto.getPayType().equals(PayTypeEnum.PLATFORM_QUOTA.getCode()) || dto.getPayType().equals(PayTypeEnum.WALLET.getCode()))) {
				// 推送订阅消息
				messageSenderService.pushMessage(
					MessageTypeEnum.PAY,
					order.getCustId(),
					MessageJumpEnum.ORDER,
					String.valueOf(order.getId()),
					order.getOrderNo(),
					CommonUtil.ConvertIntBigDecimal(order.getPayAmount()).toPlainString() + "元",
					DateTimeUtil.formatDateTime(order.getPayTime()),
					PayTypeEnum.getByCode(dto.getPayType()).getMessage(),
					"订单支付成功"
				);
			}
		}

		return true;
	}

	/**
	 * 合并出库商品明细
	 */
	private List<WarehouseOutboundItemEntity> mergeOutboundItem(List<WarehouseOutboundItemEntity> outboundItemList) {
		return outboundItemList.stream()
			.filter(item -> Func.notNull(item.getSupplierId()) && item.getWeight().compareTo(BigDecimal.ZERO) > 0)
			.collect(Collectors.toMap(
			WarehouseOutboundItemEntity::getProductSkuId,
			Function.identity(),
			(a, b) -> {
				WarehouseOutboundItemEntity item = new WarehouseOutboundItemEntity();
				item.setWarehouseId(a.getWarehouseId());
				item.setProductSkuId(a.getProductSkuId());
				item.setQuantity(a.getQuantity() + b.getQuantity());
				item.setWeight(a.getWeight().add(b.getWeight()));
				return item;
			}
		)).values().stream().toList();
	}

	/**
	 * 合并出库配套运输品
	 */
	private List<WarehouseSupportTransEntity> mergeSupportTrans(List<WarehouseSupportTransEntity> supportTransList) {
		return supportTransList.stream()
			.filter(item -> Func.notNull(item.getSupplierId()) && item.getSupportTransNum() > 0)
			.collect(Collectors.toMap(
			WarehouseSupportTransEntity::getProductSkuId,
			Function.identity(),
			(a, b) -> {
				WarehouseSupportTransEntity item = new WarehouseSupportTransEntity();
				item.setSupportTransUnitId(a.getSupportTransUnitId());
				item.setProductSkuId(a.getProductSkuId());
				item.setSupportTransNum(a.getSupportTransNum() + b.getSupportTransNum());
				return item;
			}
		)).values().stream().toList();
	}

	/**
	 * 新增差异出库单-商品
	 *
	 * @param dto 参数
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean addDiffInbound(WarehouseOutboundDTO dto) {
		dto.check();
		// 1.新增或更新仓库入库记录
		WarehouseOutboundEntity entity = BeanUtil.copyProperties(dto, WarehouseOutboundEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}
		// 出库完成时间
		entity.setCompletionTime(Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) ? DateUtil.now() : null);
		// 生成出库单号
		if (Func.isNull(dto.getId())) {
			entity.setOutboundNo(GenerateNumberUtil.generate(OutboundBizTypeEnum.getPrefixByCode(dto.getBizType()), ""));
		}
		Assert.isTrue(saveOrUpdate(entity), () -> new ServiceException("新增或更新仓库出库记录失败"));
		if (!Func.isEmpty(dto.getOutboundItemList())) {
			// 2.新增或更新仓库入库商品明细
			List<WarehouseOutboundItemEntity> itemEntityList = dto.convertOutboundItem(entity.getId(), entity.getOutboundNo());
			Assert.isTrue(warehouseOutboundItemService.saveOrUpdateBatch(itemEntityList), () -> new ServiceException("更新仓库入库商品明细失败"));
		}
		if (!Func.isEmpty(dto.getStoreSupportList())) {
			// 3.新增或更新仓库入库配套明细
			List<WarehouseSupportTransEntity> supportList = dto.convertSupportList(entity.getId(), BusinessConstant.WAREHOUSE_OUTBOUND);
			Assert.isTrue(warehouseSupportTransService.saveOrUpdateBatch(supportList), () -> new ServiceException("更新入库配套运输品明细失败"));
		}
		return true;
	}

	/**
	 * 新增或更新仓库入库商品明细及扣减商品库存
	 */
	private List<WarehouseOutboundItemEntity> saveOrUpdateOutboundItem(WarehouseOutboundDTO dto, Long outboundId, String outboundNo) {
		List<WarehouseOutboundItemEntity> itemEntityList = dto.convertOutboundItem(outboundId, outboundNo);
		// 退货入库需要将当前商品每斤价格存储用于供应商对账
		// 临时销售出库需要将当前商品每斤价格存储
		OutboundBizTypeEnum bizType = OutboundBizTypeEnum.getByCode(dto.getBizType());
		if (bizType == OutboundBizTypeEnum.RETURN || bizType == OutboundBizTypeEnum.TEMP_SALE) {
			log.info("[{}]开始查询商品最新售卖价格", bizType.getMessage());
			List<Long> skuIds = itemEntityList.stream().map(WarehouseOutboundItemEntity::getProductSkuId).distinct().toList();
			if (Func.isNotEmpty(skuIds)) {
				Map<Long, Integer> skuPrice = skuWarehouseRelationService.getSkuPrice(dto.getWarehouseId(), skuIds);
				log.info("[{}]，当前出库单[{}]查询到商品最新售卖价格:{}", bizType.getMessage(), outboundNo, JsonUtil.toJson(skuPrice));
				itemEntityList.forEach(item -> {
					Integer productSalePrice = skuPrice.get(item.getProductSkuId());
					Assert.notNull(productSalePrice, () -> new ServiceException("存在商品无法确认售卖价格,SKU编码:" + item.getSkuCode()));
					item.setProductSalePrice(productSalePrice);
				});
			}
		}
		Assert.isTrue(warehouseOutboundItemService.saveOrUpdateBatch(itemEntityList), () -> new ServiceException("更新仓库入库商品明细失败"));
		// 扣减商品库存
		if (Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) && !Objects.equals(dto.getBizType(), OutboundBizTypeEnum.DIFF_HS.getCode())
			&& !Objects.equals(dto.getBizType(), OutboundBizTypeEnum.DIFF_BS.getCode())) {
			Assert.isTrue(inventoryService.deductInventory(dto.getBizType(), itemEntityList), () -> new ServiceException("更新商品总库存失败"));
//			Assert.isTrue(warehouseStoreService.deductInventory(itemEntityList), () -> new ServiceException("更新商品批次库存失败"));
		}
		return itemEntityList;
	}

	/**
	 * 新增或更新仓库入库配套明细
	 */
	private List<WarehouseSupportTransEntity> saveOrUpdateSupportTrans(WarehouseOutboundDTO dto, Long outboundId) {
		List<WarehouseSupportTransEntity> supportList = dto.convertSupportList(outboundId, BusinessConstant.WAREHOUSE_OUTBOUND);
		if (Func.isNotEmpty(supportList) && !supportList.isEmpty()) {
			// 获取所有 supportTransUnitId
			List<Long> supportTransUnitIds = supportList.stream()
				.map(WarehouseSupportTransEntity::getSupportTransUnitId)
				.distinct()
				.collect(Collectors.toList());
			// 查询配套运输单位信息
			List<TransportUnitEntity> transportUnits = transportUnitService.listByIds(supportTransUnitIds);

			// 构建 Map 提高查找效率
			Map<Long, TransportUnitEntity> unitMap = transportUnits.stream()
				.collect(Collectors.toMap(TransportUnitEntity::getId, Function.identity()));
			supportList.forEach(s -> {
				if (Func.isNull(s.getSupportTransPrice())) {
					//保存单价
					TransportUnitEntity unit = unitMap.get(s.getSupportTransUnitId());
					s.setSupportTransPrice(Func.isNull(unit) ? BigDecimal.ZERO : CommonUtil.ConvertIntBigDecimal(unit.getPrice()));
				}
			});
		}
		Assert.isTrue(warehouseSupportTransService.saveOrUpdateBatch(supportList), () -> new ServiceException("更新入库配套运输品明细失败"));
		// 扣减配套运输品库存
		if (Objects.equals(dto.getStatus(), BusinessConstant.ENABLE_STATUS) && !Objects.equals(dto.getBizType(), OutboundBizTypeEnum.DIFF_HS.getCode())
			&& !Objects.equals(dto.getBizType(), OutboundBizTypeEnum.DIFF_BS.getCode())) {
			Assert.isTrue(inventorySupportTransService.deductInventory(supportList),
				() -> new ServiceException("更新商品配套运输品库存库存失败"));
		}
		return supportList;
	}

	@Override
	public List<IdVO> listByWarehouseAndType(Long warehouseId, Integer bizType) {
		Assert.isTrue(OutboundBizTypeEnum.relationStoreType(bizType), "只允许调拨或者转运类型");
		List<WarehouseOutboundEntity> entityList = list(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getWarehouseId, warehouseId)
			.eq(WarehouseOutboundEntity::getBizType, bizType)
			.eq(WarehouseOutboundEntity::getStatus, BusinessConstant.ENABLE_STATUS)
			.notExists("select 1 from chy_warehouse_store s where s.related_order_id = chy_warehouse_outbound.id and s.is_deleted = 0")
		);
		return entityList.stream().map(s -> new IdVO(s.getId(), s.getOutboundNo())).toList();
	}

	@Override
	public WarehouseOutboundEntity getByRelatedOrderId(Long warehouseId, Long transportId) {
		return getOne(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(Func.notNull(warehouseId), WarehouseOutboundEntity::getWarehouseId, warehouseId)
			.eq(WarehouseOutboundEntity::getRelatedOrderId, transportId)
			.eq(WarehouseOutboundEntity::getStatus, BusinessConstant.ENABLE_STATUS));
	}

	@Override
	public Map<Long, List<String>> listBatch(Long orderId, List<Long> skuIds) {
		OrderAfterSalesServiceEntity afterSales = orderAfterSalesServiceService.getOne(
			Wrappers.<OrderAfterSalesServiceEntity>lambdaQuery().select(OrderAfterSalesServiceEntity::getOrderId)
				.eq(OrderAfterSalesServiceEntity::getId, orderId)
				.eq(OrderAfterSalesServiceEntity::getStatus, 3)
		);
		Assert.notNull(afterSales, "售后单不存在");

		WarehouseOutboundEntity outbound = getOne(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getRelatedOrderId, afterSales.getOrderId())
			.eq(WarehouseOutboundEntity::getStatus, BusinessConstant.ENABLE_STATUS));
		Assert.notNull(outbound, "销售订单出库记录不存在");

		// 查询出库记录
		List<WarehouseOutboundItemEntity> itemList = warehouseOutboundItemService.list(Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
			.select(WarehouseOutboundItemEntity::getProductSkuId, WarehouseOutboundItemEntity::getId)
			.eq(WarehouseOutboundItemEntity::getWarehouseOutboundId, outbound.getId())
			.in(WarehouseOutboundItemEntity::getProductSkuId, skuIds)
		);

		if (Func.isEmpty(itemList)) {
			return null;
		}

		// 查询出库对应的入库批次
		List<WarehouseStoreOutboundRelationEntity> relationList = warehouseStoreOutboundRelationService.listByOutboundId(outbound.getId());
		if (Func.isEmpty(relationList)) {
			return null;
		}

		Map<Long, List<String>> outboundItemMap = relationList.stream()
			.collect(Collectors.groupingBy(WarehouseStoreOutboundRelationEntity::getOutboundItemId,
				Collectors.mapping(WarehouseStoreOutboundRelationEntity::getStoreBatchNo, Collectors.toList()))
			);


		return itemList.stream().collect(Collectors.toMap(WarehouseOutboundItemEntity::getProductSkuId, i -> outboundItemMap.get(i.getId())));
	}

	@Override
	public WarehouseSaleOrderOutboundVO saleOrderDetail(Long warehouseId, Long orderId) {
		// 查询销售订单出库记录
		WarehouseOutboundEntity outbound = getByRelatedOrderId(warehouseId, orderId);
		if (Func.isNull(outbound)) {
			return new WarehouseSaleOrderOutboundVO();
		}

		WarehouseSaleOrderOutboundVO vo = new WarehouseSaleOrderOutboundVO(outbound.getOutboundNo(), outbound.getCompletionTime(), outbound.getUpdateUser());
		// 出库商品明细
		List<WarehouseOutboundItemEntity> itemList = warehouseOutboundItemService.list(Wrappers.<WarehouseOutboundItemEntity>lambdaQuery()
			.eq(WarehouseOutboundItemEntity::getWarehouseOutboundId, outbound.getId())
		);
		if (Func.isEmpty(itemList)) {
			return vo;
		}
		// 出库配套运输品明细
		List<WarehouseSupportTransEntity> supportTransList = warehouseSupportTransService.listByStoreId(outbound.getId());
		// 查询原订单信息
		SaleOrderEntity order = saleOrderService.getById(orderId);
		List<SaleOrderItemEntity> orderItemList = saleOrderItemService.getOrderItemListByOrderId(orderId);
		this.assembleOutboundItem(vo, order, itemList, supportTransList, orderItemList);
		return vo;
	}

	/**
	 * 根据出库单号查询出库记录
	 */
	@Override
	public WarehouseOutboundEntity getByOutboundNo(String outboundNo) {
		return this.getOne(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getOutboundNo, outboundNo)
		);
	}

	/**
	 * 组装订单出库商品明细
	 */
	private void assembleOutboundItem(WarehouseSaleOrderOutboundVO vo,
									  SaleOrderEntity order,
									  List<WarehouseOutboundItemEntity> itemList,
									  List<WarehouseSupportTransEntity> supportTransList,
									  List<SaleOrderItemEntity> orderItemList) {
		// 根据sku聚合
		Map<Long, List<WarehouseOutboundItemEntity>> groupItemMap = itemList.stream().collect(Collectors.groupingBy(WarehouseOutboundItemEntity::getProductSkuId));
		Map<Long, List<WarehouseSupportTransEntity>> groupSupportTransMap = null;
		if (Func.isNotEmpty(supportTransList)) {
			groupSupportTransMap = supportTransList.stream().collect(Collectors.groupingBy(WarehouseSupportTransEntity::getProductSkuId));
		}
		Map<Long, SaleOrderItemEntity> skuMap = orderItemList.stream().collect(Collectors.toMap(SaleOrderItemEntity::getProductSkuId, i -> i));
		for (Map.Entry<Long, List<WarehouseOutboundItemEntity>> entry : groupItemMap.entrySet()) {
			WarehouseOutboundItemEntity firstItem = entry.getValue().get(0);
			WarehouseSaleOrderOutboundDetailVO detailVO = new WarehouseSaleOrderOutboundDetailVO();
			detailVO.setProductId(firstItem.getProductId());
			detailVO.setProductSkuId(entry.getKey());
			detailVO.setSpData(firstItem.getSpData());
			detailVO.setSkuCode(firstItem.getSkuCode());
			for (WarehouseOutboundItemEntity item : entry.getValue()) {
				// 要货数量
				if (Func.isNull(detailVO.getProductQuantity())) {
					detailVO.setProductQuantity(item.getExpectQuantity());
				}
				// 出库数量
				detailVO.sumQuantity(item.getQuantity());
				// 要货重量
				if (Func.isNull(detailVO.getProductWeight())) {
					detailVO.setProductWeight(item.getExpectWeight());
				}
				// 出库重量
				detailVO.sumWeight(item.getWeight());
				// 差异总金额
				BigDecimal diffAmount = CommonUtil.ConvertIntBigDecimal(item.getDifferenceAmount());
				if (Func.notNull(diffAmount)) {
					detailVO.setDifferenceAmount(diffAmount.toString());
				}
			}

			// 配套运输品
			if (Func.notNull(groupSupportTransMap)) {
				List<WarehouseSupportTransEntity> list = groupSupportTransMap.get(entry.getKey());
				if (Func.isNotEmpty(list)) {
					for (WarehouseSupportTransEntity trans : list) {
						detailVO.sumSupportTransNum(trans.getSupportTransNum());
						if (Func.isBlank(detailVO.getSupportTransUnitName())) {
							detailVO.setSupportTransUnitName(TransportUnitCache.getNameById(trans.getSupportTransUnitId()));
						}
						if (Func.isNull(detailVO.getExpectSupportTransNum())) {
							detailVO.setExpectSupportTransNum(trans.getExpectSupportTransNum());
						}
					}
				}
			}
			// 设置差异
			detailVO.setDiff();
			SaleOrderItemEntity orderItem = skuMap.get(entry.getKey());
			if (Func.notNull(orderItem)) {
				detailVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductSalePrice()));
				// 商品差异小计
				BigDecimal productDiffSubtotal = detailVO.getDifferenceWeight().multiply(BigDecimal.valueOf(orderItem.getProductSalePrice()));
				detailVO.setDifferenceProductSubtotal(CommonUtil.ConvertToBigDecimal(productDiffSubtotal));
				// 配送差异小计
				if (Func.notNull(order.getDeliveryFee())) {
					BigDecimal differenceDeliverySubtotal = detailVO.getDifferenceWeight().multiply(BigDecimal.valueOf(order.getDeliveryFee()));
					detailVO.setDifferenceDeliverySubtotal(CommonUtil.ConvertToBigDecimal(differenceDeliverySubtotal));
				}
				// 配送运输品差异小计
				if (Func.notNull(detailVO.getDifferenceSupportQuantity()) && Func.notNull(orderItem.getSupportTransPrice())) {
					BigDecimal differenceSupportSubtotal = CommonUtil.ConvertIntBigDecimal(detailVO.getDifferenceSupportQuantity() * orderItem.getSupportTransPrice());
					detailVO.setDifferenceSupportSubtotal(differenceSupportSubtotal);
				}
				// 差异合计
				detailVO.sumDifferenceTotal();
			}

			vo.getItemList().add(detailVO);
		}
		productService.assembleProductVO(vo.getItemList());
	}

	@Override
	public ByteArrayOutputStream print(WarehouseOutboundPrintDTO dto) throws IOException {
//		Long startTime = DateUtil.toMilliseconds(dto.getDate().atTime(LocalTime.MIN));
//		Long endTime = DateUtil.toMilliseconds(dto.getDate().atTime(LocalTime.MAX));
//		dto.setStartTime(startTime);
//		dto.setEndTime(endTime);
//		//获取档口的数据
//		Map<String, List<PrintOutboundVO>> listMap = new HashMap<>();
//		List<PrintOutboundVO>  warechouseList =	transportOrderService.printOutWarechouse(dto);
//		List<Long> warechouseNameList=warechouseList.stream().map(PrintOutboundVO::getWarehouseId).collect(Collectors.toList());
//		warechouseNameList.forEach(warechouseName -> {
//			List<PrintOutboundVO> stallss = warechouseList.stream().filter(i->i.getWarehouseName().equals(warechouseName)).collect(Collectors.toList());
//			listMap.put(stallss.get(0).getWarehouseName(), stallss);
//		});
//		//获取客户信息
//		List<PrintOutboundVO>  salseList =saleOrderService.printOutWarechouse(dto);
//		List<Long> ids=warechouseList.stream().map(PrintOutboundVO::getWarehouseId).collect(Collectors.toList());
//		ids.forEach(id->{
//			List<PrintOutboundVO> salseList1 = salseList.stream().filter(i->i.getWarehouseId().equals(id)).collect(Collectors.toList());
//			listMap.put(salseList1.get(0).getWarehouseName(),salseList1);
//		});
//
//		ByteArrayOutputStream byteArrayOutputStream = PDFServerUtil.GapCustomerDeliveryNote("档口/客户出库单.pdf", listMap);
//		return byteArrayOutputStream;


		// 设置时间范围
		Long startTime = DateUtil.toMilliseconds(dto.getDate().atStartOfDay());
		Long endTime = DateUtil.toMilliseconds(dto.getDate().plusDays(1).atStartOfDay()) - 1;
		dto.setStartTime(startTime);
		dto.setEndTime(endTime);

		// 查询所有出库记录
		List<PrintOutboundVO> allList = new ArrayList<>();
		allList.addAll(transportOrderService.printOutWarechouse(dto));
		allList.addAll(saleOrderService.printOutWarechouse(dto));

		// 按仓库名称分组
		Map<String, List<PrintOutboundVO>> listMap = allList.stream()
			.collect(Collectors.groupingBy(PrintOutboundVO::getWarehouseName));

		// 生成 PDF
		return PDFServerUtil.GapCustomerDeliveryNote("档口/客户出库单.pdf", listMap);

	}

	@Override
	public List<Long> getStoreBatchIdsByOutboundIds(List<Long> outboundIds) {
		if (Func.isEmpty(outboundIds)) {
			return new ArrayList<>();
		}

		// 查询出库记录关联的入库批次
		List<WarehouseStoreOutboundRelationEntity> relationList = warehouseStoreOutboundRelationService.list(
			Wrappers.<WarehouseStoreOutboundRelationEntity>lambdaQuery()
				.in(WarehouseStoreOutboundRelationEntity::getOutboundId, outboundIds)
		);

		if (Func.isEmpty(relationList)) {
			return new ArrayList<>();
		}

		// 提取入库批次ID
		return relationList.stream()
			.map(WarehouseStoreOutboundRelationEntity::getStoreBatchId)
			.distinct()
			.collect(Collectors.toList());
	}

	@Override
	public List<WarehouseOutboundSupplierDetailVO> getOutboundSupplierDetail(Long orderId, Long productSkuId) {
		if (Func.isNull(orderId) || Func.isNull(productSkuId)) {
			return null;
		}

		return baseMapper.selectOutboundSupplierDetail(orderId, productSkuId);
	}

	@Override
	public List<WarehouseSupportTransVO> getSupportTransAndStock(Long warehouseId, List<Long> skuIds) {
		// 获取商品对应的配套运输品
		Map<Long, IdNumVO> supportTransNum = skuStockService.getSupportTransNum(skuIds);
		if (Func.isEmpty(supportTransNum)) {
			return List.of();
		}

		List<InventorySupportTransEntity> transList = new ArrayList<>();
		for (Map.Entry<Long, IdNumVO> entry : supportTransNum.entrySet()) {
			// 构建库存查询条件
			InventorySupportTransEntity entity = new InventorySupportTransEntity();
			entity.setProductSkuId(entry.getKey());
			entity.setTransUnitId(entry.getValue().getId());
			transList.add(entity);
		}

		// 设置配套运输品库存信息
		List<InventorySupportTransVO> inventoryList = inventorySupportTransMapper.getSkuAndTransUnitInventoryList(warehouseId, transList);

		// 供应商是否显示名称，管理员 总仓显示名称，分仓显示code码
		boolean isShowName = true;
		if (!AuthUtil.isAdmin()) {
			// 获取当前用户关联仓库
			WarehouseEntity warehouse = warehouseService.getById(warehouseId);
			Assert.notNull(warehouse, () -> new ServiceException("仓库不存在"));
			isShowName = WarehouseTypeEnum.isTotalWarehouse(warehouse.getWarehouseType());
		}

		List<WarehouseSupportTransVO> voList = new ArrayList<>();
		// 构建返回VO
		for (Map.Entry<Long, IdNumVO> entry : supportTransNum.entrySet()) {
			List<InventorySupportTransVO> transVOS = inventoryList.stream()
				.filter(i -> i.getProductSkuId().equals(entry.getKey()) && i.getTransUnitId().equals(entry.getValue().getId()))
				.toList();
			if (Func.isEmpty(transVOS)) {
				// 构建返回VO
				WarehouseSupportTransVO vo = new WarehouseSupportTransVO();
				vo.setProductSkuId(entry.getKey());
				vo.setSupportTransUnitId(entry.getValue().getId());
				vo.setSupportTransPrice(Optional.ofNullable(TransportUnitCache.getById(vo.getSupportTransUnitId()).getPrice()).map(CommonUtil::ConvertIntBigDecimal).orElse(null));
				vo.setSupportTransUnitName(entry.getValue().getName());
				voList.add(vo);
				continue;
			}

			for (InventorySupportTransVO inventory : transVOS) {
				BigDecimal price = Optional.ofNullable(TransportUnitCache.getById(inventory.getTransUnitId()).getPrice()).map(CommonUtil::ConvertIntBigDecimal).orElse(null);
				if (inventory.getProductSkuId().equals(entry.getKey()) && inventory.getTransUnitId().equals(entry.getValue().getId())) {
					// 构建返回VO
					WarehouseSupportTransVO vo = new WarehouseSupportTransVO();
					vo.setProductSkuId(entry.getKey());
					vo.setSupportTransUnitId(entry.getValue().getId());
					vo.setSupportTransPrice(price);
					vo.setSupportTransUnitName(entry.getValue().getName());
					vo.setSupplierId(inventory.getSupplierId());
					vo.setSupplierName(isShowName ? inventory.getSupplierName() : inventory.getCode());
					vo.setInventorySupportTransNum(inventory.getRemainingQuantity());
					voList.add(vo);
				}
			}
		}
		// 设置配套运输品关联商品
		warehouseSupportTransService.setSupportTransProductInfo(voList);
		return voList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<SaleOrderEntity> batchSaleOutbound(BatchOutboundDTO dto) {
		// 将订单商品列表按orderId进行分组
		Map<Long, List<OrderProductInfo>> groupedByOrderId = dto.getOrderProductList()
			.stream()
			.collect(Collectors.groupingBy(OrderProductInfo::getOrderId));

		// 聚合并扣减商品库存
		this.aggregateAndDeductInventory(dto);

		// 聚合并扣减配套运输品库存
		this.aggregateAndDeductSupportTrans(dto);

		// 初始化消息推送的订单
		List<SaleOrderEntity> pushInfoOrders = new ArrayList<>();

		// 处理每个订单的出库记录
		for (Map.Entry<Long, List<OrderProductInfo>> entry : groupedByOrderId.entrySet()) {
			this.processOrderOutbound(dto, entry.getKey(), entry.getValue(), pushInfoOrders);
		}

		return pushInfoOrders;
	}

	/**
	 * 处理单个订单的出库记录
	 */
	private void processOrderOutbound(BatchOutboundDTO dto, Long orderId, List<OrderProductInfo> orderProducts, List<SaleOrderEntity> pushInfoOrders) {
		// 校验订单是否已存在出库记录
		this.validateOrderNotExists(dto.getWarehouseId(), orderId);

		// 创建出库记录主表
		WarehouseOutboundEntity outboundEntity = this.createOutboundEntity(dto, orderId, orderProducts);

		// 处理出库商品明细
		List<WarehouseOutboundItemEntity> outboundItemEntities = this.processOutboundItems(dto, outboundEntity, orderProducts);

		// 处理配套运输品明细
		List<WarehouseSupportTransEntity> supportTransEntities = this.processSupportTransItems(dto, outboundEntity, orderId);

		// 处理订单差价和推送消息
		this.processOrderDiffAndMessage(orderId, outboundItemEntities, supportTransEntities, pushInfoOrders);
	}

	/**
	 * 校验订单是否已存在出库记录
	 */
	private void validateOrderNotExists(Long warehouseId, Long orderId) {
		long existingCount = count(Wrappers.<WarehouseOutboundEntity>lambdaQuery()
			.eq(WarehouseOutboundEntity::getWarehouseId, warehouseId)
			.eq(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.SALE.getCode())
			.eq(WarehouseOutboundEntity::getRelatedOrderId, orderId)
			.eq(WarehouseOutboundEntity::getStatus, BusinessConstant.ENABLE_STATUS));
		Assert.isTrue(existingCount < 1, () -> new ServiceException("订单ID为" + orderId + "的销售出库记录已存在，不能重复出库"));
	}

	/**
	 * 创建出库记录主表
	 */
	private WarehouseOutboundEntity createOutboundEntity(BatchOutboundDTO dto, Long orderId, List<OrderProductInfo> orderProducts) {
		String orderNo = orderProducts.get(0).getOrderNo();

		WarehouseOutboundEntity outboundEntity = new WarehouseOutboundEntity();
		outboundEntity.setWarehouseId(dto.getWarehouseId());
		outboundEntity.setBizType(OutboundBizTypeEnum.SALE.getCode());
		outboundEntity.setRelatedOrderId(orderId);
		outboundEntity.setRelatedOrderNo(orderNo);
		outboundEntity.setNote(dto.getNote());
		outboundEntity.setCompletionTime(DateUtil.now());
		outboundEntity.setDeliveryCost(dto.getDeliveryCostYuan());
		outboundEntity.setOutboundNo(GenerateNumberUtil.generate(OutboundBizTypeEnum.getPrefixByCode(OutboundBizTypeEnum.SALE.getCode()), ""));
		outboundEntity.setStatus(BusinessConstant.ENABLE_STATUS);
		outboundEntity.setCreateTimeAt(System.currentTimeMillis());
		this.save(outboundEntity);
		return outboundEntity;
	}

	/**
	 * 处理出库商品明细
	 */
	private List<WarehouseOutboundItemEntity> processOutboundItems(BatchOutboundDTO dto, WarehouseOutboundEntity outboundEntity, List<OrderProductInfo> orderProducts) {
		List<WarehouseOutboundItemEntity> outboundItemEntities = new ArrayList<>();

		if (Func.isNotEmpty(orderProducts)) {
			for (OrderProductInfo inventory : orderProducts) {
				WarehouseOutboundItemEntity itemEntity = this.createOutboundItemEntity(dto, outboundEntity, inventory);
				outboundItemEntities.add(itemEntity);
			}
		}

		// 批量保存出库商品明细
		if (Func.isNotEmpty(outboundItemEntities)) {
			warehouseOutboundItemService.saveBatch(outboundItemEntities);
		}

		return outboundItemEntities;
	}

	/**
	 * 创建出库商品明细实体
	 */
	private WarehouseOutboundItemEntity createOutboundItemEntity(BatchOutboundDTO dto, WarehouseOutboundEntity outboundEntity, OrderProductInfo inventory) {
		WarehouseOutboundItemEntity itemEntity = new WarehouseOutboundItemEntity();
		itemEntity.setWarehouseId(dto.getWarehouseId());
		itemEntity.setWarehouseOutboundId(outboundEntity.getId());
		itemEntity.setWarehouseOutboundNo(outboundEntity.getOutboundNo());
		itemEntity.setProductId(inventory.getProductId());
		itemEntity.setProductSkuId(inventory.getSkuId());
		itemEntity.setSupplierId(inventory.getSupplierId());
		itemEntity.setSkuCode(inventory.getSkuCode());
		itemEntity.setSpData(inventory.getSpData());
		itemEntity.setQuantity(inventory.getOutboundQuantity());
		itemEntity.setWeight(inventory.getOutboundWeight());
		itemEntity.setAllowDeleted(inventory.getAllowDeleted());
		itemEntity.setExpectQuantity(inventory.getProductQuantity());
		if (Func.notNull(inventory.getPackageGrossConversionRate()) && Func.notNull(inventory.getProductQuantity())) {
			itemEntity.setExpectWeight(inventory.getPackageGrossConversionRate().multiply(BigDecimal.valueOf(inventory.getProductQuantity())));
		}
		return itemEntity;
	}

	/**
	 * 处理配套运输品明细
	 */
	private List<WarehouseSupportTransEntity> processSupportTransItems(BatchOutboundDTO dto, WarehouseOutboundEntity outboundEntity, Long orderId) {
		List<WarehouseSupportTransEntity> supportTransEntities = new ArrayList<>();

		List<OrderTransportInfo> currentOrderTransportList = dto.getOrderTransportList()
			.stream()
			.filter(transport -> transport.getOrderId().equals(orderId))
			.toList();

		if (Func.isNotEmpty(currentOrderTransportList)) {
			for (OrderTransportInfo transportInfo : currentOrderTransportList) {
				WarehouseSupportTransEntity supportTransEntity = this.createSupportTransEntity(dto, outboundEntity, transportInfo);
				supportTransEntities.add(supportTransEntity);
			}

			// 批量保存配套运输品明细
			if (Func.isNotEmpty(supportTransEntities)) {
				warehouseSupportTransService.saveBatch(supportTransEntities);
			}
		}

		return supportTransEntities;
	}

	/**
	 * 创建配套运输品明细实体
	 */
	private WarehouseSupportTransEntity createSupportTransEntity(BatchOutboundDTO dto, WarehouseOutboundEntity outboundEntity, OrderTransportInfo transportInfo) {
		WarehouseSupportTransEntity supportTransEntity = new WarehouseSupportTransEntity();
		supportTransEntity.setWarehouseId(dto.getWarehouseId());
		supportTransEntity.setBizType(2); // biz_type固定为2（出库）
		supportTransEntity.setSupplierId(transportInfo.getSupplierId());
		supportTransEntity.setRelatedRecordId(outboundEntity.getId());
		supportTransEntity.setProductSkuId(transportInfo.getSkuId());
		supportTransEntity.setSupportTransUnitId(transportInfo.getTransUnitId());
		supportTransEntity.setSupportTransNum(transportInfo.getOutboundQuantity());
		supportTransEntity.setStatus(1); // status固定为1
		supportTransEntity.setAllowDeleted(transportInfo.getAllowDeleted());
		supportTransEntity.setExpectSupportTransNum(transportInfo.getSupportTransNum());
		return supportTransEntity;
	}

	/**
	 * 处理订单差价和推送消息
	 */
	private void processOrderDiffAndMessage(Long orderId, List<WarehouseOutboundItemEntity> outboundItemEntities, List<WarehouseSupportTransEntity> supportTransEntities, List<SaleOrderEntity> pushInfoOrders) {
		// 将outboundItemEntities按productSkuId分组
		Map<Long, List<WarehouseOutboundItemEntity>> outboundItemGroupMap = outboundItemEntities.stream()
			.collect(Collectors.groupingBy(WarehouseOutboundItemEntity::getProductSkuId));

		// 将supportTransEntities按productSkuId分组
		Map<Long, List<WarehouseSupportTransEntity>> supportTransGroupMap = supportTransEntities.stream()
			.collect(Collectors.groupingBy(WarehouseSupportTransEntity::getProductSkuId));

		// 聚合outboundItemEntities（按productSkuId聚合quantity和weight）
		List<WarehouseOutboundItemEntity> aggregatedOutboundItems = new ArrayList<>();
		for (Map.Entry<Long, List<WarehouseOutboundItemEntity>> entry : outboundItemGroupMap.entrySet()) {
			List<WarehouseOutboundItemEntity> itemGroup = entry.getValue();
			if (Func.isNotEmpty(itemGroup)) {
				WarehouseOutboundItemEntity aggregatedItem = this.aggregateOutboundItems(itemGroup);
				aggregatedOutboundItems.add(aggregatedItem);
			}
		}

		// 聚合supportTransEntities（按productSkuId聚合supportTransNum）
		List<WarehouseSupportTransEntity> aggregatedSupportTrans = new ArrayList<>();
		for (Map.Entry<Long, List<WarehouseSupportTransEntity>> entry : supportTransGroupMap.entrySet()) {
			List<WarehouseSupportTransEntity> transGroup = entry.getValue();
			if (Func.isNotEmpty(transGroup)) {
				WarehouseSupportTransEntity aggregatedTrans = this.aggregateSupportTrans(transGroup);
				aggregatedSupportTrans.add(aggregatedTrans);
			}
		}

		// 对聚合后的数据进行价格差异处理
		SaleOrderEntity order = saleOrderService.checkAndGenerateOrderDiff(orderId, aggregatedOutboundItems, aggregatedSupportTrans);
		pushInfoOrders.add(order);
		// 更新出库商品明细差异金额（使用原始数据）
		warehouseOutboundItemService.updateDiffAmount(aggregatedOutboundItems);
	}

	/**
	 * 聚合并扣减商品库存
	 */
	private void aggregateAndDeductInventory(BatchOutboundDTO dto) {
		Map<String, WarehouseOutboundItemEntity> aggregatedItems = new HashMap<>();

		List<OrderProductInfo> inventoryList = dto.getOrderProductList();
		if (Func.isNotEmpty(inventoryList)) {
			for (OrderProductInfo inventory : inventoryList) {
				Long supplierId = inventory.getSupplierId();
				if (supplierId == null) {
					continue;
				}
				if ((inventory.getOutboundQuantity() == null || inventory.getOutboundQuantity() == 0) && (inventory.getOutboundWeight() == null || inventory.getOutboundWeight().compareTo(BigDecimal.ZERO) == 0)) {
					continue;
				}
				String groupKey = inventory.getSkuId() + "_" + inventory.getSupplierId();
				this.aggregateInventoryItem(aggregatedItems, groupKey, dto, inventory);
			}
		}

		// 对聚合后的出库商品明细进行削减库存
		if (Func.isNotEmpty(aggregatedItems)) {
			List<WarehouseOutboundItemEntity> aggregatedItemList = new ArrayList<>(aggregatedItems.values());
			Assert.isTrue(inventoryService.deductInventory(OutboundBizTypeEnum.SALE.getCode(), aggregatedItemList), () -> new ServiceException("更新商品总库存失败"));
		}
	}

	/**
	 * 聚合库存商品项
	 */
	private void aggregateInventoryItem(Map<String, WarehouseOutboundItemEntity> aggregatedItems, String groupKey, BatchOutboundDTO dto, OrderProductInfo inventory) {
		WarehouseOutboundItemEntity existingItem = aggregatedItems.get(groupKey);
		if (existingItem == null) {
			// 创建新的聚合项
			WarehouseOutboundItemEntity aggregatedItem = new WarehouseOutboundItemEntity();
			aggregatedItem.setWarehouseId(dto.getWarehouseId());
			aggregatedItem.setProductId(inventory.getProductId());
			aggregatedItem.setProductSkuId(inventory.getSkuId());
			aggregatedItem.setSupplierId(inventory.getSupplierId());
			aggregatedItem.setSkuCode(inventory.getSkuCode());
			aggregatedItem.setSpData(inventory.getSpData());
			aggregatedItem.setQuantity(inventory.getOutboundQuantity() == null ? 0 : inventory.getOutboundQuantity());
			aggregatedItem.setWeight(inventory.getOutboundWeight() == null ? BigDecimal.ZERO : inventory.getOutboundWeight());
			aggregatedItem.setExpectQuantity(inventory.getProductQuantity());
			aggregatedItems.put(groupKey, aggregatedItem);
		} else {
			// 聚合数量和重量
			existingItem.setQuantity(existingItem.getQuantity() + (inventory.getOutboundQuantity() == null ? 0 : inventory.getOutboundQuantity()));
			existingItem.setExpectQuantity(existingItem.getExpectQuantity() + inventory.getProductQuantity());
			if (existingItem.getWeight() != null && inventory.getOutboundWeight() != null) {
				existingItem.setWeight(existingItem.getWeight().add(inventory.getOutboundWeight()));
			} else if (inventory.getOutboundWeight() != null) {
				existingItem.setWeight(inventory.getOutboundWeight());
			}
		}
	}

	/**
	 * 聚合并扣减配套运输品库存
	 */
	private void aggregateAndDeductSupportTrans(BatchOutboundDTO dto) {
		Map<String, WarehouseSupportTransEntity> aggregatedTransports = new HashMap<>();

		if (Func.isNotEmpty(dto.getOrderTransportList())) {
			for (OrderTransportInfo transportInfo : dto.getOrderTransportList()) {
				if (transportInfo.getSupplierId() == null) {
					continue;
				}
				if ((transportInfo.getOutboundQuantity() == null || transportInfo.getOutboundQuantity() == 0)) {
					continue;
				}
				String groupKey = transportInfo.getSkuId() + "_" + transportInfo.getSupplierId() + "_" + transportInfo.getTransUnitId();
				this.aggregateSupportTransItem(aggregatedTransports, groupKey, dto, transportInfo);
			}
		}

		// 对聚合后的配套运输品明细进行削减库存
		if (Func.isNotEmpty(aggregatedTransports)) {
			List<WarehouseSupportTransEntity> aggregatedTransportList = new ArrayList<>(aggregatedTransports.values());
			Assert.isTrue(inventorySupportTransService.deductInventory(aggregatedTransportList), () -> new ServiceException("更新商品配套运输品库存库存失败"));
		}
	}

	/**
	 * 聚合配套运输品项
	 */
	private void aggregateSupportTransItem(Map<String, WarehouseSupportTransEntity> aggregatedTransports, String groupKey, BatchOutboundDTO dto, OrderTransportInfo transportInfo) {
		WarehouseSupportTransEntity existingTransport = aggregatedTransports.get(groupKey);
		if (existingTransport == null) {
			// 创建新的聚合项
			WarehouseSupportTransEntity aggregatedTransport = new WarehouseSupportTransEntity();
			aggregatedTransport.setWarehouseId(dto.getWarehouseId());
			aggregatedTransport.setProductSkuId(transportInfo.getSkuId());
			aggregatedTransport.setSupplierId(transportInfo.getSupplierId());
			aggregatedTransport.setSupportTransUnitId(transportInfo.getTransUnitId());
			aggregatedTransport.setSupportTransNum(transportInfo.getOutboundQuantity() == null ? 0 : transportInfo.getOutboundQuantity());
			aggregatedTransports.put(groupKey, aggregatedTransport);
		} else {
			// 聚合数量
			existingTransport.setSupportTransNum(existingTransport.getSupportTransNum() + (transportInfo.getOutboundQuantity() == null ? 0 : transportInfo.getOutboundQuantity()));
		}
	}

	@Override
	public IPage<CustomerDeliveryGroupVO> getCustomerDeliveryGroup(CustomerDeliveryQueryDTO dto, IPage<CustomerDeliveryGroupVO> page) {
		IPage<CustomerDeliveryGroupVO> resultPage = page.setRecords(this.baseMapper.selectCustomerDeliveryGroup(page, dto));

		if (resultPage.getTotal() < 1) {
			return resultPage;
		}

		// 设置配送方式中文名称
		List<CustomerDeliveryGroupVO> records = resultPage.getRecords();
		// 设置地址且自提不显示收货人姓名、收货人电话、收货地址
		records.forEach(record -> {
			if (record.getDeliveryType() == 2) { // 自提
				record.setReceiverName(null);
				record.setReceiverPhone(null);
				record.setReceiverProvince(null);
				record.setReceiverCity(null);
				record.setReceiverRegion(null);
				record.setReceiverDetailAddress(null);
			} else { // 配送
				Region region = RegionCache.getByCode(record.getReceiverRegion());
				if (region != null) {
					record.setReceiverProvince(region.getProvinceName());
					record.setReceiverCity(region.getCityName());
					record.setReceiverRegion(region.getDistrictName());
					record.setReceiverDetailAddress(region.getProvinceName() + region.getCityName() + region.getDistrictName() + record.getReceiverDetailAddress());
				}
			}
			record.setDeliveryTypeName();
		});

		return resultPage;
	}

	/**
	 * 获取差异出库单-根据转运单id
	 *
	 * @param transportOrderId 转运单id
	 * @return 差异出库单-根据转运单id
	 */
	@Override
	public List<WarehouseOutboundItemEntity> getDiffOutItemRecordsByTransportOrderId(Long transportOrderId) {
		List<WarehouseOutboundEntity> outboundList = baseMapper.selectList(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.eq(WarehouseOutboundEntity::getRelatedOrderId, transportOrderId)
				.in(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF.getCode(), OutboundBizTypeEnum.DIFF_HS.getCode(), OutboundBizTypeEnum.DIFF_BS.getCode())
		);
		if (Func.isEmpty(outboundList)) {
			return new ArrayList<>();
		}
		List<Long> outboundIds = outboundList.stream().map(WarehouseOutboundEntity::getId).collect(Collectors.toList());
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(Wrappers.<WarehouseOutboundItemEntity>lambdaQuery().in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds));

//		if (Func.isNotEmpty(outboundItems)) {
//			Map<Long, WarehouseOutboundEntity> outboundEntityMap = outboundList.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, java.util.function.Function.identity()));
//			outboundItems.forEach(item -> {
//				WarehouseOutboundEntity outboundEntity = outboundEntityMap.get(item.getWarehouseOutboundId());
//				if (outboundEntity != null && OutboundBizTypeEnum.DIFF_HS.getCode().equals(outboundEntity.getBizType())) {
//						if (item.getQuantity() != null) {
//							item.setQuantity(-item.getQuantity());
//						}
//						if (item.getWeight() != null) {
//							item.setWeight(item.getWeight().negate());
//						}
//					}
//				});
//		}
		return outboundItems;
	}

	@Override
	public Map<Long, List<WarehouseOutboundItemEntity>> getDiffOutItemRecordsByTransportOrderIds(List<Long> transportOrderIds) {
		if (Func.isEmpty(transportOrderIds)) {
			return new HashMap<>();
		}
		List<WarehouseOutboundEntity> outboundList = baseMapper.selectList(
			Wrappers.<WarehouseOutboundEntity>lambdaQuery()
				.in(WarehouseOutboundEntity::getRelatedOrderId, transportOrderIds)
				.in(WarehouseOutboundEntity::getBizType, OutboundBizTypeEnum.DIFF.getCode(), OutboundBizTypeEnum.DIFF_HS.getCode(), OutboundBizTypeEnum.DIFF_BS.getCode())
		);
		if (Func.isEmpty(outboundList)) {
			return new HashMap<>();
		}
		List<Long> outboundIds = outboundList.stream().map(WarehouseOutboundEntity::getId).collect(Collectors.toList());
		List<WarehouseOutboundItemEntity> outboundItems = warehouseOutboundItemService.list(Wrappers.<WarehouseOutboundItemEntity>lambdaQuery().in(WarehouseOutboundItemEntity::getWarehouseOutboundId, outboundIds));

		if (Func.isEmpty(outboundItems)) {
			return new HashMap<>();
		}

		Map<Long, WarehouseOutboundEntity> outboundEntityMap = outboundList.stream().collect(Collectors.toMap(WarehouseOutboundEntity::getId, java.util.function.Function.identity()));
//
//		outboundItems.forEach(item -> {
//			WarehouseOutboundEntity outboundEntity = outboundEntityMap.get(item.getWarehouseOutboundId());
//			if (outboundEntity != null && OutboundBizTypeEnum.DIFF_HS.getCode().equals(outboundEntity.getBizType())) {
//				if (item.getQuantity() != null) {
//					item.setQuantity(-item.getQuantity());
//				}
//				if (item.getWeight() != null) {
//					item.setWeight(item.getWeight().negate());
//				}
//			}
//		});
//
		Map<Long, Long> outboundIdToTransportOrderIdMap = outboundList.stream()
			.collect(Collectors.toMap(WarehouseOutboundEntity::getId, WarehouseOutboundEntity::getRelatedOrderId, (a, b) -> a));

		return outboundItems.stream().collect(Collectors.groupingBy(item -> outboundIdToTransportOrderIdMap.get(item.getWarehouseOutboundId())));
	}

	@Override
	public ByteArrayOutputStream printSalesOutboundOrder(PrintSalesOutboundOrderDTO dto) throws IOException {
		//获取订单
		SaleOrderEntity saleOrder = saleOrderService.getById(dto.getOrderId());
		if (saleOrder == null) {
			return null;
		}
		SalesOutboundOrderVO salesOutboundOrderVO = new SalesOutboundOrderVO();
		salesOutboundOrderVO.setDate(DateUtil.format(saleOrder.getPayTime(), "yyyy-MM-dd HH:mm"));
		salesOutboundOrderVO.setOrderNo(saleOrder.getOrderNo());
		CustEntity cust = custMapper.selectById(saleOrder.getCustId());
		User user = userMapper.selectById(cust.getUserId());
		if (user != null) {
			salesOutboundOrderVO.setSalesman(user.getRealName());
		}
		WarehouseEntity warehouse = warehouseService.getById(cust.getWarehouseId());
		salesOutboundOrderVO.setCustomerName(cust.getCustName());
		salesOutboundOrderVO.setWarehouseName(warehouse.getWarehouseName());
		salesOutboundOrderVO.setAddress(warehouse.getAddress());

		if (saleOrder.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			salesOutboundOrderVO.setTakeMethod(DeliveryEnum.DELIVERY.getMessage());
			salesOutboundOrderVO.setReceiver(saleOrder.getReceiverName());
			salesOutboundOrderVO.setReceiverPhone(saleOrder.getReceiverPhone());
			salesOutboundOrderVO.setTakeAddress(warehouse.getRegionName() + warehouse.getAddress());
		} else {
			salesOutboundOrderVO.setTakeMethod(DeliveryEnum.PICKUP.getMessage());
		}

		List<SalesOutboundOrderListVO> itemGroup = new ArrayList<>();
		//获取订单
		List<SaleOrderItemEntity> list = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>lambdaQuery().eq(SaleOrderItemEntity::getOrderId, saleOrder.getId()));
		//获取下单量商品
		List<SalesOutboundOrderListVO> spList = baseMapper.selectOutboundItems(saleOrder.getId());
		List<Long> skuIds = list.stream().map(SaleOrderItemEntity::getProductSkuId).toList();
		//获取下单量框
		List<SalesOutboundOrderListVO> kList = baseMapper.selectSList(skuIds, saleOrder.getId());
		List<SkuStockEntity> stockEntities = skuStockService.list(Wrappers.<SkuStockEntity>lambdaQuery().in(SkuStockEntity::getId, skuIds)).stream().toList();
		for (SaleOrderItemEntity item : list) {
			BigDecimal price = BigDecimal.ZERO;
			BigDecimal weightOrNumOut = BigDecimal.ZERO;
			BigDecimal weightOrNumInt = BigDecimal.ZERO;
			SalesOutboundOrderListVO vo = new SalesOutboundOrderListVO();
			vo.setProductName(item.getProductName());
			SkuStockEntity skuStockEntity = stockEntities.stream().filter(i -> i.getId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (skuStockEntity != null) {
				vo.setGrossWeight(skuStockEntity.getPackageGrossConversionRate());
				vo.setNetWeight(skuStockEntity.getPackageNetConversionRate());
			}

			SalesOutboundOrderListVO salesOutboundOrderListVO = spList.stream().filter(i -> i.getSkuId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (salesOutboundOrderListVO != null) {
				vo.setOrderNum(salesOutboundOrderListVO.getOrderNum());
				if (Func.isNotEmpty(salesOutboundOrderListVO.getDifference()))
					vo.setDifference(CommonUtil.ConvertToBigDecimal(salesOutboundOrderListVO.getDifference()));
			}
			if (item.getIsStandard().equals(0)) {//标品 数量
				vo.setOutboundNum(String.valueOf(item.getProductQuantity()));
				vo.setUnitPrice(CommonUtil.ConvertIntBigDecimal(item.getProductUnitPrice()) + "/件");
			} else {//非标配斤重
				assert skuStockEntity != null;
				BigDecimal sj = skuStockEntity.getPackageGrossConversionRate().multiply(new BigDecimal(Func.isEmpty(vo.getOrderNum()) ? "0" : vo.getOrderNum()));
				vo.setOrderNum(sj.toString() + "斤");
				vo.setOutboundNum(CommonUtil.WeightIntBigDecimal(item.getProductWeight()) + "斤");
				vo.setUnitPrice(CommonUtil.ConvertIntBigDecimal(item.getProductSalePrice()) + "/斤");
			}
			vo.setSubtotal(CommonUtil.ConvertIntBigDecimal(item.getProductPrice()));
			vo.setSupportTrans(item.getSupportTrans());
			if (Func.isNotEmpty(item.getSupportTransUnitId()))
				vo.setSupportTransUnitPrice(CommonUtil.ConvertIntBigDecimal(item.getSupportTransPrice()));
			else
				vo.setSupportTransUnitPrice(BigDecimal.ZERO);
			SalesOutboundOrderListVO kout = kList.stream().filter(i -> i.getSkuId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (kout != null) {
				vo.setSupportTransOrderNum(kout.getSupportTransOrderNum());
			}
			vo.setSupportTransOutboundNum(item.getSupportTransNum());

			//vo.setDifference(subtract.toString());
			itemGroup.add(vo);
		}
		return PDFServerUtil.SalesOutboundOrder("销售出库单.pdf", salesOutboundOrderVO, itemGroup);
	}

	private WarehouseOutboundItemEntity aggregateOutboundItems(List<WarehouseOutboundItemEntity> itemGroup) {
		if (itemGroup == null || itemGroup.isEmpty()) {
			return null;
		}
		WarehouseOutboundItemEntity firstItem = itemGroup.get(0);
		WarehouseOutboundItemEntity aggregatedItem = new WarehouseOutboundItemEntity();

		// 复制基本信息（使用第一个item的基本信息）
		aggregatedItem.setId(firstItem.getId());
		aggregatedItem.setWarehouseId(firstItem.getWarehouseId());
		aggregatedItem.setWarehouseOutboundId(firstItem.getWarehouseOutboundId());
		aggregatedItem.setWarehouseOutboundNo(firstItem.getWarehouseOutboundNo());
		aggregatedItem.setProductId(firstItem.getProductId());
		aggregatedItem.setProductSkuId(firstItem.getProductSkuId());
		aggregatedItem.setSupplierId(firstItem.getSupplierId());
		aggregatedItem.setSkuCode(firstItem.getSkuCode());
		aggregatedItem.setSpData(firstItem.getSpData());
		aggregatedItem.setAllowDeleted(firstItem.getAllowDeleted());

		// 聚合quantity和weight
		Integer totalQuantity = itemGroup.stream()
			.mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
			.sum();
		aggregatedItem.setQuantity(totalQuantity);

		BigDecimal totalWeight = itemGroup.stream()
			.filter(item -> item.getWeight() != null)
			.map(WarehouseOutboundItemEntity::getWeight)
			.reduce(BigDecimal.ZERO, BigDecimal::add);
		aggregatedItem.setWeight(totalWeight);

		return aggregatedItem;
	}

	/**
	 * 聚合配套运输品（按productSkuId聚合supportTransNum）
	 */
	private WarehouseSupportTransEntity aggregateSupportTrans(List<WarehouseSupportTransEntity> transGroup) {
		WarehouseSupportTransEntity firstTrans = transGroup.get(0);
		WarehouseSupportTransEntity aggregatedTrans = new WarehouseSupportTransEntity();

		// 复制基本信息（使用第一个trans的基本信息）
		aggregatedTrans.setWarehouseId(firstTrans.getWarehouseId());
		aggregatedTrans.setBizType(firstTrans.getBizType());
		aggregatedTrans.setSupplierId(firstTrans.getSupplierId());
		aggregatedTrans.setRelatedRecordId(firstTrans.getRelatedRecordId());
		aggregatedTrans.setProductSkuId(firstTrans.getProductSkuId());
		aggregatedTrans.setSupportTransUnitId(firstTrans.getSupportTransUnitId());
		aggregatedTrans.setStatus(firstTrans.getStatus());
		aggregatedTrans.setAllowDeleted(firstTrans.getAllowDeleted());

		// 聚合supportTransNum
		Integer totalSupportTransNum = transGroup.stream()
			.mapToInt(trans -> trans.getSupportTransNum() != null ? trans.getSupportTransNum() : 0)
			.sum();
		aggregatedTrans.setSupportTransNum(totalSupportTransNum);

		return aggregatedTrans;
	}

	@Override
	public List<WarehouseOutboundRecordVO> selectOutboundRecordsForDiffDetail(Long warehouseId, Long supplierId, Long productSkuId, String startTime, String endTime) {
		return baseMapper.selectOutboundRecordsForDiffDetail(warehouseId, supplierId, productSkuId, startTime, endTime);
	}

	@Override
	public List<SupplierReturnOutboundVO> getSupplierReturnDetailSummary(SupplierStatisticsQueryDTO queryDTO) {
		if (Func.isNull(queryDTO.getSupplierId()) || Func.isBlank(queryDTO.getDateTime())) {
			return List.of();
		}
		LocalDate localDate = LocalDate.parse(queryDTO.getDateTime());
		Long startTime = DateUtil.toMilliseconds(localDate.atStartOfDay());
		Long endTime = DateUtil.toMilliseconds(localDate.atTime(LocalTime.MAX));
		List<SupplierReturnOutboundVO> records = baseMapper.getSupplierReturnDetailSummary(queryDTO.getSupplierId(), startTime, endTime, queryDTO.getProductSkuId());
		if (Func.isEmpty(records)) {
			return records;
		}

		// 创建合计行
		SupplierReturnOutboundVO totalRow = new SupplierReturnOutboundVO();

		// 初始化数值
		totalRow.setQuantity(0);
		totalRow.setWeight(BigDecimal.ZERO);
		totalRow.setTotalAmount(BigDecimal.ZERO);

		// 累加
		for (SupplierReturnOutboundVO vo : records) {
			totalRow.setQuantity(totalRow.getQuantity() + (Func.notNull(vo.getQuantity()) ? vo.getQuantity() : 0));
			totalRow.setWeight(totalRow.getWeight().add(Optional.ofNullable(vo.getWeight()).orElse(BigDecimal.ZERO)));
			totalRow.setTotalAmount(totalRow.getTotalAmount().add(Func.notNull(vo.getTotalAmount()) ? vo.getTotalAmount() : BigDecimal.ZERO));
		}

		records.add(totalRow);
		return records;
	}
}
