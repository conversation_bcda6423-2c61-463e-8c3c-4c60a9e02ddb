package org.springblade.modules.business.cust.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SetUsageLimitDTO {

	@Schema(description = "id")
    private	Long id;
	@Schema(description = "金额")
	private BigDecimal limit;
	@Schema(description = "授信截止时间")
	private LocalDateTime creditEndTime;
}
