package org.springblade.modules.business.order.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 备货单详情列表（批量供应商入库查询）
 */
@Getter
@Setter
public class ReplenishmentOrderItemByTimeVO extends ReplenishmentOrderItemVO {
	@Schema(description = "供应商ID")
	private Long supplierId;

	@Schema(description = "供应商名称")
	private String supplierName;

	@Schema(description = "仓号")
	private String address;

	@Schema(description = "备货单号")
	private String replenishmentNo;

	@Schema(description = "报缺数量")
	private Integer missingQuantity;

	@Schema(description = "包装单位换算比率毛重（一个基本单位对应的毛重包装单位值）")
	private BigDecimal packageGrossConversionRate;

	@Schema(description = "配套运输品单价")
	private BigDecimal supportTransPrice;

	@Schema(description = "采购单商品明细ID")
	private Long purchaseOrderItemId;

	@Schema(description = "备货单明细ID")
	private Long replenishmentItemId;
}
