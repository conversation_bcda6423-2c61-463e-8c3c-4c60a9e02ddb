package org.springblade.modules.business.order.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
@Data
public class PurchaseOrderPageListVO {
	@JsonSerialize( using = ToStringSerializer.class )
	public Long id;
	/**
	 * 采购单号
	 */
	@Schema(description = "采购单号")
	private String purchaseNo;
	/**
	 * 采购总金额
	 */
	@Schema(description = "采购总金额")
	private Integer totalAmount;
	/**
	 * 采购总数量
	 */
	@Schema(description = "采购总数量")
	private Integer totalCount;
	/**
	 * 采购总重量
	 */
	@Schema(description = "采购总重量")
	private Integer totalWeight;
	/**
	 * 状态（0=草稿 1=待审核 2=已通过 3=已完成）
	 */
	@Schema(description = "状态（0=草稿 1=待审核 2=已通过 3=已完成）")
	private Integer purchaseStatus;
	/**
	 * 采购员id
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "采购员id")
	private Long purchaserId;
	/**
	 * 采购员名称
	 */
	@Schema(description = "采购员名称")
	private String purchaserName;
	/**
	 * 仓库ID （运输到的仓库）
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "仓库ID （运输到的仓库）")
	private Long warehouseId;
	/**
	 * 订单仓库ID
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "订单仓库ID")
	private Long orderWarehouseId;
	/**
	 * 来源仓库名称
	 */
	@Schema(description = "来源仓库名称")
	private String warehouseName;

	/**
	 * 去往仓库名称
	 */
	@Schema(description = "去往仓库名称")
	private String warehouseNameGo;

	/**
	 * 分配状态 1未分配完，2已分配 0全部
	 */
	@Schema(description = "分配状态 1未分配完 2已分配 0全部")
	private Integer allocationStatus;
	/**
	 * 分配状态 1未分配完，2已分配 0全部
	 */
	@Schema(description = "分配状态 1未分配完 2已分配 0全部")
	private String allocationStatusName;

	/**
	 * 截单时间
	 */
	@Schema(description = "截单时间")
	private String createTime;
}
