package org.springblade.modules.business.warehouse.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springblade.common.cache.TransportUnitCache;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.modules.business.product.pojo.entity.TransportUnitEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseSupportTransEntity;

/**
 * 批量入库商品明细
 */
@Getter
@Setter
public class WarehouseStoreBatchItemDTO extends WarehouseStoreItemDTO {

	@Schema(description = "供应商id")
	@NotNull(message = "供应商id不能为空")
	private Long supplierId;

	@Schema(description = "备货单id")
	@NotNull(message = "备货单id不能为空")
	private Long replenishmentId;

	@Schema(description = "备货单明细id")
	@NotNull(message = "备货单明细id不能为空")
	private Long replenishmentItemId;

	@Schema(description = "备货单号")
	@NotNull(message = "备货单号不能为空")
	private String replenishmentNo;

	@Schema(description = "采购单商品明细ID")
	@NotNull(message = "采购单商品明细ID不能为空")
	private Long purchaseOrderItemId;

	@Schema(description = "运输单位")
	private Long supportTransUnitId;

	@Schema(description = "配套运输数量")
	@Min(value = 0, message = "配套运输数量必须大于等于0")
	private Integer supportTransNum;

	public WarehouseSupportTransEntity convertToSupportTransEntity() {
		WarehouseSupportTransEntity supportTransEntity = new WarehouseSupportTransEntity();
		supportTransEntity.setProductSkuId(this.getProductSkuId());
		supportTransEntity.setSupportTransUnitId(this.getSupportTransUnitId());
		supportTransEntity.setSupplierId(this.getSupplierId());
		supportTransEntity.setSupportTransNum(this.getSupportTransNum());
		supportTransEntity.setBizType(BusinessConstant.WAREHOUSE_STORE);
		supportTransEntity.setAllowDeleted(BusinessConstant.ENABLE_STATUS);
		TransportUnitEntity transportUnit = TransportUnitCache.getById(this.getSupportTransUnitId());
		if (Func.notNull(transportUnit)) {
			supportTransEntity.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(transportUnit.getPrice()));
		}
		return supportTransEntity;
	}
}
