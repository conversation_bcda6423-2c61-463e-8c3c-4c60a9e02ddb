/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 销售订单详情原始数据表-支付成功时候的数据 实体类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("chy_sale_order_item_source")
@Schema(description = "SaleOrderItemSourceEntity对象")
@EqualsAndHashCode(callSuper = true)
public class SaleOrderItemSourceEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@Schema(description = "订单id")
	private Long orderId;
	/**
	 * 订单编号
	 */
	@Schema(description = "订单编号")
	private String orderNo;
	/**
	 * 仓库id
	 */
	@Schema(description = "仓库id")
	private Long warehouseId;
	/**
	 * 商品图片
	 */
	@Schema(description = "商品图片")
	private String productPic;
	/**
	 * 商品名称
	 */
	@Schema(description = "商品名称")
	private String productName;
	/**
	 * 商品品牌
	 */
	@Schema(description = "商品品牌")
	private String productBrand;
	/**
	 * 商品单位
	 */
	@Schema(description = "商品单位")
	private String productUnit;
	/**
	 * 销售价格（斤）
	 */
	@Schema(description = "销售价格（斤）")
	private Integer productSalePrice;
	/**
	 * 销售单价（件）
	 */
	@Schema(description = "销售单价（件）")
	private Integer productUnitPrice;
	/**
	 * 销售价格(商品总价格)
	 */
	@Schema(description = "销售价格(商品总价格)")
	private Integer productPrice;
	/**
	 * 购买数量
	 */
	@Schema(description = "购买数量")
	private Integer productQuantity;
	/**
	 * 商品sku
	 */
	@Schema(description = "商品sku")
	private Long productSkuId;
	/**
	 * 包装单位换算比率毛重（一个基本单位对应的毛重包装单位值）
	 */
	@Schema(description = "包装单位换算比率毛重（一个基本单位对应的毛重包装单位值）")
	private BigDecimal packageGrossConversionRate;
	/**
	 * 商品id
	 */
	@Schema(description = "商品id")
	private Long productId;
	/**
	 * 商品分类id
	 */
	@Schema(description = "商品分类id")
	private Long productCategoryId;
	/**
	 * 商品销售属性
	 */
	@Schema(description = "商品销售属性")
	private String productAttr;
	/**
	 * 差价类型（1.退差2.补差）
	 */
	@Schema(description = "差价类型（1.退差2.补差）")
	private Integer differenceType;
	/**
	 * 差价状态
	 */
	@Schema(description = "差价状态")
	private Integer differenceStatus;
	/**
	 * 差价金额
	 */
	@Schema(description = "差价金额")
	private Integer differenceAmount;
	/**
	 * 配套运输单价
	 */
	@Schema(description = "配套运输单价")
	private Integer supportTransPrice;
	/**
	 * 配套运输
	 */
	@Schema(description = "配套运输")
	private String supportTrans;
	/**
	 * 配套ID
	 */
	@Schema(description = "配套ID")
	private Long supportTransUnitId;
	/**
	 * 配套运输数量
	 */
	@Schema(description = "配套运输数量")
	private Integer supportTransNum;
	/**
	 * 取消订单时间
	 */
	@Schema(description = "取消订单时间")
	private LocalDateTime cancelTime;
	/**
	 * 取消订单人
	 */
	@Schema(description = "取消订单人")
	private Integer cancelUserId;
	/**
	 * 取消原因
	 */
	@Schema(description = "取消原因")
	private String cancelReason;
	/**
	 * sku编码
	 */
	@Schema(description = "sku编码")
	private String skuCode;
	/**
	 * 订单备注
	 */
	@Schema(description = "订单备注")
	private String note;
	/**
	 * 送货费用
	 */
	@Schema(description = "送货费用")
	private Integer deliveryExpense;
	/**
	 * 商品出库重量
	 */
	@Schema(description = "商品出库重量")
	private Integer productWeight;
	/**
	 * 是否标品：0->标品；1->非标品
	 */
	@Schema(description = "是否标品：0->标品；1->非标品")
	private Integer isStandard;
	/**
	 * 运输单位换算比率（一个基本单位对应的运输单位值）
	 */
	@Schema(description = "运输单位换算比率（一个基本单位对应的运输单位值）")
	private Integer transportConversionRate;
	/**
	 * 服务费用
	 */
	@Schema(description = "服务费用")
	private Integer serviceExpense;

}
