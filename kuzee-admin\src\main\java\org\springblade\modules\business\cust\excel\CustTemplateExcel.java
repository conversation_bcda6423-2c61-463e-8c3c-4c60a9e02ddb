package org.springblade.modules.business.cust.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 客户导入Excel模板
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
public class CustTemplateExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String index;

	@ExcelProperty("客户名称")
	private String custName;

	@ExcelProperty("客户联系人")
	private String contacts;

	@ExcelProperty("电话")
	private String phone;

	@ExcelProperty("分配区域")
	private String warehouseName;

	@ExcelProperty("信用额度")
	private String creditLimit;

	@ExcelProperty("还款周期")
	private String creditCycle;

	@ExcelProperty("业务员")
	private String salesmanName;

	@ExcelProperty("业务员手机")
	private String salesmanPhone;
}
