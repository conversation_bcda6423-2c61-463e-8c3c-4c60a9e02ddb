/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户额度使用记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_cust_credit_usage_record")
@Schema(description = "CustCreditUsageRecordEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustCreditUsageRecordEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long custId;
	/**
	 * 业务类型（充值，支付，退差等）枚举CreditUsagBizTypeEnum
	 */
	@Schema(description = "业务类型（充值，支付，退差等）枚举CreditUsagBizTypeEnum")
	private Integer bizType;
	/**
	 * 使用额度金额（用正负表示）
	 */
	@Schema(description = "使用额度金额（用正负表示）")
	private Integer usedAmount;
	/**
	 * 交易后可使用额度
	 */
	@Schema(description = "交易后可使用额度")
	private Integer balance;
	/**
	 * 关联业务编号
	 */
	@Schema(description = "关联业务编号")
	private String bizNo;
	/**
	 * 使用时间
	 */
	@Schema(description = "使用时间")
	private LocalDateTime usageTime;
	/**
	 * 使用描述（如“购买商品扣减额度”）
	 */
	@Schema(description = "使用描述（如“购买商品扣减额度”）")
	private String description;

	/**
	 * 订单编号
	 */
	@Schema(description = "订单编号")
	private String orderCode;
}
