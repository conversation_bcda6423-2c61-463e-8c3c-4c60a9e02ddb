package org.springblade.common.pdf;

import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import lombok.Data;
import org.springblade.common.pojo.vo.*;
import org.springblade.core.tool.utils.Func;
import org.springframework.core.io.ClassPathResource;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

public class PDFServerUtil {

	public static final float[] HEADER_COLUMN_WIDTHS = {5f, 15f, 10f, 70f};
	public static final float[] HEADER_COLUMN_WIDTHS2 = {20f, 10f, 10f, 10f, 30f};
	public static final float[] HEADER_COLUMN_WIDTHS3 = {3f, 20f, 8f, 8f, 10f, 12f, 8f, 12f, 12f, 10f, 6f, 8f, 12f, 12f};

	public static final float[] HEADER_COLUMN_WIDTHS4 = {10f, 10f, 10f, 10f, 10f, 11f, 11f, 10f, 10f, 10f};

	public static final float[] HEADER_COLUMN_WIDTHS5 = {10f, 11f, 10f, 11f, 10f, 11f, 10f, 11f};

	public static final float[] HEADER_COLUMN_WIDTHS6 = {10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f, 10f};


	public static final float[] HEADER_COLUMN_WIDTHS7 = {4f, 12f, 10f, 4f, 8f, 6f, 10f, 10f, 10f, 10f};

	public static final float[] HEADER_COLUMN_WIDTHS8 = {4f, 12f, 10f, 4f, 8f, 10f, 6f, 10f, 10f, 10f, 10f};

	public static final float[] HEADER_COLUMN_WIDTHS9 = {4f, 12f, 10f, 10f, 8f, 5f, 8f, 5f, 10f, 10f, 10f, 12f};

	public static final float HEADER_HEIHT = 13f;
	public static final float BODY_HEIHT = 20f;
	public static final int STALLS_PER_COLUMN = 2;
	public static final int COLUMNS_PER_STALL = 3; // 要货数量, 入库数量, 出库数量
	//private static final String FONT_PATH = "D:/03workJava/agrieplatform/backend/kuzee-admin/src/main/resources/fonts/simhei.ttf";
	public static final String FONT_PATH = "fonts/simhei.ttf";
	public static final String IMG_PATH = "img/title.png";

	public static Rectangle rectangle = PageSize.A5.rotate();

	public static void PDFServerUtil() {
		try {
			// Sample data
			List<Stall> stalls = new ArrayList<>();
			stalls.add(new Stall("档口A", "白菜", null, null, null, null));
			stalls.add(new Stall("档口B", "白菜", null, null, null, null));
			stalls.add(new Stall("档口C", "白菜", null, null, null, null));
			stalls.add(new Stall("档口A1", "白菜", null, null, null, null));
			stalls.add(new Stall("档口B1", "白菜", null, null, null, null));
			stalls.add(new Stall("档口C1", "白菜", null, null, null, null));
			stalls.add(new Stall("档口A2", "白菜", null, null, null, null));
			stalls.add(new Stall("档口B2", "白菜", null, null, null, null));
			stalls.add(new Stall("档口C2", "白菜", null, null, null, null));

			//generateOutboundOrder("单品出库单.pdf", stalls);
			//GapCustomerDeliveryNote("档口_客户出库单.pdf",null);
			PrintingSupplyOrder("百蔬帝-采购订单.pdf", null);
			System.out.println("PDF generated successfully!");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	//public static ByteArrayOutputStream  generateOutboundOrder(String filePath, List<Stall> stalls) throws DocumentException, IOException {
	public static void generateOutboundOrder(String filePath, Map<String, List<PrintOutboundVO>> stalls) throws DocumentException, IOException {
		Document document = new Document(PageSize.A5.rotate());
		PdfWriter.getInstance(document, new FileOutputStream(filePath));
		document.open();
		// 创建文档对象
//		Document document = new Document();
//		// 使用ByteArrayOutputStream代替FileOutputStream
//		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//		// 获取PDF写入器
//		PdfWriter.getInstance(document, outputStream);
//		// 打开文档
//		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);
		// Add title and date
		addTitleAndDate(document, titleFont, titleRightFont, "单品出库单");

		// Create main table
		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS);
		mainTable.setWidthPercentage(100);

		// Add header row
		addMainHeaderCells(mainTable, headerFont, "序号,品名,总订单量,档口详情");
		Integer ic = 0;
		for (Map.Entry<String, List<PrintOutboundVO>> entry : stalls.entrySet()) {
			ic++;
			addDataRows(mainTable, entry.getValue(), chineseFont, ic.toString(), 1);
		}

		document.add(mainTable);
		addFooter(document, footFont);
		document.close();

//		return outputStream;
	}


	/**
	 * 1、单品出库单
	 *
	 * @param filePath
	 * @param stalls
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream SingleItemOutboundOrder(String filePath, Map<String, List<PrintOutboundVO>> stalls) throws DocumentException, IOException {
//		Document document = new Document(PageSize.A5.rotate());
//		PdfWriter.getInstance(document, new FileOutputStream(filePath));
//		document.open();
		// 创建文档对象
		Document document = new Document(rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter.getInstance(document, outputStream);
		// 打开文档
		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);
		// Add title and date
//		addTitleAndDate(document, titleFont, chineseFont, "单品出库单");
		String title = "单品出库单";
		String dateStr = "日期：     年    月    日";

		document.add(addTitleAndDate(null, titleFont, titleRightFont, title, dateStr, 0));

		// Create main table
		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS);
		mainTable.setWidthPercentage(100);

		// Add header row
		addMainHeaderCells(mainTable, headerFont, "序号,品名,总订单量,档口详情");
		Integer ic = 0;
		for (Map.Entry<String, List<PrintOutboundVO>> entry : stalls.entrySet()) {
			ic++;
			addDataRows(mainTable, entry.getValue(), chineseFont, ic.toString(), 1);
		}

		document.add(mainTable);
		addFooter(document, footFont);
		document.close();

		return outputStream;
	}

	/**
	 * 2、档口和客户出库单
	 *
	 * @param filePath
	 * @param stalls
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream GapCustomerDeliveryNote(String filePath, Map<String, List<PrintOutboundVO>> stalls) throws DocumentException, IOException {
//		Document document = new Document(PageSize.A5.rotate());
//		PdfWriter.getInstance(document, new FileOutputStream(filePath));
//		document.open();
		// 创建文档对象
		Document document = new Document(rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter.getInstance(document, outputStream);
		// 打开文档
		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);

		String title = "档口/客户出库单";
		String dateStr = "日期：     年    月    日";

		document.add(addTitleAndDate(null, titleFont, titleRightFont, title, dateStr, 0));
		// Add title and date
		//addTitleAndDate(document, titleFont, chineseFont, "档口/客户出库单");

		// Create main table
		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS2);
		mainTable.setWidthPercentage(100);

		for (Map.Entry<String, List<PrintOutboundVO>> entry : stalls.entrySet()) {
			addMainHeaderCells(mainTable, headerFont, entry.getKey() + ",要货数量,出库数量,出库重量,备注");
			addDataRows(mainTable, entry.getValue(), chineseFont, "1", 2);
		}
		document.add(mainTable);
		addFooter(document, footFont);
		document.close();

		return outputStream;
	}

	/**
	 * 3、供货单打印
	 *
	 * @param filePath
	 * @param stalls
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream PrintingSupplyOrder(String filePath, List<PrintSupplyOrderVO> stalls) throws DocumentException, IOException {
//		Document document = new Document(PageSize.A5.rotate());
//		PdfWriter.getInstance(document, new FileOutputStream(filePath));
//		document.open();
		// 创建文档对象
		Document document = new Document(rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);

		// 2. 添加页码事件处理器
		writer.setPageEvent(new PDFPageUtil.PageNumberFooter());

		// 打开文档
		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);

		String title = "采购订单";

//		Date createTime = stalls.getCreateTime();
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(createTime);
//
//		int year = calendar.get(Calendar.YEAR); // 获取年份
//		int month = calendar.get(Calendar.MONTH) + 1; // 获取月份（0-11），所以要加1
//		int day = calendar.get(Calendar.DAY_OF_MONTH); // 获取月中第几天（1-31）
//
//		String dateStr = stalls.getReplenishmentNo() + "\n\n 订单日期： "+year+" 年 "+month+" 月 "+day+" 日";
//		document.add(addTitleAndDate(titleFont, titleRightFont, title, dateStr));
//
//
//		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS3.length);
//		mainTable.setWidthPercentage(100);
//		mainTable.setWidths(HEADER_COLUMN_WIDTHS3);
//
//		addOrderTableHeader(mainTable, headerFont, stalls);
//
//
//		addDataRowsGXS(mainTable, stalls.getData(), chineseFont, "1", 3);
//
//		document.add(mainTable);
//		addFooterGXS(document, footFont, stalls.getPurchaser());
		document.close();

		return outputStream;
	}

	/**
	 * 4、供应商供货入库单
	 *
	 * @param filePath
	 * @param dto
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream SupplierSupplyReceipt(String filePath, PrintSupplierSupplyReceiptDVO dto, List<PrintSupplierSupplyReceiptVO> list, List<String> gysList) throws DocumentException, IOException {
//		Document document = new Document(PageSize.A5.rotate());
//		PdfWriter.getInstance(document, new FileOutputStream(filePath));
//		document.open();
		// 创建文档对象
		Document document = new Document(rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter.getInstance(document, outputStream);
		// 打开文档
		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);
		// Add title and date

		String title = "供应商供货入库单";
		String dateStr = "供货单日期：" + dto.getDate();
		document.add(addTitleAndDate(null, titleFont, titleRightFont, title, dateStr, 0));

		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS4.length);
		mainTable.setWidthPercentage(100);
		mainTable.setWidths(HEADER_COLUMN_WIDTHS4);

		supplierTableHeader(mainTable, headerFont);

		addDataRowsSupplier(mainTable, list, chineseFont);

		document.add(mainTable);
		addFooterSupplier(document, footFont, dto.getWarehouseName(), dto.getAddress(), gysList);
		document.close();

		return outputStream;
	}


	/**
	 * 5、销售出库单
	 *
	 * @param filePath
	 * @param dto
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream SalesOutboundOrder(String filePath, SalesOutboundOrderVO dto, List<SalesOutboundOrderListVO> list) throws DocumentException, IOException {
//		Document document = new Document(PageSize.A5.rotate());
//		PdfWriter.getInstance(document, new FileOutputStream(filePath));
//		document.open();
		// 创建文档对象
		Document document = new Document(rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter.getInstance(document, outputStream);
		// 打开文档
		document.open();
		// Load Chinese font
		String resource = new ClassPathResource(FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 5);
		//表头
		Font headerFont = new Font(baseFont, 5, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 8);
		// Add title and date
		//addTitleAndDate(document, titleFont, chineseFont,"百蔬帝-采购订单");

		// 创建一个包含2列的表格，宽度为100%
		String title = "销售出库单";
		String dateStr = "下单时间：" + dto.getDate() + " \n 订单编号：" + dto.getOrderNo();

		document.add(addTitleAndDate(null, titleFont, titleRightFont, title, dateStr, 0));


		PdfPTable mainTable = new PdfPTable(HEADER_COLUMN_WIDTHS5.length);
		mainTable.setWidthPercentage(100);
		mainTable.setWidths(HEADER_COLUMN_WIDTHS5);

		SalesOutboundOrderHeader(mainTable, headerFont, dto);

		PdfPTable mainTable1 = new PdfPTable(HEADER_COLUMN_WIDTHS6.length);
		mainTable1.setWidthPercentage(100);
		mainTable1.setWidths(HEADER_COLUMN_WIDTHS6);

		SalesOutboundOrderHeader2(mainTable1, headerFont);

		document.add(mainTable);
		document.add(newlineTable());

		addDataRowsSalesOut(mainTable1, list, chineseFont);
		document.add(mainTable1);
		addFooterSupplier1(document, footFont, dto.getWarehouseName(), dto.getAddress());
		document.close();

		return outputStream;
	}

	/**
	 * 绑定数据
	 *
	 * @param table
	 * @param stalls
	 * @param font
	 */
	private static void addDataRowsSalesOut(PdfPTable table, List<SalesOutboundOrderListVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, String.valueOf(j + 1), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getGrossWeight()) ? " " : String.valueOf(stalls.get(j).getGrossWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getNetWeight()) ? " " : String.valueOf(stalls.get(j).getNetWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOrderNum()) ? " " : String.valueOf(stalls.get(j).getOrderNum()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundNum()) ? " " : String.valueOf(stalls.get(j).getOutboundNum()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getUnitPrice()) ? " " : String.valueOf(stalls.get(j).getUnitPrice()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSubtotal()) ? " " : String.valueOf(stalls.get(j).getSubtotal()), font);
				if (Func.isNotEmpty(stalls.get(j).getSupportTrans())) {
					addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTrans()) ? " " : String.valueOf(stalls.get(j).getSupportTrans()), font);
					addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTransUnitPrice()) ? " " : String.valueOf(stalls.get(j).getSupportTransUnitPrice()), font);
					addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTransOrderNum()) ? " " : String.valueOf(Func.isNotEmpty(stalls.get(j).getSupportTransOrderNum()) ? stalls.get(j).getSupportTransOrderNum() : " "), font);
					addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTransOutboundNum()) ? " " : String.valueOf(stalls.get(j).getSupportTransOutboundNum()), font);
				} else {
					addCenteredCell(table, " ", font);
					addCenteredCell(table, " ", font);
					addCenteredCell(table, " ", font);
					addCenteredCell(table, " ", font);
				}
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getDifference()) ? " " : String.valueOf(stalls.get(j).getDifference()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}

	/**
	 * 绑定数据
	 *
	 * @param table
	 * @param stalls
	 * @param font
	 */
	private static void addDataRowsSupplier(PdfPTable table, List<PrintSupplierSupplyReceiptVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupplierName()) ? " " : String.valueOf(stalls.get(j).getSupplierName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getWarehouseName()) ? " " : String.valueOf(stalls.get(j).getWarehouseName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupplyOrderNo()) ? " " : String.valueOf(stalls.get(j).getSupplyOrderNo()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundNum()) ? " " : String.valueOf(stalls.get(j).getOutboundNum()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getInboundNum()) ? " " : String.valueOf(stalls.get(j).getInboundNum()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getTotalWt()) ? " " : String.valueOf(stalls.get(j).getTotalWt()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTrans()) ? " " : String.valueOf(stalls.get(j).getSupportTrans()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTransNum()) ? " " : String.valueOf(stalls.get(j).getSupportTransNum()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupportTransInboundNum()) ? " " : String.valueOf(stalls.get(j).getSupportTransInboundNum()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}

	/**
	 * 供应商供货入库单 页脚
	 *
	 * @param document
	 * @param font
	 * @param warehouseName
	 */
	public static void addFooterSupplier(Document document, Font font, String warehouseName, String address, List<String> gysList) {
		PdfPTable table = new PdfPTable(4);
		table.setWidthPercentage(100);
		// 设置列宽（可以根据需要调整比例）
		table.setWidths(new float[]{1f, 1f, 1f, 1f});

		Paragraph title = new Paragraph("档口：" + warehouseName, font);
		title.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell = new PdfPCell(title);
		dateCell.setColspan(2);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("转运：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("仓库签字：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		Paragraph title1 = new Paragraph("地址：" + address, font);
		title1.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell1 = new PdfPCell(title1);
		dateCell1.setColspan(4);
		dateCell1.setBorder(PdfPCell.NO_BORDER);
		dateCell1.setFixedHeight(30f);
		dateCell1.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell1.setVerticalAlignment(Element.ALIGN_TOP);

		table.addCell(dateCell1);

		Paragraph title2 = new Paragraph("供应商：_____________", font);
		for (int i = 0; i < gysList.size(); i++) {
			title2.setAlignment(Element.ALIGN_CENTER);
			dateCell = new PdfPCell(title2);
			dateCell.setBorder(PdfPCell.NO_BORDER);
			dateCell.setFixedHeight(30f);
			dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
			dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			table.addCell(dateCell);
		}
		//去余
		int num = 4 - gysList.size() % 4;
		for (int i = 0; i < num; i++) {
			title2 = new Paragraph("", font);
			title2.setAlignment(Element.ALIGN_CENTER);
			dateCell = new PdfPCell(title2);
			dateCell.setBorder(PdfPCell.NO_BORDER);
			title2.setAlignment(Element.ALIGN_CENTER);
			table.addCell(dateCell);
		}

		document.add(table);
	}

	/**
	 * 供应商供货入库单 页脚
	 *
	 * @param document
	 * @param font
	 * @param warehouseName
	 */
	public static void addFooterSupplier1(Document document, Font font, String warehouseName, String address) {
		PdfPTable table = new PdfPTable(2);
		table.setWidthPercentage(100);
		// 设置列宽（可以根据需要调整比例）
		table.setWidths(new float[]{1f, 1f});

		Paragraph title = new Paragraph("档口：" + warehouseName, font);
		title.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("仓库签字：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		Paragraph title1 = new Paragraph("地址：" + address, font);
		title1.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell1 = new PdfPCell(title1);
		dateCell1.setColspan(2);
		dateCell1.setBorder(PdfPCell.NO_BORDER);
		dateCell1.setFixedHeight(30f);
		dateCell1.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell1.setVerticalAlignment(Element.ALIGN_TOP);

		table.addCell(dateCell1);

		document.add(table);
	}

	/**
	 * 添加空行
	 *
	 * @return
	 */
	private static PdfPTable newlineTable() {
		PdfPTable mainTablez = new PdfPTable(1);
		mainTablez.setWidthPercentage(100);
		mainTablez.setWidths(new float[]{1f});
		PdfPCell dateCell1 = new PdfPCell();
		dateCell1.setBorder(PdfPCell.NO_BORDER);
		dateCell1.setFixedHeight(20f);
		mainTablez.addCell(dateCell1);
		return mainTablez;
	}

	/**
	 * 标题格式确定
	 *
	 * @param titleFont   头部字体
	 * @param chineseFont 正文字体
	 * @param titleName   标题
	 * @param dateStr     日期/编号
	 * @return
	 * @throws IOException
	 */
	public static PdfPTable addTitleAndDate(PdfPTable table, Font titleFont, Font chineseFont, String titleName, String dateStr, int type) throws IOException {
		if (table == null) {
			table = new PdfPTable(3);
			table.setWidthPercentage(100);
			// 设置列宽（可以根据需要调整比例）
			table.setWidths(new float[]{1f, 1f, 1f});
		}
		String resourceImg = new ClassPathResource(IMG_PATH).getURL().toString();
		// 加载图片
		Image image = Image.getInstance(resourceImg); // 修改为实际图片路径
		// 设置图片尺寸（可选）
		image.scaleToFit(120, 120);
		// 将图片放入单元格中
		PdfPCell imageCell = new PdfPCell(image);
		// 可以设置单元格的对齐方式等属性
		imageCell.setBorder(PdfPCell.NO_BORDER);
		imageCell.setFixedHeight(40f);
		switch (type) {
			case 1:
				imageCell.setColspan(5);
				break;
			case 7:
				imageCell.setColspan(3);
				break;
			case 8:
				imageCell.setColspan(3);
				break;
			case 9:
				imageCell.setColspan(4);
				break;
			default:
				break;
		}
		imageCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		imageCell.setVerticalAlignment(Element.ALIGN_LEFT);

		Paragraph title = new Paragraph(titleName, titleFont);
		title.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(40f);
		switch (type) {
			case 1:
				dateCell.setColspan(6);
				break;
			case 7:
				dateCell.setColspan(4);
				break;
			case 8:
				dateCell.setColspan(5);
				break;
			case 9:
				dateCell.setColspan(5);
				break;
			default:
				break;
		}
		dateCell.setHorizontalAlignment(Element.ALIGN_CENTER);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);

		Paragraph orderNumber = new Paragraph(dateStr, chineseFont);
		orderNumber.setAlignment(Element.ALIGN_CENTER);
		PdfPCell numberCell = new PdfPCell(orderNumber);
		numberCell.setBorder(PdfPCell.NO_BORDER);
		numberCell.setFixedHeight(40f);
		 switch (type) {
			case 1:
				numberCell.setColspan(4);
				break;
			case 7:
				numberCell.setColspan(3);
				break;
			case 8:
				numberCell.setColspan(3);
				break;
			case 9:
				numberCell.setColspan(3);
				break;
			default:
				break;
		}
		numberCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
		numberCell.setVerticalAlignment(Element.ALIGN_MIDDLE);

		// 添加单元格到表格
		table.addCell(imageCell);
		table.addCell(dateCell);
		table.addCell(numberCell);
		return table;
	}

	/**
	 * 销售出库单头部
	 *
	 * @param table
	 * @param font
	 */
	private static void SalesOutboundOrderHeader(PdfPTable table, Font font, SalesOutboundOrderVO dto) {

		table.addCell(createCell("客户名称", font, 1, false));
		table.addCell(createCell(dto.getCustomerName(), font, 1, true));
		table.addCell(createCell("收货人", font, 1, false));
		table.addCell(createCell(dto.getReceiver(), font, 1, true));
		table.addCell(createCell("收货电话", font, 1, false));
		table.addCell(createCell(dto.getReceiverPhone(), font, 1, true));
		table.addCell(createCell("业务员", font, 1, false));
		table.addCell(createCell(dto.getSalesman(), font, 1, true));

		table.addCell(createCell("提货方式", font, 1, false));
		table.addCell(createCell(dto.getTakeMethod(), font, 1, true));
		table.addCell(createCell("收货地址", font, 1, false));
		table.addCell(createCellR(dto.getTakeAddress(), font, 5, true));
	}

	/**
	 * 销售出库单头部
	 *
	 * @param table
	 * @param font
	 */
	private static void SalesOutboundOrderHeader2(PdfPTable table, Font font) {

		table.addCell(createCell("序号", font, 1, false));
		table.addCell(createCell("商品", font, 1, false));
		table.addCell(createCell("毛重", font, 1, false));
		table.addCell(createCell("净重", font, 1, false));
		table.addCell(createCell("下单量", font, 1, false));
		table.addCell(createCell("出库量", font, 1, false));
		table.addCell(createCell("单价", font, 1, false));
		table.addCell(createCell("金额小计", font, 1, false));

		table.addCell(createCell("配套运输品", font, 1, false));
		table.addCell(createCell("单价", font, 1, false));
		table.addCell(createCell("下单量", font, 1, false));
		table.addCell(createCell("出库量", font, 1, false));
		table.addCell(createCell("差额", font, 1, false));
	}

	/**
	 * 创建单元格
	 *
	 * @param text
	 * @param font
	 * @param colspan
	 * @param isW
	 * @return
	 */
	private static PdfPCell createCell(String text, Font font, int colspan, boolean isW) {
		PdfPCell indexHeader = new PdfPCell(new Phrase(text, font));
		if (colspan > 1)
			indexHeader.setRowspan(colspan);
		indexHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		indexHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		if (!isW)
			indexHeader.setBackgroundColor(new Color(220, 220, 220));
		return indexHeader;
	}

	/**
	 * 创建单元格
	 *
	 * @param text
	 * @param font
	 * @param colspan
	 * @param isW
	 * @return
	 */
	private static PdfPCell createCellR(String text, Font font, int colspan, boolean isW) {
		PdfPCell indexHeader = new PdfPCell(new Phrase(text, font));
		if (colspan > 1)
			indexHeader.setColspan(colspan);
		indexHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		indexHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		if (!isW)
			indexHeader.setBackgroundColor(new Color(220, 220, 220));
		return indexHeader;
	}

	/**
	 * 供应商供货单头部
	 *
	 * @param table
	 * @param font
	 */
	private static void supplierTableHeader(PdfPTable table, Font font) {


		table.addCell(createCell("供应商", font));

		table.addCell(createCell("仓号", font));

		table.addCell(createCell("商品名", font));

		table.addCell(createCell("供货单号", font));

		table.addCell(createCell("送货量", font));

		PdfPCell indexHeader1 = new PdfPCell(new Phrase("到货", font));
		indexHeader1.setColspan(2);
		indexHeader1.setHorizontalAlignment(Element.ALIGN_CENTER);
		indexHeader1.setVerticalAlignment(Element.ALIGN_MIDDLE);
		indexHeader1.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(indexHeader1);

		table.addCell(createCell("配套运输品", font));

		table.addCell(createCell("送货量", font));

		table.addCell(createCell("到货量", font));

		addHeaderCell(table, "到货量", font);
		addHeaderCell(table, "总重量(斤)", font);

	}

	/**
	 * 创建单元格
	 *
	 * @param text
	 * @param font
	 * @return
	 */
	private static PdfPCell createCell(String text, Font font) {
		PdfPCell indexHeader = new PdfPCell(new Phrase(text, font));
		indexHeader.setRowspan(2);
		indexHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		indexHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		indexHeader.setBackgroundColor(new Color(220, 220, 220));
		return indexHeader;
	}

	/**
	 * 添加订单表头
	 *
	 * @param table
	 * @param font
	 * @param stalls
	 */
	public static void addOrderTableHeader(PdfPTable table, Font font, PrintSupplyOrderVO stalls) {
		// First row - simple headers
		PdfPCell gxsHeader = new PdfPCell(new Phrase("供应商名称", font));
		gxsHeader.setColspan(3);
		gxsHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		gxsHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		gxsHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		gxsHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(gxsHeader);
		PdfPCell gxsNameHeader = new PdfPCell(new Phrase(stalls.getSupplierName(), font));
		gxsNameHeader.setColspan(4);
		gxsNameHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		gxsNameHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
		gxsNameHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		gxsNameHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(gxsNameHeader);
		PdfPCell phoneHeader = new PdfPCell(new Phrase("联系电话", font));
		phoneHeader.setColspan(3);
		phoneHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		phoneHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		phoneHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		phoneHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(phoneHeader);
		PdfPCell phoneInfoHeader = new PdfPCell(new Phrase(stalls.getSupplierPhone(), font));
		phoneInfoHeader.setColspan(4);
		phoneInfoHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		phoneInfoHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
		phoneInfoHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		phoneInfoHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(phoneInfoHeader);


		PdfPCell gxsAddressHeader = new PdfPCell(new Phrase("供应商地址", font));
		gxsAddressHeader.setColspan(3);
		gxsAddressHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		gxsAddressHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		gxsAddressHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		gxsAddressHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(gxsAddressHeader);
		PdfPCell gxsAddressNameHeader = new PdfPCell(new Phrase(stalls.getSupplierAddress(), font));
		gxsAddressNameHeader.setColspan(11);
		gxsAddressNameHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		gxsAddressNameHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
		gxsAddressNameHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		gxsAddressNameHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(gxsAddressNameHeader);


		PdfPCell indexHeader = new PdfPCell(new Phrase("序", font));
		//indexHeader.setRowspan(2);
		indexHeader.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		indexHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		indexHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		indexHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(indexHeader);
		PdfPCell nameHeader = new PdfPCell(new Phrase("品名", font));
		//nameHeader.setRowspan(2);
		nameHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		nameHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		nameHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(nameHeader);
		// Merge cells for 采购 section
		PdfPCell purchaseHeader = new PdfPCell(new Phrase("计量", font));
		//purchaseHeader.setRowspan(2);
		purchaseHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		purchaseHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		purchaseHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(purchaseHeader);
		purchaseHeader = new PdfPCell(new Phrase("采购件数", font));
		//purchaseHeader.setRowspan(2);
		purchaseHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		purchaseHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		purchaseHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(purchaseHeader);
		purchaseHeader = new PdfPCell(new Phrase("采购重量", font));
		//purchaseHeader.setRowspan(2);
		purchaseHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		purchaseHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		purchaseHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(purchaseHeader);
		purchaseHeader = new PdfPCell(new Phrase("单价", font));
		//purchaseHeader.setRowspan(2);
		purchaseHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
		purchaseHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
		purchaseHeader.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(purchaseHeader);

		// Merge cells for 搬运 section
//		PdfPCell transportHeader = new PdfPCell(new Phrase("搬运", font));
//		transportHeader.setColspan(8);
//		transportHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
//		transportHeader.setBackgroundColor(new Color(220, 220, 220));
//		table.addCell(transportHeader);

		addHeaderCell(table, "件数", font);
		addHeaderCell(table, "重量", font);
		addHeaderCell(table, "小计", font);

		addHeaderCell(table, "运输品", font);
		addHeaderCell(table, "采购量", font);
		addHeaderCell(table, "数量", font);
		addHeaderCell(table, "小计", font);
		addHeaderCell(table, "备注", font);

	}

	private static void addSupplierCell(PdfPTable table, String label, String value, Font labelFont, Font valueFont) {
		PdfPCell labelCell = new PdfPCell(new Phrase(label, labelFont));
		labelCell.setBorder(Rectangle.NO_BORDER);
		table.addCell(labelCell);

		PdfPCell valueCell = new PdfPCell(new Phrase(value, valueFont));
		valueCell.setBorder(Rectangle.NO_BORDER);
		table.addCell(valueCell);
	}

	private static void addHeaderCell1(PdfPTable table, String text, Font font) {
		PdfPCell cell = new PdfPCell(new Phrase(text, font));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setBackgroundColor(new Color(240, 240, 240));
		table.addCell(cell);
	}

	private static void addEmptyCell(PdfPTable table) {
		PdfPCell cell = new PdfPCell();
		cell.setBorder(Rectangle.NO_BORDER);
		table.addCell(cell);
	}

	/**
	 * 添加标题和日期
	 *
	 * @param document
	 * @param titleFont
	 * @param chineseFont
	 * @param titleName
	 * @throws DocumentException
	 */
	private static void addTitleAndDate(Document document, Font titleFont, Font chineseFont, String titleName) throws DocumentException {
		Paragraph title = new Paragraph(titleName, titleFont);
		title.setAlignment(Element.ALIGN_CENTER);
		document.add(title);
		document.add(Chunk.NEWLINE);

		Paragraph date = new Paragraph(String.format("日期：        年       月      日"), chineseFont);
		PdfPTable table = new PdfPTable(1);
		table.setWidthPercentage(100);
		// 设置列宽（可以根据需要调整比例）
		table.setWidths(new float[]{1f});
		// 第一列：日期（左对齐）
		PdfPCell dateCell = new PdfPCell(date);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(20f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		table.addCell(dateCell);
		document.add(table);

	}

	/**
	 * 添加主表头
	 *
	 * @param table
	 * @param font
	 * @param titleNameStr
	 */
	public static void addMainHeaderCells(PdfPTable table, Font font, String titleNameStr) {
		List<String> headerNames = Arrays.stream(titleNameStr.split(",")).toList();
		for (String name : headerNames) {
			addHeaderCell(table, name, font);
		}
	}

	/**
	 * 添加数据行
	 *
	 * @param mainTable
	 * @param stalls
	 * @param font
	 * @param num
	 * @param type
	 */
	public static void addDataRowsGXS(PdfPTable mainTable, List<PrintSupplyOrderListVO> stalls, Font font, String num, Integer type) {
		addStallDataGXS(mainTable, stalls, font);
	}

	/**
	 * 添加数据行
	 *
	 * @param mainTable
	 * @param stalls
	 * @param font
	 * @param num
	 * @param type
	 */
	private static void addDataRows(PdfPTable mainTable, List<PrintOutboundVO> stalls, Font font, String num, Integer type) {

		switch (type) {
			case 1:
				// Add sequence number
				addCenteredCell(mainTable, num, font);

				// Add product name (merged for all stalls)
				String productName = stalls.isEmpty() ? "" : stalls.get(0).getProductName();
				addCenteredCell(mainTable, productName, font);

				// Add total order quantity (merged for all stalls)
				int totalOrder = stalls.stream().filter(i -> Func.isNotEmpty(i.getTotalOrder())).mapToInt(PrintOutboundVO::getTotalOrder).sum();
				addCenteredCell(mainTable, String.valueOf(totalOrder), font);
				// Create a nested table for stall details
				int totalColumnsNeeded = STALLS_PER_COLUMN * COLUMNS_PER_STALL;
				PdfPTable stallsTable = new PdfPTable(totalColumnsNeeded);
				stallsTable.setWidthPercentage(100);
				// Add stall headers and subheaders
				addStallData(stallsTable, stalls, font);
				// Add the stalls table to the main table cell
				PdfPCell stallsCell = new PdfPCell(stallsTable);
				mainTable.addCell(stallsCell);
				break;
			case 2:
				addStallDataRow(mainTable, stalls, font);
				break;
			case 3:

				break;
			default:

				break;

		}


	}

	private static void addStallDataRow(PdfPTable table, List<PrintOutboundVO> stalls, Font font) {
		for (int j = 0; j < stalls.size(); j++) {
			addCenteredCell(table, String.valueOf(stalls.get(j).getProductName()), font);
			addCenteredCell(table, String.valueOf(stalls.get(j).getTotalOrder()), font);
			addCenteredCell(table, "", font);
			addCenteredCell(table, "", font);
			addCenteredCell(table, "", font);
		}
	}

	private static void addStallDataGXS(PdfPTable table, List<PrintSupplyOrderListVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, String.valueOf(j + 1), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getMetering()) ? " " : String.valueOf(stalls.get(j).getMetering()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPurchaseNumber()) ? " " : String.valueOf(stalls.get(j).getPurchaseNumber()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPurchaseWeight()) ? " " : String.valueOf(stalls.get(j).getPurchaseWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPrice()) ? " " : String.valueOf(stalls.get(j).getPrice()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPieces()) ? " " : String.valueOf(stalls.get(j).getPieces()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getWeight()) ? " " : String.valueOf(stalls.get(j).getWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSubtotal()) ? " " : String.valueOf(stalls.get(j).getSubtotal()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getTransport()) ? " " : String.valueOf(stalls.get(j).getTransport()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOrderQuantity()) ? " " : String.valueOf(stalls.get(j).getOrderQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getQuantity()) ? " " : String.valueOf(stalls.get(j).getQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSubtotal1()) ? " " : String.valueOf(stalls.get(j).getSubtotal1()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getRemark()) ? " " : String.valueOf(stalls.get(j).getRemark()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}

	private static void addStallData(PdfPTable table, List<PrintOutboundVO> stalls, Font font) {

		for (int j = 0; j < stalls.size(); j++) {
			for (int i = 0; i < STALLS_PER_COLUMN; i++) {
				if (j + i >= stalls.size()) {
					PdfPCell stallNameCell = new PdfPCell(new Phrase(" ", font));
					stallNameCell.setColspan(COLUMNS_PER_STALL);
					stallNameCell.setHorizontalAlignment(Element.ALIGN_CENTER);
					stallNameCell.setBackgroundColor(new Color(230, 230, 230));
					table.addCell(stallNameCell);
					continue;
				}
				PdfPCell stallNameCell = new PdfPCell(new Phrase(stalls.get(j + i).getWarehouseName(), font));
				stallNameCell.setColspan(COLUMNS_PER_STALL);
				stallNameCell.setHorizontalAlignment(Element.ALIGN_CENTER);
				stallNameCell.setBackgroundColor(new Color(230, 230, 230));
				table.addCell(stallNameCell);
			}
			for (int i = 0; i < STALLS_PER_COLUMN; i++) {
				if (j + i >= stalls.size()) {
					addSubHeaderCell(table, " ", font);
					addSubHeaderCell(table, " ", font);
					addSubHeaderCell(table, " ", font);
					continue;
				}
				addSubHeaderCell(table, "要货数量", font);
				addSubHeaderCell(table, "出库数量", font);
				addSubHeaderCell(table, "出库重量", font);
			}
			for (int i = 0; i < STALLS_PER_COLUMN; i++) {
				if (j + i >= stalls.size()) {
					addCenteredCell(table, " ", font);
					addCenteredCell(table, " ", font);
					addCenteredCell(table, " ", font);
					continue;
				}
				addCenteredCell(table, String.valueOf(stalls.get(j + i).getTotalOrder()), font);
				addCenteredCell(table, " ", font);
				addCenteredCell(table, " ", font);
			}
			j = (j + STALLS_PER_COLUMN) - 1;
		}

		// Fill empty cells if needed
		int remaining = STALLS_PER_COLUMN - (stalls.size() % STALLS_PER_COLUMN);
		if (remaining < STALLS_PER_COLUMN && remaining > 0) {
			for (int i = 0; i < remaining * COLUMNS_PER_STALL; i++) {
				addCenteredCell(table, "", font);
			}
		}
	}

	private static void addHeaderCell(PdfPTable table, String text, Font font) {
		PdfPCell cell = new PdfPCell(new Phrase(text, font));
		cell.setMinimumHeight(PDFServerUtil.HEADER_HEIHT);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setBackgroundColor(new Color(220, 220, 220));
		table.addCell(cell);
	}

	private static void addSubHeaderCell(PdfPTable table, String text, Font font) {
		PdfPCell cell = new PdfPCell(new Phrase(text, font));
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setBackgroundColor(new Color(240, 240, 240));
		table.addCell(cell);
	}

	private static void addCenteredCell(PdfPTable table, String text, Font font) {
		PdfPCell cell = new PdfPCell(new Phrase(text, font));
		cell.setMinimumHeight(PDFServerUtil.BODY_HEIHT);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(cell);
	}

	private static void addFooter(Document document, Font font) throws DocumentException {
		//document.add(Chunk.NEWLINE);
		document.add(new Paragraph("", font));
		String footerText = "分拣：                             仓库：";
		document.add(new Paragraph(footerText, font));
	}

	public static void addFooterGXS(Document document, Font font, String userName) throws DocumentException {
		//document.add(Chunk.NEWLINE);
//		document.add(new Paragraph("", font));
//		String footerText = "采购员：" + userName + "            搬运：            供应商：             仓库：";
//		document.add(new Paragraph(footerText, font));
//		document.add(new Paragraph("", font));
//		String footerText1 = "总金额：               备注：";
//		document.add(new Paragraph(footerText1, font));


		PdfPTable table = new PdfPTable(4);
		table.setWidthPercentage(100);
		// 设置列宽（可以根据需要调整比例）
		table.setWidths(new float[]{1f, 1f, 1f, 1f});

		Paragraph title = new Paragraph("搬运：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("供应商：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("仓库：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		title = new Paragraph("总金额：_____________", font);
		title.setAlignment(Element.ALIGN_CENTER);
		dateCell = new PdfPCell(title);
		dateCell.setBorder(PdfPCell.NO_BORDER);
		dateCell.setFixedHeight(30f);
		dateCell.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		table.addCell(dateCell);

		Paragraph title1 = new Paragraph("备注：________________________________________________________________________________________", font);
		title1.setAlignment(Element.ALIGN_CENTER);
		PdfPCell dateCell1 = new PdfPCell(title1);
		dateCell1.setColspan(4);
		dateCell1.setBorder(PdfPCell.NO_BORDER);
		dateCell1.setFixedHeight(30f);
		dateCell1.setHorizontalAlignment(Element.ALIGN_LEFT);
		dateCell1.setVerticalAlignment(Element.ALIGN_MIDDLE);

		table.addCell(dateCell1);

		document.add(table);
	}

	/**
	 * 添加数据行
	 *
	 * @param table  PdfPTable
	 * @param stalls 数据集合
	 * @param font   字体
	 */
	public static void addDataRowsTransport(PdfPTable table, List<PrintTransferOrderTransportVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, String.valueOf(j + 1), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getSupplierName()) ? " " : String.valueOf(stalls.get(j).getSupplierName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getAddress()) ? " " : String.valueOf(stalls.get(j).getAddress()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductType()) ? " " : String.valueOf(stalls.get(j).getProductType()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getBaseUnit()) ? " " : String.valueOf(stalls.get(j).getBaseUnit()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getMeasureMode()) ? " " : String.valueOf(stalls.get(j).getMeasureMode()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getTotal()) ? " " : String.valueOf(stalls.get(j).getTotal()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPullQuantity()) ? " " : String.valueOf(stalls.get(j).getPullQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getPullWeight()) ? " " : String.valueOf(stalls.get(j).getPullWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getStore()) ? " " : String.valueOf(stalls.get(j).getStore()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getRemark()) ? " " : String.valueOf(stalls.get(j).getRemark()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}

	/**
	 * 添加数据行
	 *
	 * @param table  PdfPTable
	 * @param stalls 数据集合
	 * @param font   字体
	 */
	public static void addDataRowsProduct(PdfPTable table, List<PrintTransferOrderProductVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, String.valueOf(j + 1), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductType()) ? " " : String.valueOf(stalls.get(j).getProductType()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getBaseUnit()) ? " " : String.valueOf(stalls.get(j).getBaseUnit()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getMeasureMode()) ? " " : String.valueOf(stalls.get(j).getMeasureMode()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getStall()) ? " " : String.valueOf(stalls.get(j).getStall()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getQuantity()) ? " " : String.valueOf(stalls.get(j).getQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundQuantity()) ? " " : String.valueOf(stalls.get(j).getOutboundQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundWeight()) ? " " : String.valueOf(stalls.get(j).getOutboundWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getStoreDifference()) ? " " : String.valueOf(stalls.get(j).getStoreDifference()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getRemark()) ? " " : String.valueOf(stalls.get(j).getRemark()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}

	/**
	 * 添加数据行
	 *
	 * @param table  PdfPTable
	 * @param stalls 数据集合
	 * @param font   字体
	 */
	public static void addDataRowsWarehouse(PdfPTable table, List<PrintTransferOrderWarehouseVO> stalls, Font font) {
		if (Func.isNotEmpty(stalls) && stalls.size() > 0) {
			for (int j = 0; j < stalls.size(); j++) {
				addCenteredCell(table, String.valueOf(j + 1), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductName()) ? " " : String.valueOf(stalls.get(j).getProductName()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getProductType()) ? " " : String.valueOf(stalls.get(j).getProductType()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getBaseUnit()) ? " " : String.valueOf(stalls.get(j).getBaseUnit()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getMeasureMode()) ? " " : String.valueOf(stalls.get(j).getMeasureMode()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getQuantity()) ? " " : String.valueOf(stalls.get(j).getQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundQuantity()) ? " " : String.valueOf(stalls.get(j).getOutboundQuantity()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getOutboundWeight()) ? " " : String.valueOf(stalls.get(j).getOutboundWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getStoreWeight()) ? " " : String.valueOf(stalls.get(j).getStoreWeight()), font);
				addCenteredCell(table, Func.isEmpty(stalls.get(j).getRemark()) ? " " : String.valueOf(stalls.get(j).getRemark()), font);
			}
		} else {
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
			addCenteredCell(table, " ", font);
		}
	}


	@Data
	static class Stall {
		private String name;
		private String productName;
		private Integer totalOrder;
		private Integer requestQuantity;    // 要货数量
		private Integer inboundQuantity;   // 入库数量
		private Integer outboundQuantity;  // 出库数量

		public Stall(String name, String productName, Integer totalOrder,
					 Integer requestQuantity, Integer inboundQuantity, Integer outboundQuantity) {
			this.name = name;
			this.productName = productName;
			this.totalOrder = totalOrder;
			this.requestQuantity = requestQuantity;
			this.inboundQuantity = inboundQuantity;
			this.outboundQuantity = outboundQuantity;
		}
	}
}
