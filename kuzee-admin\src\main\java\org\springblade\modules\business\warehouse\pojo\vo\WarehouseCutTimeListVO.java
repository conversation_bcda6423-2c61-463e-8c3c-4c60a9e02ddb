package org.springblade.modules.business.warehouse.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class WarehouseCutTimeListVO {
	@Schema(description = "时间")
	private String cutTime;
	@Schema(description = "仓库名称")
	private String warehouseName;
	@Schema(description = "仓库id")
	private Long warehouseId;
	@Schema(description = "仓库cut时间分类list")
	private List<WarehouseCutTimeListVO> list;
}
