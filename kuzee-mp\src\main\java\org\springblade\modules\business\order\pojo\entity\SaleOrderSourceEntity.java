/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 销售订单原始数据表-支付成功时候的数据 实体类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("chy_sale_order_source")
@Schema(description = "SaleOrderSourceEntity对象")
@EqualsAndHashCode(callSuper = true)
public class SaleOrderSourceEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	@Schema(description = "客户id")
	private Long custId;
	/**
	 * 仓库id
	 */
	@Schema(description = "仓库id")
	private Long warehouseId;
	/**
	 * 订单编号
	 */
	@Schema(description = "订单编号")
	private String orderNo;
	/**
	 * 订单提交时间
	 */
	@Schema(description = "订单提交时间")
	private LocalDateTime orderTime;
	/**
	 * 订单应付金额（包含订单总金额、配套、送货费、促销等）
	 */
	@Schema(description = "订单应付金额（包含订单总金额、配套、送货费、促销等）")
	private Integer totalAmount;
	/**
	 * 送货费用
	 */
	@Schema(description = "送货费用")
	private Integer deliveryExpense;
	/**
	 * 订单实际支付金额
	 */
	@Schema(description = "订单实际支付金额")
	private Integer payAmount;
	/**
	 * 授信额度支付金额
	 */
	@Schema(description = "授信额度支付金额")
	private Integer creditaPayAmount;
	/**
	 * 钱包余额支付金额
	 */
	@Schema(description = "钱包余额支付金额")
	private Integer walletPayAmount;
	/**
	 * 三方渠道支付金额
	 */
	@Schema(description = "三方渠道支付金额")
	private Integer channelPayAmount;
	/**
	 * 钱包余额补差
	 */
	@Schema(description = "钱包余额补差")
	private Integer walletAmountDiff;
	/**
	 * 支付方式：0->未支付；1->平台额度；2->钱包；3-混合支付；4-第三方支付
	 */
	@Schema(description = "支付方式：0->未支付；1->平台额度；2->钱包；3-混合支付；4-第三方支付")
	private Integer payType;
	/**
	 * 订单来源：0->PC订单；1->app订单
	 */
	@Schema(description = "订单来源：0->PC订单；1->app订单")
	private Integer sourceType;
	/**
	 * 订单类型：0->正常订单；1->秒杀订单；2->临时订单
	 */
	@Schema(description = "订单类型：0->正常订单；1->秒杀订单；2->临时订单")
	private Integer orderType;
	/**
	 * 活动场次ID
	 */
	@Schema(description = "活动场次ID")
	private Long sessionsId;
	/**
	 * 配送方式  1 配送 2 自提
	 */
	@Schema(description = "配送方式  1 配送 2 自提")
	private Integer deliveryType;
	/**
	 * 是否加单1 加单 0 正常单
	 */
	@Schema(description = "是否加单1 加单 0 正常单")
	private Integer addOrder;
	/**
	 * 收货人名称
	 */
	@Schema(description = "收货人名称")
	private String receiverName;
	/**
	 * 收货人手机号
	 */
	@Schema(description = "收货人手机号")
	private String receiverPhone;
	/**
	 * 邮政编码
	 */
	@Schema(description = "邮政编码")
	private String receiverPostCode;
	/**
	 * 省份/直辖市
	 */
	@Schema(description = "省份/直辖市")
	private String receiverProvince;
	/**
	 * 城市
	 */
	@Schema(description = "城市")
	private String receiverCity;
	/**
	 * 区
	 */
	@Schema(description = "区")
	private String receiverRegion;
	/**
	 * 详细地址(街道)
	 */
	@Schema(description = "详细地址(街道)")
	private String receiverDetailAddress;
	/**
	 * 订单备注
	 */
	@Schema(description = "订单备注")
	private String note;
	/**
	 * 确认收货状态
	 */
	@Schema(description = "确认收货状态")
	private Integer confirmStatus;
	/**
	 * 是否加框
	 */
	@Schema(description = "是否加框")
	private Integer isAddBox;
	/**
	 * 订单提交时间戳
	 */
	@Schema(description = "订单提交时间戳")
	private Long createTimeAt;
	/**
	 * 支付状态：0-待支付 1-支付中 2-支付成功 3-支付失败
	 */
	@Schema(description = "支付状态：0-待支付 1-支付中 2-支付成功 3-支付失败")
	private Integer payStatus;
	/**
	 * 0-未截单 1已截单
	 */
	@Schema(description = "0-未截单 1已截单")
	private Integer isCutOrder;
	/**
	 * 采购批次号
	 */
	@Schema(description = "采购批次号")
	private String batchNo;
	/**
	 * 退款原因/取消原因
	 */
	@Schema(description = "退款原因/取消原因")
	private String returnReason;
	/**
	 * 退款处理人员
	 */
	@Schema(description = "退款处理人员")
	private String returnHandleMan;
	/**
	 * 退款时间
	 */
	@Schema(description = "退款时间")
	private LocalDateTime returnTime;
	/**
	 * 取消订单时间
	 */
	@Schema(description = "取消订单时间")
	private LocalDateTime cancelTime;
	/**
	 * 取消订单人
	 */
	@Schema(description = "取消订单人")
	private Long cancelUserId;
	/**
	 * 支付时间
	 */
	@Schema(description = "支付时间")
	private LocalDateTime payTime;
	/**
	 * 收货时间
	 */
	@Schema(description = "收货时间")
	private LocalDateTime receiverTime;
	/**
	 * 仓库配置配送基础费用
	 */
	@Schema(description = "仓库配置配送基础费用")
	private Integer deliveryFee;
	/**
	 * 仓库配置服务费费用
	 */
	@Schema(description = "仓库配置服务费费用")
	private Integer serviceFee;
	/**
	 * 提货上传文件
	 */
	@Schema(description = "提货上传文件")
	private String takeDeliveryPath;
	/**
	 * 授信额度是否还款
	 */
	@Schema(description = "授信额度是否还款")
	private Integer isCreditaRepayment;
	/**
	 * 对接第三方支付编号
	 */
	@Schema(description = "对接第三方支付编号")
	private String payOrderNo;
	/**
	 * 授信额度还款时间
	 */
	@Schema(description = "授信额度还款时间")
	private LocalDateTime creditaRepayTime;
	/**
	 * 支付时间戳
	 */
	@Schema(description = "支付时间戳")
	private Long payTimeAt;
	/**
	 * 下单客户ID
	 */
	@Schema(description = "下单客户ID")
	private Long orderCustId;
	/**
	 * 确认收货方式：1. 手动确认收货；2. 自动确认收货。结算时推送才有该字段
	 */
	@Schema(description = "确认收货方式：1. 手动确认收货；2. 自动确认收货。结算时推送才有该字段")
	private Integer receiveMethod;

}
