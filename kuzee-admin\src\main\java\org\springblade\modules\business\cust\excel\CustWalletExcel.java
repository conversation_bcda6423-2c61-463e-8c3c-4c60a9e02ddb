/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.excel;


import lombok.Data;

import java.io.Serializable;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serial;


/**
 * 客户钱包主表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustWalletExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("用户id")
	private Long custId;
	/**
	 * 账户余额
	 */
	@ColumnWidth(20)
	@ExcelProperty("账户余额")
	private Integer balance;
	/**
	 * 冻结余额
	 */
	@ColumnWidth(20)
	@ExcelProperty("冻结余额")
	private Integer freezeBalance;
	/**
	 * 累计支出
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计支出")
	private Integer totalExpense;
	/**
	 * 累计充值
	 */
	@ColumnWidth(20)
	@ExcelProperty("累计充值")
	private Integer totalRecharge;

}
