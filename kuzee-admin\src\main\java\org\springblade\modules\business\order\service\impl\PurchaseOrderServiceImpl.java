/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.DictCache;
import org.springblade.common.enums.AllocationStatusEnum;
import org.springblade.common.enums.DictEnum;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.message.enums.MessageJumpEnum;
import org.springblade.message.enums.MessageTypeEnum;
import org.springblade.modules.business.cust.service.impl.MessageSenderService;
import org.springblade.modules.business.order.excel.PurchaseOrderExcel;
import org.springblade.modules.business.order.mapper.PurchaseOrderMapper;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.PurchaseOrderEntity;
import org.springblade.modules.business.order.pojo.entity.PurchaseOrderItemEntity;
import org.springblade.modules.business.order.pojo.entity.SaleOrderEntity;
import org.springblade.modules.business.order.pojo.vo.PurchaseOrderPageListVO;
import org.springblade.modules.business.order.pojo.vo.PurchaseOrderVO;
import org.springblade.modules.business.order.pojo.vo.SaleOrderItemJobVO;
import org.springblade.modules.business.order.pojo.vo.WarehousePurchaseListVO;
import org.springblade.modules.business.order.pojo.vo.CustomerUserIdVO;
import org.springblade.modules.business.order.pojo.vo.SkuCustomerUserMappingVO;
import org.springblade.modules.business.order.service.IPurchaseOrderItemService;
import org.springblade.modules.business.order.service.IPurchaseOrderService;
import org.springblade.modules.business.order.service.ISaleOrderService;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.product.service.ISkuWarehouseRelationService;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.ListRelationSkuVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseAdminIdVO;
import org.springblade.modules.business.warehouse.service.IWarehousePurchaseService;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springblade.modules.business.order.event.BusinessEvent;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * 采购单主表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class PurchaseOrderServiceImpl extends BaseServiceImpl<PurchaseOrderMapper, PurchaseOrderEntity> implements IPurchaseOrderService {
	private final ISaleOrderService saleOrderService;
	private final IPurchaseOrderItemService purchaseOrderItemService;

	private final IWarehouseService warehouseService;

	private final  ISkuWarehouseRelationService skuWarehouseRelationService;
	private final IWarehousePurchaseService warehousePurchaseService;
	private final MessageSenderService messageSenderService;
	private final ISkuStockService skuStockService;
	private final ApplicationEventPublisher eventPublisher;
	@Override
	public IPage<PurchaseOrderVO> selectPurchaseOrderPage(IPage<PurchaseOrderVO> page, PurchaseOrderVO purchaseOrder) {
		return page.setRecords(baseMapper.selectPurchaseOrderPage(page, purchaseOrder));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean adjustBatch(List<Long> orderIds, String batchNo) {
		if (Func.isEmpty(orderIds)) {
			log.warn("未提供任何订单ID！！");
			throw new ServiceException("未提供任何订单ID！！");
		}
		// 批量查询销售订单
		List<SaleOrderEntity> saleOrders = saleOrderService.listByIds(orderIds);
		//把传入的批次号批量加到订单表上面去
		saleOrders.forEach(item -> item.setBatchNo(GenerateNumberUtil.PurchaseCodeF()));
		return saleOrderService.updateBatchById(saleOrders);
//		List<PurchaseOrderItemEntity> updatePurchaseOrderItemList = new ArrayList<>();
//		List<PurchaseOrderSaleItemEntity> insetPurchaseOrderSaleItemList = new ArrayList<>();
//		Map<Long, SaleOrderEntity> saleOrderMap = saleOrders.stream().collect(Collectors.toMap(SaleOrderEntity::getId, saleOrder -> saleOrder));
//
//		// 批量查询销售订单商品
//		List<SaleOrderItemEntity> saleOrderItems = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda()
//			.in(SaleOrderItemEntity::getOrderId, orderIds)
//			.ne(SaleOrderItemEntity::getStatus, OrderEnum.CANCELLED.getCode()));
//		for (Long orderId : orderIds) {
//			SaleOrderEntity saleOrder = saleOrderMap.get(orderId);
//			if (saleOrder == null) {
//				log.warn("未查询到订单ID={} 的销售订单！！", orderId);
//				throw new ServiceException("未查询到订单ID=" + orderId + " 的销售订单！！");
//			}
//			// 判断此订单是否已截单或者已经有采购批次则无法调整
//			if (Objects.equals(saleOrder.getIsCutOrder(), BusinessConstant.IS_CUT_ORDER_YES) || Func.isNotEmpty(saleOrder.getBatchNo())) {
//				log.warn("订单ID={} 已截单或已经有采购批次！！", orderId);
//				throw new ServiceException("订单ID=" + orderId + " 已截单或已经有采购批次！！");
//			}
//			List<SaleOrderItemEntity> orderItems = saleOrderItemMap.getOrDefault(orderId, new ArrayList<>());
//			if (orderItems.isEmpty()) {
//				log.warn("未查询到订单ID={} 的销售订单商品！！", orderId);
//				throw new ServiceException("未查询到订单ID=" + orderId + " 的销售订单商品！！");
//			}
//
//
//
//			// 查询采购订单(根据仓库id和批次号)
//			List<PurchaseOrderEntity> purchaseOrders = this.list(
//				Wrappers.<PurchaseOrderEntity>query().lambda()
//					.eq(PurchaseOrderEntity::getStatus, BusinessConstant.ENABLE_STATUS)
//					.eq(PurchaseOrderEntity::getWarehouseId, saleOrder.getWarehouseId())
//					.eq(PurchaseOrderEntity::getBatchNo, batchNo)
//			);
//			if (purchaseOrders.isEmpty()) {
//				log.warn("未查询到订单ID={} 对应批次的采购订单！！", orderId);
//				throw new ServiceException("未查询到订单ID=" + orderId + " 对应批次采购订单！！");
//			}
//			// 查询采购订单商品
//			List<PurchaseOrderItemEntity> purchaseOrderAllItems = purchaseOrderItemService.list(
//				Wrappers.<PurchaseOrderItemEntity>query().lambda()
//					.in(PurchaseOrderItemEntity::getPurchaseId, purchaseOrders.stream().map(PurchaseOrderEntity::getId).collect(Collectors.toList()))
//			);
//
//			purchaseOrders.forEach(purchaseOrder -> {
//				List<PurchaseOrderItemEntity> purchaseOrderItems = purchaseOrderAllItems.stream()
//					.filter(purchaseOrderItem -> purchaseOrder.getId().equals(purchaseOrderItem.getPurchaseId()))
//					.collect(Collectors.toList());
//				// 创建一个 Map 来存储 purchaseOrderItems，使用 productSkuId 作为键
//				Map<Long, PurchaseOrderItemEntity> purchaseOrderItemMap = purchaseOrderItems.stream()
//					.collect(Collectors.toMap(PurchaseOrderItemEntity::getProductSkuId, item -> item));
//				orderItems.forEach(saleOrderItem -> {
//					Long productSkuId = saleOrderItem.getProductSkuId();
//					if (purchaseOrderItemMap.containsKey(productSkuId)) {
//						// 存在相同的 productSkuId，更新数量
//						PurchaseOrderItemEntity purchaseOrderItem = purchaseOrderItemMap.get(productSkuId);
//						// 先判断单价是否一致
//						if (!Objects.equals(purchaseOrderItem.getUnitPrice(), saleOrderItem.getProductUnitPrice())) {
//							log.warn("订单ID={} 的商品单价不一致！！", orderId);
//							throw new ServiceException("订单ID=" + orderId + " 的商品单价不一致！！");
//						}
//						purchaseOrderItem.setQuantity(purchaseOrderItem.getQuantity() + saleOrderItem.getProductQuantity());
//						// 如果 weight 字段存在且需要更新，取消注释并设置正确的值
//						// purchaseOrderItem.setWeight(purchaseOrderItem.getWeight() + saleOrderItem.getProductQuantity());
//						purchaseOrderItem.setSubtotal(purchaseOrderItem.getSubtotal() + saleOrderItem.getProductPrice());
//						updatePurchaseOrderItemList.add(purchaseOrderItem);
//
//						// 添加采购详情关联订单详情表
//						PurchaseOrderSaleItemEntity purchaseOrderSaleItem = new PurchaseOrderSaleItemEntity();
//						purchaseOrderSaleItem.setPurchaseItemId(purchaseOrderItem.getId());
//						purchaseOrderSaleItem.setProductSkuId(purchaseOrderItem.getProductSkuId());
//						purchaseOrderSaleItem.setOrderItemId(saleOrderItem.getId());
//						insetPurchaseOrderSaleItemList.add(purchaseOrderSaleItem);
//
//					} else {
//						// 不存在相同的 productSkuId，添加新的记录,调接口
//						WarehouseEntity warehouseEntity = warehouseService.getById(saleOrder.getWarehouseId());
//						if (warehouseEntity == null) {
//							log.warn("未查询到仓库ID={} 的仓库信息！！", saleOrder.getWarehouseId());
//							throw new ServiceException("未查询到仓库信息！！");
//						}
//						WarehouseJobVO warehouse = BeanUtil.copyProperties(warehouseEntity, WarehouseJobVO.class);
//
//						// 获取采购员
//						List<WarehousePurchaseEntity> warehousePurchases = warehousePurchaseService.getPurchaseListByWarehouseId(saleOrder.getWarehouseId());
//						if (warehousePurchases.isEmpty()) {
//							log.warn("未查询到仓库ID={} 的采购员信息！！", saleOrder.getWarehouseId());
//							throw new ServiceException("未查询到采购员信息！！");
//						}
//						List<Long> purchaseIdList = warehousePurchases.stream().map(WarehousePurchaseEntity::getPurchaserId).collect(Collectors.toList());
//
//						// 获取运输单位
//						TransportUnitEntity transportUnitEntity = transportUnitService.getOne(Wrappers.<TransportUnitEntity>query().lambda().eq(TransportUnitEntity::getId, saleOrderItem.getSupportTransUnitId()));
//						if (transportUnitEntity == null) {
//							log.warn("未查询到运输单位ID={} 的运输单位信息！！", saleOrderItem.getSupportTransUnitId());
//							throw new ServiceException("未查询到运输单位信息！！");
//						}
//						List<TransportUnitEntity> transportUnitEntities = List.of(transportUnitEntity);
//
//						// 获取销售订单商品列表
//						List<SaleOrderItemJobVO> cutOrderList = saleOrderItemService.getCutOrderListByOrderId(saleOrder.getWarehouseId(), saleOrder.getId(), saleOrderItem.getProductSkuId());
//						if (cutOrderList.isEmpty()) {
//							log.warn("未查询到订单ID={} 的销售订单商品列表！！", saleOrder.getId());
//							throw new ServiceException("未查询到销售订单商品列表！！");
//						}
//						// TODO 等待朱康接口完善，现在无法调用导致循环依赖
//						CutOrderJob cutOrderJob=new CutOrderJob(saleOrderService,saleOrderItemService, warehouseService,
//							warehousePurchaseService, this, purchaseOrderItemService, inventoryService,
//							skuOemService,skuPpService,transportOrderService,transportOrderItemService,iSkuWarehouseRelationService
//							,purchaseOrderSaleItemService,transportUnitService);
//						 cutOrderJob.GeneratePurchaseOrder(cutOrderList, warehouse, purchaseIdList, transportUnitEntities, BusinessConstant.ORDER_TYPE_SALE);
//					}
//				});
//			});
//
//			// 更新订单信息
//			saleOrder.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_YES);
//			saleOrder.setBatchNo(batchNo);
//			saleOrderService.saveOrUpdate(saleOrder);
//		}

		// 保存和更新记录
//		if (!updatePurchaseOrderItemList.isEmpty()) {
//			purchaseOrderItemService.updateBatchById(updatePurchaseOrderItemList);
//		}
//		if (!insetPurchaseOrderSaleItemList.isEmpty()) {
//			purchaseOrderSaleItemService.saveBatch(insetPurchaseOrderSaleItemList);
//		}
//
//		return true;
	}




	@Override
	public List<PurchaseOrderExcel> exportPurchaseOrder(Wrapper<PurchaseOrderEntity> queryWrapper) {
		List<PurchaseOrderExcel> purchaseOrderList = baseMapper.exportPurchaseOrder(queryWrapper);
		//purchaseOrderList.forEach(purchaseOrder -> {
		//	purchaseOrder.setTypeName(DictCache.getValue(DictEnum.YES_NO, PurchaseOrderEntity.getType()));
		//});
		return purchaseOrderList;
	}

	@Override
	public IPage<PurchaseOrderPageListVO> pageList(PurchaseOrderPageListDTO dto, Query query) {
//		IPage<PurchaseOrderPageListVO> page = Condition.getPage(query);
//		IPage<PurchaseOrderPageListVO> list = baseMapper.pageList(page, dto, 0);
//		List<String> wIdStr=list.getRecords().stream() .map(PurchaseOrderPageListVO::getOrderWarehouseId).collect(Collectors.toList());
//		List<Long> ids=new ArrayList<>();
//		for (String str:wIdStr) {
//			List<Long> longList = Arrays.stream(str.split(","))
//				.filter(s -> Func.isNotEmpty(s)) // 过滤掉空字符串
//				.map(Long::valueOf)
//				.collect(Collectors.toList());
//			if (longList.size()>0){
//				ids.addAll( longList);
//			}
//		}
//		QueryWrapper<WarehouseEntity> queryWrapper = new QueryWrapper<>();
//		queryWrapper.in("id",ids);
//		List<WarehouseEntity> list1 = warehouseService.list(queryWrapper);
//		for (PurchaseOrderPageListVO vo : list.getRecords()) {
//			List<Long> longList = Arrays.stream(vo.getOrderWarehouseId().split(","))
//				.filter(s -> Func.isNotEmpty(s)) // 过滤掉空字符串
//				.map(Long::valueOf)
//				.collect(Collectors.toList());
//			if ( longList.size()>1){
//				QueryWrapper <WarehouseEntity> queryWrapper2 = new QueryWrapper<>();
//				queryWrapper2.in("id",longList);
//			    List<WarehouseEntity> sd=	list1.stream().filter(queryWrapper2).toList();
//				 vo.setWarehouseName(sd.stream().map(WarehouseEntity::getWarehouseName).collect(Collectors.joining(",")));
//			}else{
//				WarehouseEntity warehouse = list1.stream().filter(w -> w.getId().equals(longList.get(0))).findFirst().orElse(null);
//				if ( warehouse != null)
//					vo.setWarehouseName(warehouse.getWarehouseName());
//			}
//			vo.setAllocationStatusName(DictCache.getValue(DictEnum.ALLOCATION_STATUS, vo.getAllocationStatus()));
//		}
//		return list;
		IPage<PurchaseOrderPageListVO> page = Condition.getPage(query);
		IPage<PurchaseOrderPageListVO> list = baseMapper.pageList(page, dto, 0);

		// 收集所有 orderWarehouseId 并转换为 Long 集合
		List<Long> wIdStrs = list.getRecords().stream()
			.map(PurchaseOrderPageListVO::getOrderWarehouseId)
			.filter(Func::isNotEmpty)
			.toList();
		List<Long> warehouseIds =wIdStrs;
//		Set<Long> warehouseIds = new HashSet<>();
//		for (Long str : wIdStrs) {
//			Arrays.fi
//				.filter(s -> Func.isNotEmpty(s))
//				.map(Long::valueOf)
//				.forEach(warehouseIds::add);
//		}

		if (!warehouseIds.isEmpty()) {
			// 一次性查询所有仓库信息
			List<WarehouseEntity> warehouses = warehouseService.listByIds(warehouseIds);
			Map<Long, String> warehouseMap = warehouses.stream()
				.collect(Collectors.toMap(WarehouseEntity::getId, WarehouseEntity::getWarehouseName));

			// 设置仓库名称
			for (PurchaseOrderPageListVO vo : list.getRecords()) {
				Long orderWarehouseId = vo.getOrderWarehouseId();
				if (Func.isEmpty(orderWarehouseId)) continue;
				String s = warehouseMap.get(orderWarehouseId);
				vo.setWarehouseName(s);
//				List<Long> idsStr = Arrays.stream(orderWarehouseId.split(","))
//					.filter(Func::isNotEmpty)
//					.toList();
//
//				List<String> warehouseNames = idsStr.stream()
//					.map(Long::valueOf)
//					.map(warehouseMap::get)
//					.filter(Objects::nonNull)
//					.toList();
//
//				if (warehouseNames.size() > 1) {
//					vo.setWarehouseName(String.join(",", warehouseNames));
//				} else if (!warehouseNames.isEmpty()) {
//					vo.setWarehouseName(warehouseNames.get(0));
//				}
				vo.setAllocationStatusName(DictCache.getValue(DictEnum.ALLOCATION_STATUS, vo.getAllocationStatus()));
			}
		}

		return list;
	}

	@Override
	public List<WarehousePurchaseListVO> getWarehousePurchaseList(WarehousePurchaseListDTO dto, Query query) {
		//IPage<WarehousePurchaseListVO> page = Condition.getPage(query);
		//IPage<WarehousePurchaseListVO> pageList = baseMapper.getWarehousePurchaseList(page, dto);

		List<ListRelationSkuVO> listRelationSkuVOS = skuWarehouseRelationService.listRelationSku(null, AuthUtil.getUserId(), null);
		List<WarehousePurchaseListVO> list = new ArrayList<>();
		for (ListRelationSkuVO item:listRelationSkuVOS ) {
			WarehousePurchaseListVO vo=new WarehousePurchaseListVO();
			vo.setSkuId(item.getSkuId());
			vo.setProductName(item.getProductName());
			vo.setSpData(item.getSpData());
			list.add(vo);
		}

		return list;
	}

	@Override
	public List<SaleOrderItemJobVO> getSelfPurchaseOrder(List<AddSelfPurchaseOrderDTO> dto, Long warehouseId) {
		List<Long> skuIdList = dto.stream().map(AddSelfPurchaseOrderDTO::getSkuId).collect(Collectors.toList());
		List<SaleOrderItemJobVO> saleOrderItemJobList = baseMapper.getSelfPurchaseOrder(skuIdList, warehouseId);
		if (Func.isEmpty(saleOrderItemJobList)) {
			//log.warn("未查询到符合条件的销售订单商品！{}",skuIdList);
			throw new ServiceException("未查询到符合条件的销售订单商品！");
		}

		for (SaleOrderItemJobVO item : saleOrderItemJobList) {
			item.setWarehouseId(warehouseId);
			AddSelfPurchaseOrderDTO orderDTO = dto.stream().filter(addSelfPurchaseOrderDTO -> addSelfPurchaseOrderDTO.getSkuId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (Func.isNotEmpty(orderDTO)) {
				item.setProductQuantity(orderDTO.getQuantity());
				if (Func.isEmpty(item.getPrice())){
					//todo 获取商品单价为空
				}
				if  (Func.isNotEmpty(item.getPrice())) {
					item.setProductUnitPrice(item.getPrice());
					item.setProductPrice(item.getPrice() * orderDTO.getQuantity());
				}
			}
		}
		return saleOrderItemJobList;
	}

	@Override
	public IPage<PurchaseOrderPageListVO> SaleOrderPageList(PurchaseOrderPageListDTO dto, Query query) {
		IPage<PurchaseOrderPageListVO> page = Condition.getPage(query);
		IPage<PurchaseOrderPageListVO> list = baseMapper.pageList(page, dto, 1);
		for (PurchaseOrderPageListVO vo : list.getRecords()) {
			vo.setAllocationStatusName(DictCache.getValue(DictEnum.ALLOCATION_STATUS, vo.getAllocationStatus()));
		}
		return list;
	}

	@Override
	public List<PurchaseOrderItemEntity> getPurchaseWarehouedList(Long warehouseId) {
		return baseMapper.getPurchaseWarehouedList(warehouseId);
	}


	@Override
	public <T extends ReplenishmentOrderItemBaseDTO> void updateStatusAndQuantity(Long purchaseOrderId, List<T> itemList, boolean isMissing) {
		Map<Long, T> itemMap = itemList.stream().collect(Collectors.toMap(ReplenishmentOrderItemBaseDTO::getPurchaseOrderItemId, item -> item));
		// 查询采购单商品明细
		List<PurchaseOrderItemEntity> orderItemList = purchaseOrderItemService.list(Wrappers.<PurchaseOrderItemEntity>query().lambda()
			.select(PurchaseOrderItemEntity::getId, PurchaseOrderItemEntity::getRemainingQuantity,
				PurchaseOrderItemEntity::getMissingQuantity, PurchaseOrderItemEntity::getSupportTransAllocationNum,
				PurchaseOrderItemEntity::getProductSkuId)
			.eq(PurchaseOrderItemEntity::getPurchaseId, purchaseOrderId));

		Assert.notEmpty(orderItemList, () -> new ServiceException("关联采购单商品明细不存在"));

		// 需要更新剩余采购数量的集合
		List<PurchaseOrderItemEntity> updateList = new ArrayList<>();
		for (PurchaseOrderItemEntity item : orderItemList) {
			T t = itemMap.get(item.getId());
			if (Func.isNull(t)) {
				continue;
			}
			if (t.getQuantity() > item.getRemainingQuantity()) {
				throw new ServiceException("采购数量大于剩余采购数量！");
			}
			item.setRemainingQuantity(item.getRemainingQuantity() - t.getQuantity());
			if (Func.notNull(t.getSupportTransNum()) && !isMissing) {
				item.setSupportTransAllocationNum((Func.notNull(item.getSupportTransAllocationNum()) ? item.getSupportTransAllocationNum() : 0) + t.getSupportTransNum());
			}
			if (isMissing) {
				item.setMissingQuantity((Func.notNull(item.getMissingQuantity()) ? item.getMissingQuantity() : 0) + t.getQuantity());
			}
			updateList.add(item);
		}

		Assert.notEmpty(updateList, () -> new ServiceException("关联采购单商品明细不存在"));

		// 更新采购单商品明细剩余采购数量
		purchaseOrderItemService.updateBatchById(updateList);
		// 更新采购单状态
		PurchaseOrderEntity purchaseOrder = new PurchaseOrderEntity();
		purchaseOrder.setId(purchaseOrderId);
		if (orderItemList.stream().allMatch(item -> item.getRemainingQuantity() == 0)) {
			purchaseOrder.setAllocationStatus(AllocationStatusEnum.END.getCode());
		} else {
			purchaseOrder.setAllocationStatus(AllocationStatusEnum.PENDING.getCode());
		}
		updateById(purchaseOrder);
		//推送报缺微信消息通知
		if(isMissing){
			log.info("发布商品报缺事件，涉及 {} 条采购明细。", updateList.size());
			eventPublisher.publishEvent(new BusinessEvent<>(this, updateList, "PURCHASE_MISSING_ITEMS"));
		}

	}
	//推送报缺微信消息通知方法
	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, condition = "#event.type == 'PURCHASE_MISSING_ITEMS'")
	public void handleMissingItemsEvent(BusinessEvent<List<PurchaseOrderItemEntity>> event) {
		List<PurchaseOrderItemEntity> itemList = event.getData();
		try {
			log.info("开始处理商品报缺推送消息（事务提交后），共涉及 {} 条采购明细。", itemList.size());
			if (Func.isEmpty(itemList)) {
				return;
			}

			// --- 1. 批量数据获取 ---
			List<Long> purchaseItemIds = itemList.stream().map(PurchaseOrderItemEntity::getId).collect(Collectors.toList());
			List<Long> skuIds = itemList.stream().map(PurchaseOrderItemEntity::getProductSkuId).distinct().collect(Collectors.toList());
			log.info("报缺涉及 {} 个独立SKU，ID: {}", skuIds.size(), skuIds);

			// 一次性获取所有相关SKU的详细信息
			Map<Long, SkuStockEntity> skuStockMap = skuStockService.getIds(skuIds).stream()
				.collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));

			// 一次性获取所有相关的管理员ID（总仓+分仓），并去重
			Set<Long> allAdminIds = new HashSet<>();
			warehousePurchaseService.getWarehouseAdminList().stream()
				.map(WarehouseAdminIdVO::getUserId)
				.forEach(allAdminIds::add);
			warehousePurchaseService.getAdminsBySkuIds(skuIds).stream()
				.map(WarehouseAdminIdVO::getUserId)
				.forEach(allAdminIds::add);
			log.info("查询到 {} 位相关仓库管理员，用户ID: {}", allAdminIds.size(), allAdminIds);

			// 一次性获取所有SKU到客户的映射关系
			Map<Long, List<Long>> skuToCustomerIdsMap = baseMapper.getSkuCustomerUserMapping(purchaseItemIds).stream()
				.collect(Collectors.groupingBy(
					SkuCustomerUserMappingVO::getProductSkuId,
					Collectors.mapping(SkuCustomerUserMappingVO::getCustId, Collectors.toList())
				));
			log.info("查询到报缺商品与客户的映射关系，共涉及 {} 个SKU的客户。", skuToCustomerIdsMap.size());

			String dateStr = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

			// --- 2. 批量发送通知 ---

			// 批量通知管理员
			if (!allAdminIds.isEmpty()) {
				log.info("准备向管理员批量推送报缺通知...");
				// 为每个缺货的SKU，向所有相关管理员发送一条通知
				skuIds.forEach(skuId -> {
					SkuStockEntity stockEntity = skuStockMap.get(skuId);
					if (stockEntity != null) {
						String adminMessage = String.format("%s %s 报缺", dateStr, stockEntity.getProductName());
						log.debug("向管理员 {} 推送消息: {}", allAdminIds, adminMessage);
						messageSenderService.batchPushSupplyMessage(
							MessageTypeEnum.MISSING,
							new ArrayList<>(allAdminIds),
							stockEntity.getProductName(),
							"缺货中",
							dateStr
						);
					}
				});
			} else {
				log.warn("未找到任何相关的仓库管理员，将跳过管理员通知。");
			}

			// 批量通知客户
			log.info("准备向客户批量推送报缺通知...");
			skuToCustomerIdsMap.forEach((skuId, customerUserIds) -> {
				SkuStockEntity stockEntity = skuStockMap.get(skuId);
				if (stockEntity != null) {
					List<Long> distinctUserIds = customerUserIds.stream().distinct().collect(Collectors.toList());
					if (!distinctUserIds.isEmpty()) {
						String customerMessage = String.format("您 %s 预定的 %s 出现货源紧张情况，有可能会影响您的订单。请知悉！", dateStr, stockEntity.getProductName());
						log.debug("SKU ID: {} 报缺，准备通知客户: {}。消息内容: {}", skuId, distinctUserIds, customerMessage);
						messageSenderService.batchPushCustMessage(
							MessageTypeEnum.MISSING_CUST,
							distinctUserIds,
							stockEntity.getProductName(),
							dateStr,
							"商品货源紧张，可能影响您的订单，请知悉"
						);
					}
				}
			});
			log.info("商品报缺推送消息处理完成。");
		} catch (Exception e) {
			log.error("处理商品报缺推送消息时发生异常（该异常不会影响主事务）", e);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void missing(PurchaseOrderMissingItemDTO dto) {
		this.updateStatusAndQuantity(dto.getPurchaseOrderId(), dto.getItemList(), true);
	}

	@Override
	public List<CustomerUserIdVO> getCustomerUserIdsByPurchaseItemIds(List<Long> purchaseItemIds) {
		if (Func.isEmpty(purchaseItemIds)) {
			return new ArrayList<>();
		}
		return baseMapper.getCustomerUserIdsByPurchaseItemIds(purchaseItemIds);
	}
}
