/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户登录日志表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_cust_login_log")
@Schema(description = "CustLoginLogEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustLoginLogEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private Long custId;
	/**
	 * 角色编码(用于小程序端判断权限)
	 */
	@Schema(description = "角色编码(用于小程序端判断权限)")
	private String ip;
	/**
	 * 登录类型：0->PC；1->android;2->ios;3->小程序
	 */
	@Schema(description = "登录类型：0->PC；1->android;2->ios;3->小程序")
	private Byte loginType;

}
