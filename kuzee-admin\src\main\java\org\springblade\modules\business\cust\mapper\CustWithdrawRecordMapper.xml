<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustWithdrawRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custWithdrawRecordResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustWithdrawRecordEntity">
        <result column="cust_id" property="custId"/>
        <result column="amount" property="amount"/>
        <result column="bank_card_id" property="bankCardId"/>
        <result column="withdraw_time" property="withdrawTime"/>
        <result column="withdraw_status" property="withdrawStatus"/>
    </resultMap>

    <select id="selectCustWithdrawRecordPage" resultMap="custWithdrawRecordResultMap">
        select * from chy_cust_withdraw_record where is_deleted = 0
    </select>

    <select id="exportCustWithdrawRecord" resultType="org.springblade.modules.business.cust.excel.CustWithdrawRecordExcel">
        SELECT * FROM chy_cust_withdraw_record ${ew.customSqlSegment}
    </select>

</mapper>
