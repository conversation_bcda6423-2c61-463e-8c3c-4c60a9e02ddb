package org.springblade.modules.business.order.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Getter
@Setter
@Schema(description = "实时对账VO")
public class RealtimeReconciliationVO {

	@Schema(description = "时间")
	private LocalDate date;

	@Schema(description = "供应商ID")
	private Long supplierId;

	@Schema(description = "供应商名称")
	private String supplierName;

	@Schema(description = "供应商电话")
	private String supplierPhone;

	@Schema(description = "供货总额")
	private BigDecimal supplyAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "结算金额")
	private BigDecimal settlementAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "售后扣款")
	private BigDecimal afterDeduction = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "售后罚款")
	private BigDecimal afterFines = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "报损扣款")
	private BigDecimal reportLossReturn = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "转运费")
	private BigDecimal forwardingCharges = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	@Schema(description = "退货出库金额")
	private BigDecimal returnOutboundAmount = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

	// 确保所有金额都保持2位小数
	public void setSupplyAmount(BigDecimal supplyAmount) {
		this.supplyAmount = supplyAmount != null ? supplyAmount.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount != null ? settlementAmount.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setAfterDeduction(BigDecimal afterDeduction) {
		this.afterDeduction = afterDeduction != null ? afterDeduction.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setAfterFines(BigDecimal afterFines) {
		this.afterFines = afterFines != null ? afterFines.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setReportLossReturn(BigDecimal reportLossReturn) {
		this.reportLossReturn = reportLossReturn != null ? reportLossReturn.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setForwardingCharges(BigDecimal forwardingCharges) {
		this.forwardingCharges = forwardingCharges != null ? forwardingCharges.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

	public void setReturnOutboundAmount(BigDecimal returnOutboundAmount) {
		this.returnOutboundAmount = returnOutboundAmount != null ? returnOutboundAmount.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}
}
