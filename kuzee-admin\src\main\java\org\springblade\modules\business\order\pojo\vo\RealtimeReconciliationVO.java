package org.springblade.modules.business.order.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "实时对账VO")
public class RealtimeReconciliationVO {

	@Schema(description = "时间")
	private LocalDate date;

	@Schema(description = "供应商ID")
	private Long supplierId;

	@Schema(description = "供应商名称")
	private String supplierName;

	@Schema(description = "供应商电话")
	private String supplierPhone;

	@Schema(description = "供货总额")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal supplyAmount=BigDecimal.ZERO;

	@Schema(description = "结算金额")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal settlementAmount=BigDecimal.ZERO;

	@Schema(description = "售后扣款")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal afterDeduction=BigDecimal.ZERO;

	@Schema(description = "售后罚款")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal afterFines=BigDecimal.ZERO;

	@Schema(description = "报损扣款")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal reportLossReturn=BigDecimal.ZERO;

	@Schema(description = "转运费")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal forwardingCharges=BigDecimal.ZERO;

	@Schema(description = "退货出库金额")
	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private BigDecimal returnOutboundAmount=BigDecimal.ZERO;
}
