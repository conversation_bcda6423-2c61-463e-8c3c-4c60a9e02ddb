<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustEntity">
        <result column="parent_id" property="parentId"/>
        <result column="blade_user_id" property="bladeUserId"/>
        <result column="cust_name" property="custName"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="credit_limit" property="creditLimit"/>
        <result column="credit_end_time" property="creditEndTime"/>
        <result column="phone" property="phone"/>
        <result column="password" property="password"/>
        <result column="nickname" property="nickname"/>
        <result column="icon" property="icon"/>
        <result column="gender" property="gender"/>
        <result column="source_type" property="sourceType"/>
        <result column="cust_role_id" property="custRoleId"/>
        <result column="unionid" property="unionid"/>
    </resultMap>

    <select id="selectCustPage" resultMap="custResultMap">
        select * from chy_cust where is_deleted = 0
    </select>

    <select id="exportCust" resultType="org.springblade.modules.business.cust.excel.CustExcel">
        SELECT * FROM chy_cust ${ew.customSqlSegment}
    </select>

    <select id="getListPage" resultType="org.springblade.modules.business.cust.pojo.vo.CustListPageVO">
        SELECT * FROM chy_cust WHERE is_deleted = 0 and parent_id = 0 and cust_type = 0
        <if test="dto.custName!= null and dto.custName!= ''">
            AND (cust_name LIKE CONCAT('%', #{dto.custName}, '%') or phone LIKE CONCAT('%', #{dto.custName}, '%'))
        </if>
        <if test="dto.contacts!= null and dto.contacts!= ''">
            AND contacts LIKE CONCAT('%', #{dto.contacts}, '%')
        </if>
        <if test="dto.phone!= null and dto.phone!= ''">
            AND phone LIKE CONCAT('%', #{dto.phone}, '%')
        </if>
        <if test="dto.status!= null">
            AND status = #{dto.status}
        </if>
        <if test="dto.userId!= null and dto.userId!= ''">
            AND user_id = #{dto.userId}
        </if>
        <if test="dto.warehouseId!= null and dto.warehouseId!= ''">
            AND warehouse_id = #{dto.warehouseId}
        </if>
    </select>

    <select id="getCustByIds" resultType="org.springblade.modules.business.cust.pojo.entity.CustEntity">
        SELECT * FROM chy_cust WHERE is_deleted = 0 AND parent_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getCustInfo" resultType="org.springblade.modules.business.cust.pojo.entity.CustEntity">
        select * from chy_cust c
        left join chy_order_after_sales_service cr on c.id = cr.cust_id
        where cr.id = #{id}
    </select>

</mapper>
