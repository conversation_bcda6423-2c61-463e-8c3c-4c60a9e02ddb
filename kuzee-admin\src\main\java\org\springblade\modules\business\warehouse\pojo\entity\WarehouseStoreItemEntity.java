/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.handle.AmountTypeHandle;
import org.springblade.common.handle.WeightTypeHandle;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.Func;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 入库记录商品详情表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@TableName(value = "chy_warehouse_store_item", autoResultMap = true)
@Schema(description = "WarehouseStoreItemEntity对象")
@EqualsAndHashCode(callSuper = true)
public class WarehouseStoreItemEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 仓库id
	 */
	@Schema(description = "仓库id")
	private Long warehouseId;
	/**
	 * 仓库入库id
	 */
	@Schema(description = "仓库入库id")
	private Long warehouseStoreId;

	@Schema(description = "仓库入库单号")
	private String warehouseStoreNo;

	@Schema(description = "备货单明细id")
	private Long replenishmentItemId;

	@Schema(description = "关联入库单号(退货入库退到指定批次)")
	private String storeBatchNo;

	/**
	 * 商品ID
	 */
	@Schema(description = "商品ID")
	private Long productId;
	/**
	 * 商品SKU
	 */
	@Schema(description = "商品SKU")
	private Long productSkuId;

	@Schema(description = "供应商ID（关联供应商表）")
	private Long supplierId;
	/**
	 * sku编码
	 */
	@Schema(description = "sku编码")
	private String skuCode;
	/**
	 * 商品销售属性
	 */
	@Schema(description = "商品销售属性")
	private String spData;
	/**
	 * 预计到货量
	 */
	@Schema(description = "预计到货量")
	private Integer expectQuantity;

	@Schema(description = "预计到货重量")
	@TableField(typeHandler = WeightTypeHandle.class)
	private BigDecimal expectWeight;
	/**
	 * 入库实际数量
	 */
	@Schema(description = "入库实际数量")
	private Integer actualQuantity;
	/**
	 * 入库实际重量
	 */
	@Schema(description = "入库实际重量")
	@TableField(typeHandler = WeightTypeHandle.class)
	private BigDecimal actualWeight;
	/**
	 * 每斤采购价
	 */
	@Schema(description = "每斤采购价")
	@TableField(typeHandler = AmountTypeHandle.class)
	private BigDecimal unitPrice;
	/**
	 * 每斤询价
	 */
	@Schema(description = "每斤询价")
	@TableField(typeHandler = AmountTypeHandle.class)
	private BigDecimal unitInquiry;
	/**
	 * 整件采购价
	 */
	@Schema(description = "整件采购价")
	@TableField(typeHandler = AmountTypeHandle.class)
	private BigDecimal wholePrice;
	/**
	 * 整件询价
	 */
	@Schema(description = "整件询价")
	@TableField(typeHandler = AmountTypeHandle.class)
	private BigDecimal wholeInquiry;

	@Schema(description = "剩余数量")
	private Integer remainingQuantity;

	@Schema(description = "剩余重量")
	@TableField(typeHandler = WeightTypeHandle.class)
	private BigDecimal remainingWeight;

	@Schema(description = "是否允许删除(0-允许 1-不允许)")
	private Integer allowDeleted;

	public Integer deductQuantity(Integer quantity) {
		if (Func.isNull(quantity)) {
			return 0;
		}
		if (quantity < 0) {
			throw new ServiceException("扣减数量不能为负数");
		}
		if (quantity > 0) {
			int newRemainingQuantity = Math.max(remainingQuantity - quantity, 0);
			int excessQuantity = Math.max(quantity - remainingQuantity, 0);
			this.remainingQuantity = newRemainingQuantity;
			return excessQuantity;
		}
		return 0;
	}

	public BigDecimal deductWeight(BigDecimal weight) {
		if (Func.isNull(weight)) {
			return BigDecimal.ZERO;
		}
		if (weight.compareTo(BigDecimal.ZERO) < 0) {
			throw new ServiceException("扣减重量不能为负数");
		}
		if (weight.compareTo(BigDecimal.ZERO) > 0) {
			BigDecimal newRemainingWeight = remainingWeight.subtract(weight).max(BigDecimal.ZERO);
			BigDecimal excessWeight = weight.subtract(remainingWeight).max(BigDecimal.ZERO);
			this.remainingWeight = newRemainingWeight;
			return excessWeight;
		}
		return BigDecimal.ZERO;
	}
}
