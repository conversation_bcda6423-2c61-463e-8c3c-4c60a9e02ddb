/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.business.order.pojo.entity.SupplierReconciliationRecordEntity;
import org.springblade.modules.business.order.pojo.vo.SupplierReconciliationRecordVO;
import org.springblade.modules.business.order.excel.SupplierReconciliationRecordExcel;
import org.springblade.modules.business.order.wrapper.SupplierReconciliationRecordWrapper;
import org.springblade.modules.business.order.service.ISupplierReconciliationRecordService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 *  供应商对账单操作记录表控制器
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/supplierReconciliationRecord")
@Tag(name = "供应商对账单操作记录表", description = "供应商对账单操作记录表接口")
public class SupplierReconciliationRecordController extends BladeController {

	private final ISupplierReconciliationRecordService supplierReconciliationRecordService;

	/**
	 *  详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入supplierReconciliationRecord")
	public R<SupplierReconciliationRecordVO> detail(SupplierReconciliationRecordEntity supplierReconciliationRecord) {
		SupplierReconciliationRecordEntity detail = supplierReconciliationRecordService.getOne(Condition.getQueryWrapper(supplierReconciliationRecord));
		return R.data(SupplierReconciliationRecordWrapper.build().entityVO(detail));
	}

	/**
	 *  分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入supplierReconciliationRecord")
	public R<IPage<SupplierReconciliationRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> supplierReconciliationRecord, Query query) {
		IPage<SupplierReconciliationRecordEntity> pages = supplierReconciliationRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(supplierReconciliationRecord, SupplierReconciliationRecordEntity.class));
		return R.data(SupplierReconciliationRecordWrapper.build().pageVO(pages));
	}


	/**
	 *  自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入supplierReconciliationRecord")
	public R<IPage<SupplierReconciliationRecordVO>> page(SupplierReconciliationRecordVO supplierReconciliationRecord, Query query) {
		IPage<SupplierReconciliationRecordVO> pages = supplierReconciliationRecordService.selectSupplierReconciliationRecordPage(Condition.getPage(query), supplierReconciliationRecord);
		return R.data(pages);
	}

	/**
	 *  新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入supplierReconciliationRecord")
	public R save(@Valid @RequestBody SupplierReconciliationRecordEntity supplierReconciliationRecord) {
		return R.status(supplierReconciliationRecordService.save(supplierReconciliationRecord));
	}

	/**
	 *  修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入supplierReconciliationRecord")
	public R update(@Valid @RequestBody SupplierReconciliationRecordEntity supplierReconciliationRecord) {
		return R.status(supplierReconciliationRecordService.updateById(supplierReconciliationRecord));
	}

	/**
	 *  新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入supplierReconciliationRecord")
	public R submit(@Valid @RequestBody SupplierReconciliationRecordEntity supplierReconciliationRecord) {
		return R.status(supplierReconciliationRecordService.saveOrUpdate(supplierReconciliationRecord));
	}

	/**
	 *  删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(supplierReconciliationRecordService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-supplierReconciliationRecord")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "导出数据", description  = "传入supplierReconciliationRecord")
	public void exportSupplierReconciliationRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> supplierReconciliationRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SupplierReconciliationRecordEntity> queryWrapper = Condition.getQueryWrapper(supplierReconciliationRecord, SupplierReconciliationRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SupplierReconciliationRecordEntity::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SupplierReconciliationRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SupplierReconciliationRecordExcel> list = supplierReconciliationRecordService.exportSupplierReconciliationRecord(queryWrapper);
		ExcelUtil.export(response, "数据" + DateUtil.time(), "数据表", list, SupplierReconciliationRecordExcel.class);
	}

}
