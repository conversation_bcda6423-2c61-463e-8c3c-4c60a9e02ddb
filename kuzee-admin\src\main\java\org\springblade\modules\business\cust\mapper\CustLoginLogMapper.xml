<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustLoginLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custLoginLogResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustLoginLogEntity">
        <result column="cust_id" property="custId"/>
        <result column="ip" property="ip"/>
        <result column="login_type" property="loginType"/>
    </resultMap>

    <select id="selectCustLoginLogPage" resultMap="custLoginLogResultMap">
        select * from chy_cust_login_log where is_deleted = 0
    </select>

    <select id="exportCustLoginLog" resultType="org.springblade.modules.business.cust.excel.CustLoginLogExcel">
        SELECT * FROM chy_cust_login_log ${ew.customSqlSegment}
    </select>

</mapper>
