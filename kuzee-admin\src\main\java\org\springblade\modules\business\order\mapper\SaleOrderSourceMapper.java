/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.mapper;

import org.springblade.modules.business.order.pojo.entity.SaleOrderSourceEntity;
import org.springblade.modules.business.order.pojo.vo.SaleOrderSourceVO;
import org.springblade.modules.business.order.excel.SaleOrderSourceExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 销售订单原始数据表-支付成功时候的数据 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface SaleOrderSourceMapper extends BaseMapper<SaleOrderSourceEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param saleOrderSource 查询参数
	 * @return List<SaleOrderSourceVO>
	 */
	List<SaleOrderSourceVO> selectSaleOrderSourcePage(IPage page, SaleOrderSourceVO saleOrderSource);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<SaleOrderSourceExcel>
	 */
	List<SaleOrderSourceExcel> exportSaleOrderSource(@Param("ew") Wrapper<SaleOrderSourceEntity> queryWrapper);

}
