package org.springblade.modules.business.cust.pojo.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CustListPageDTO {

	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称/电话号码")
	private String custName;
	/**
	 * 所属分区
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "所属分区")
	private Long warehouseId;

	/**
	 *联系人
	 */
	@Schema(description = "联系人")
	private String contacts;

	/**
	 * 手机号
	 */
	@Schema(description = "手机号")
	private String phone;

	@Schema(
		description = "业务状态"
	)
	private Integer status;

	/**
	 * 业务员id
	 */
	@Schema(description = "业务员id")
	private Long userId;

	//是否移动端
	@Schema(description = "是否移动过端 1 是移动端")
	private int isApp;
}
