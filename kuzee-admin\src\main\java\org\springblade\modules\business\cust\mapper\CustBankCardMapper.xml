<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustBankCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custBankCardResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustBankCardEntity">
        <result column="cust_id" property="custId"/>
        <result column="bank_name" property="bankName"/>
        <result column="card_number" property="cardNumber"/>
        <result column="holder_name" property="holderName"/>
        <result column="is_default" property="isDefault"/>
    </resultMap>

    <select id="selectCustBankCardPage" resultMap="custBankCardResultMap">
        select * from chy_cust_bank_card where is_deleted = 0
    </select>

    <select id="exportCustBankCard" resultType="org.springblade.modules.business.cust.excel.CustBankCardExcel">
        SELECT * FROM chy_cust_bank_card ${ew.customSqlSegment}
    </select>

</mapper>
