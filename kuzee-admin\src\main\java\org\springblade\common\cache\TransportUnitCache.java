package org.springblade.common.cache;

import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.modules.business.product.pojo.entity.TransportUnitEntity;
import org.springblade.modules.business.product.service.ITransportUnitService;

import java.util.Optional;

import static org.springblade.core.cache.constant.CacheConstant.BIZ_CACHE;

public class TransportUnitCache {

	private static final String TRANSPORT_ID = "transportUnit:id:";

	private static final ITransportUnitService transportUnitService;

	static {
		transportUnitService = SpringUtil.getBean(ITransportUnitService.class);
	}

	public static TransportUnitEntity getById(Long id) {
		return CacheUtil.get(BIZ_CACHE, TRANSPORT_ID, id, () -> transportUnitService.getById(id), Boolean.FALSE);
	}

	public static String getNameById(Long id) {
		return Optional.ofNullable(getById(id)).map(TransportUnitEntity::getTransportName).orElse(null);
	}
}
