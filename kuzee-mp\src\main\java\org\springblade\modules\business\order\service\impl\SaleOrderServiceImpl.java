/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.google.zxing.WriterException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.DictCache;
import org.springblade.common.cache.RegionCache;
import org.springblade.common.config.CustomProperties;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utills.AnalysisUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.common.utills.PicUtil;
import org.springblade.common.utills.PriceCalculator;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.QRCodeGeneratorUtil;
import org.springblade.common.utils.SupportTransUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.message.enums.MessageJumpEnum;
import org.springblade.message.enums.MessageTypeEnum;
import org.springblade.modules.business.cust.pojo.entity.*;
import org.springblade.modules.business.cust.service.*;
import org.springblade.modules.business.order.mapper.*;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.*;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.product.mapper.CartItemMapper;
import org.springblade.modules.business.product.mapper.ProductMapper;
import org.springblade.modules.business.product.mapper.SkuWarehouseRelationMapper;
import org.springblade.modules.business.product.pojo.entity.*;
import org.springblade.modules.business.product.service.ISkuOemService;
import org.springblade.modules.business.product.service.ISkuPpService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.product.service.ITransportUnitService;
import org.springblade.modules.business.promotion.mapper.SecKillActivityMapper;
import org.springblade.modules.business.promotion.mapper.SecKillActivitySessionsMapper;
import org.springblade.modules.business.promotion.mapper.SecKillActivitySkusMapper;
import org.springblade.modules.business.promotion.pojo.entity.SecKillActivityEntity;
import org.springblade.modules.business.promotion.pojo.entity.SecKillActivitySessionsEntity;
import org.springblade.modules.business.promotion.pojo.entity.SecKillActivitySkusEntity;
import org.springblade.modules.business.promotion.pojo.vo.SessionTimeVO;
import org.springblade.modules.business.promotion.service.ISecKillActivitySessionsService;
import org.springblade.modules.business.warehouse.mapper.InventoryMapper;
import org.springblade.modules.business.order.pojo.entity.SaleOrderItemSourceEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.service.*;
import org.springblade.modules.business.order.service.ISaleOrderItemSourceService;
import org.springblade.modules.system.pojo.entity.Region;
import org.springblade.modules.system.service.IUserOauthService;
import org.springblade.pay.dto.request.PayContext;
import org.springblade.pay.dto.request.RefundContext;
import org.springblade.pay.dto.response.PayResult;
import org.springblade.pay.engine.PayEngine;
import org.springblade.pay.engine.RefundEngine;
import org.springblade.pay.enums.PaySceneTypeEnum;
import org.springblade.pay.enums.RefundSceneTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 销售订单表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class SaleOrderServiceImpl extends BaseServiceImpl<SaleOrderMapper, SaleOrderEntity> implements ISaleOrderService {
	private final ISaleOrderDiffService saleOrderDiffService;
	private final ICustWalletService custWalletService;
	private final ICustWalletTradeRecordService custWalletTradeRecordService;
	private final ICustService custService;
	private final ICustReceiveAddressService custReceiveAddressService;
	private final ISaleOrderItemService saleOrderItemService;

	private final ISkuStockService skuStockService;
	private final ITransportUnitService transportUnitService;

	private final ISkuPpService skuPpService;

	private final ISkuOemService skuOemService;

	private final SkuWarehouseRelationMapper skuWarehouseRelationMapper;

	private final IWarehouseService warehouseService;
	private final ProductMapper productMapper;

	private final PurchaseOrderMapper purchaseOrderMapper;
	private final IPurchaseOrderItemService purchaseOrderItemService;

	private final IWarehousePurchaseService warehousePurchaseService;
	private final InventoryMapper inventoryMapper;
	private final ITransportOrderService transportOrderService;
	private final ITransportOrderItemService transportOrderItemService;

	private final ISaleOrderItemCancelRecordService saleOrderItemCancelRecordService;
	private final PayEngine payEngine;
	private final RefundEngine engine;
	private final CartItemMapper cartItemMapper;
	private final IOrderAfterSalesServiceService iOrderAfterSalesServiceService;

	private final ICustCreditUsageRecordService custCreditUsageRecordService;
	private final MessageSenderService messageSenderService;

	private final SecKillActivityMapper secKillActivityMapper;
	private final SecKillActivitySkusMapper secKillActivitySkusMapper;
	private final SecKillActivitySessionsMapper secKillActivitySessionsMapper;
	private final ISecKillActivitySessionsService secKillActivitySessionsService;
	private final CustomProperties customProperties;
	private final IUserOauthService userOauthService;

	private final SaleOrderOriginalMapper saleOrderOriginalMapper;

	private final SaleOrderItemOriginalMapper saleOrderItemOriginalMapper;

	private final SaleOrderDiffMapper saleOrderDiffMapper;

	private final ISaleOrderItemSourceService saleOrderItemSourceService;

	private final ISaleOrderSourceService saleOrderSourceService;
	@Override
	public IPage<SaleOrderVO> selectSaleOrderPage(IPage<SaleOrderVO> page, SaleOrderVO saleOrder) {
		return page.setRecords(baseMapper.selectSaleOrderPage(page, saleOrder));
	}

	/**
	 * 获取订单详情
	 *
	 * @param orderId 订单id
	 * @return SalaOrderDetailVO
	 */
	@Override
	public SalaOrderDetailVO detailAll(Long orderId) {
		if (orderId == null) {
			log.warn("无效的参数: 订单ID为空");
			throw new IllegalArgumentException("订单ID不能为空");
		}

		SalaOrderDetailVO orderDetailVO = baseMapper.selectOrderDetail(orderId);
		if (orderDetailVO == null) {
			return null;
		}

		// 获取订单项列表
		List<SaleOrderItemEntity> orderItems = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda().eq(SaleOrderItemEntity::getOrderId, orderId));
		List<SalaOrderItemDetailVO> orderItemList = orderItems.stream().map(item -> {
			SalaOrderItemDetailVO itemDetailVO = new SalaOrderItemDetailVO();
			BeanUtils.copyProperties(item, itemDetailVO);
			return itemDetailVO;
		}).collect(Collectors.toList());
		orderDetailVO.setOrderItemList(orderItemList);

		//获取取消订单列表
		List<SaleOrderItemEntity> cancelOrderItems = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda().eq(SaleOrderItemEntity::getOrderId, orderId).eq(SaleOrderItemEntity::getStatus, OrderEnum.CANCELLED.getCode()));
		List<SalaOrderItemDetailVO> cancelOrderItemList = cancelOrderItems.stream().map(item -> {
			SalaOrderItemDetailVO itemDetailVO = new SalaOrderItemDetailVO();
			BeanUtils.copyProperties(item, itemDetailVO);
			return itemDetailVO;
		}).collect(Collectors.toList());
		orderDetailVO.setCancelOrderItemList(cancelOrderItemList);

		// 设置枚举值名称
		orderDetailVO.setOrderStatusName(BaseEnum.getMessageByCode(OrderEnum.class, orderDetailVO.getOrderStatus()));
		orderDetailVO.setDeliveryTypeName(BaseEnum.getMessageByCode(DeliveryEnum.class, orderDetailVO.getDeliveryType()));
		orderDetailVO.setOrderPayTypeName(BaseEnum.getMessageByCode(OrderPayEnum.class, orderDetailVO.getOrderPayType()));

		if (orderDetailVO.getDifferenceType() != null) {
			orderDetailVO.setDifferenceTypeName(BaseEnum.getMessageByCode(DifferenceEnum.class, orderDetailVO.getDifferenceType()));
		}
		if (orderDetailVO.getDifferenceStatus() != null) {
			orderDetailVO.setDifferenceStatusName(BaseEnum.getMessageByCode(DifferenceStatusEnum.class, orderDetailVO.getDifferenceStatus()));
		}

		return orderDetailVO;
	}

	/**
	 * 退差方法
	 *
	 * @param orderId 订单id
	 * @return boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean refundDifference(Long orderId) {
		if (orderId == null) {
			log.warn("无效的参数: 订单ID为空");
			throw new IllegalArgumentException("订单ID不能为空");
		}

		SaleOrderDiffEntity saleOrderDiff = saleOrderDiffService.getSaleOrderDiffByOrderId(orderId, DifferenceEnum.RETURN_DIFFERENCE.getCode());
		if (saleOrderDiff == null) {
			log.warn("未找到订单ID={} 和 差异类型={} 的销售订单差异记录", orderId, DifferenceEnum.RETURN_DIFFERENCE.getCode());
			throw new ServiceException("暂无查询到该订单退差记录");
		}

		SaleOrderEntity saleOrder = this.getOne(Wrappers.<SaleOrderEntity>query().lambda().eq(SaleOrderEntity::getId, orderId));
		if (saleOrder == null) {
			log.warn("未找到订单ID={} 的销售订单记录", orderId);
			throw new ServiceException("暂无查询到该订单记录");
		}

		Integer differenceAmount = saleOrderDiff.getDifferenceAmount();
		Integer creditaPayAmount = saleOrder.getCreditaPayAmount();

		if (creditaPayAmount == null) {
			creditaPayAmount = 0;
		}

		CustEntity cust = custService.getOne(Wrappers.<CustEntity>query().lambda().eq(CustEntity::getId, saleOrder.getCustId()));
		if (cust == null) {
			log.warn("未找到用户ID={} 的用户记录", saleOrder.getCustId());
			throw new ServiceException("暂无查询到该用户记录");
		}

		CustWalletEntity custWallet = custWalletService.getOne(Wrappers.<CustWalletEntity>query().lambda().eq(CustWalletEntity::getCustId, saleOrder.getCustId()));
		if (custWallet == null) {
			log.warn("未找到用户ID={} 的钱包记录", saleOrder.getCustId());
			throw new ServiceException("暂无查询到该用户钱包记录");
		}

		if (differenceAmount > creditaPayAmount) {
			// 全部信用额度加到信用额度中
			cust.setCreditLimit(cust.getCreditLimit() + creditaPayAmount);
			if (!custService.saveOrUpdate(cust)) {
				log.error("更新用户ID={} 的信用额度失败", saleOrder.getCustId());
				throw new ServiceException("更新用户信用额度失败");
			}

			// 剩余部分加到钱包余额中
			Integer remainingAmount = differenceAmount - creditaPayAmount;
			custWallet.setBalance(custWallet.getBalance() + remainingAmount);
			if (!custWalletService.saveOrUpdate(custWallet)) {
				log.error("更新用户ID={} 的钱包余额失败", saleOrder.getCustId());
				throw new ServiceException("更新用户钱包余额失败");
			}

			// 添加钱包交易记录表
			CustWalletTradeRecordEntity insertEntity = new CustWalletTradeRecordEntity();
			insertEntity.setCustId(saleOrder.getCustId());
			insertEntity.setAmount(remainingAmount);
			insertEntity.setBalance(custWallet.getBalance());
			insertEntity.setBizNo(saleOrder.getOrderNo());
			insertEntity.setBizType(WalletTradeBizTypeEnum.RETURN_DIFFERENCE.getCode());
			insertEntity.setTradeNo(saleOrder.getOrderNo());

			if (!custWalletTradeRecordService.save(insertEntity)) {
				log.error("保存用户ID={} 的钱包交易记录失败", saleOrder.getCustId());
				throw new ServiceException("保存钱包交易记录失败");
			}
		} else {
			// 全部差异金额加到信用额度中
			cust.setCreditLimit(cust.getCreditLimit() + differenceAmount);
			if (!custService.saveOrUpdate(cust)) {
				log.error("更新用户ID={} 的信用额度失败", saleOrder.getCustId());
				throw new ServiceException("更新用户信用额度失败");
			}
		}

		saleOrderDiff.setDifferenceOperator(AuthUtil.getUser().getNickName());
		saleOrderDiff.setDifferenceStatus(DifferenceStatusEnum.SUCCESSFUL.getCode());
		saleOrderDiffService.saveOrUpdate(saleOrderDiff);
		log.info("订单ID={} 的退差操作成功", orderId);
		return true;
	}

	/**
	 * 退单方法
	 *
	 * @param orderId 订单id
	 * @param reason  退单原因
	 * @return boolean
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean returnOrder(Long orderId, String reason) {
		if (orderId == null) {
			log.warn("无效的参数: 订单ID为空");
			throw new IllegalArgumentException("订单ID不能为空");
		}

		SaleOrderEntity saleOrder = this.getOne(Wrappers.<SaleOrderEntity>query().lambda().eq(SaleOrderEntity::getId, orderId));
		if (saleOrder == null) {
			log.warn("未找到订单ID={} 的销售订单记录", orderId);
			throw new ServiceException("暂无查询到该订单记录");
		}
		if (Objects.equals(saleOrder.getIsCutOrder(), BusinessConstant.IS_CUT_ORDER_YES)) {
			throw new ServiceException("该订单已截单无法退单！");
		}
		saleOrder.setReturnReason(reason);
		saleOrder.setReturnHandleMan(AuthUtil.getUser().getNickName());
		saleOrder.setReturnTime(LocalDateTime.now());
		saleOrder.setStatus(OrderEnum.CANCELLED.getCode());

		if (!this.updateById(saleOrder)) {
			log.error("更新订单ID={} 的状态失败", orderId);
			throw new ServiceException("更新订单状态失败");
		}

		// 退款逻辑
		refundOrderAmounts(saleOrder);

		log.info("订单ID={} 的退单操作成功", orderId);
		return true;
	}

	/**
	 * 处理订单退款逻辑
	 *
	 * @param saleOrder 销售订单实体
	 */
	private void refundOrderAmounts(SaleOrderEntity saleOrder) {
		// 退款信用额度
		Integer creditaPayAmount = saleOrder.getCreditaPayAmount();
		if (creditaPayAmount == null) {
			creditaPayAmount = 0;
		}
		if (creditaPayAmount > 0) {
			CustEntity cust = custService.getOne(Wrappers.<CustEntity>query().lambda().eq(CustEntity::getId, saleOrder.getCustId()));
			if (cust == null) {
				log.warn("未找到用户ID={} 的用户记录", saleOrder.getCustId());
				throw new RuntimeException("暂无查询到该用户记录");
			}

			cust.setCreditLimit(cust.getCreditLimit() + creditaPayAmount);
			if (!custService.saveOrUpdate(cust)) {
				log.error("更新用户ID={} 的信用额度失败", saleOrder.getCustId());
				throw new RuntimeException("更新用户信用额度失败");
			}

			log.info("用户ID={} 的信用额度增加 {} 元", saleOrder.getCustId(), creditaPayAmount);
		}

		// 退款钱包余额
		Integer walletPayAmount = saleOrder.getWalletPayAmount();
		if (walletPayAmount == null) {
			walletPayAmount = 0;
		}
		Integer channelPayAmount = saleOrder.getChannelPayAmount();
		if (channelPayAmount == null) {
			channelPayAmount = 0;
		}
		Integer totalRefundAmount = walletPayAmount + channelPayAmount;

		if (totalRefundAmount > 0) {
			CustWalletEntity custWallet = custWalletService.getOne(Wrappers.<CustWalletEntity>query().lambda().eq(CustWalletEntity::getCustId, saleOrder.getCustId()));
			if (custWallet == null) {
				log.warn("未找到用户ID={} 的钱包记录", saleOrder.getCustId());
				throw new RuntimeException("暂无查询到该用户钱包记录");
			}

			custWallet.setBalance(custWallet.getBalance() + totalRefundAmount);
			if (!custWalletService.saveOrUpdate(custWallet)) {
				log.error("更新用户ID={} 的钱包余额失败", saleOrder.getCustId());
				throw new RuntimeException("更新用户钱包余额失败");
			}

			log.info("用户ID={} 的钱包余额增加 {} 元", saleOrder.getCustId(), totalRefundAmount);

			// 添加钱包交易记录表
			CustWalletTradeRecordEntity insertEntity = new CustWalletTradeRecordEntity();
			insertEntity.setCustId(saleOrder.getCustId());
			insertEntity.setAmount(totalRefundAmount);
			insertEntity.setBalance(custWallet.getBalance());
			insertEntity.setBizNo(saleOrder.getOrderNo());
			insertEntity.setBizType(WalletTradeBizTypeEnum.RETURN_ORDER.getCode());
			insertEntity.setTradeNo(saleOrder.getOrderNo());

			if (!custWalletTradeRecordService.save(insertEntity)) {
				log.error("保存用户ID={} 的钱包交易记录失败", saleOrder.getCustId());
				throw new RuntimeException("保存钱包交易记录失败");
			}

			log.info("用户ID={} 的钱包交易记录保存成功", saleOrder.getCustId());
		}
	}


	@Override
	@Transactional
	public SaleOrderEntity addSave(OrderSaveDTO dto) {
		log.info("[购买下单]开始下单");
		//todo 获取信息客户ID
		Long custId = custService.getCustomId();
		CustEntity custPEntity = custService.getCustomPId();
		Long custPId = 0l;
		if (custPEntity != null) {
			custPId = custPEntity.getId();
		} else {
			custPId = custId;
		}
		//Long custId = 1899702448679829506l;
		Long warehouseId = 0l;

		//判断当前的订单是不是属于加单
		boolean isAddOrder = false;
		CustEntity customer = custService.getById(custId);
		if (customer == null) {
			throw new RuntimeException("客户不存在");
		}
		warehouseId = customer.getWarehouseId();
		CustReceiveAddressEntity receiveAddressEntity = custReceiveAddressService.getById(dto.getReceiveAddressId());
		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			if (custReceiveAddressService.getById(dto.getReceiveAddressId()) == null) {
				throw new ServiceException("收货地址不存在");
			}
			if (!Objects.equals(customer.getId(), receiveAddressEntity.getCustId())) {
				throw new ServiceException("收货地址不属于该客户");
			}
			log.info("[购买下单]验证收货地址: 地址ID={}, 客户ID={}", dto.getReceiveAddressId(), customer.getId());

		}
		if (customer.getWarehouseId() == null) {
			throw new ServiceException("客户没有选择仓库");
		}
		log.info("[购买下单]下单客户{},对应的创库{}", customer.getId(), warehouseId);
		//获取仓库信息
		WarehouseEntity warehouseEntity = warehouseService.getById(customer.getWarehouseId());
		if (warehouseEntity == null) {
			throw new ServiceException("仓库不存在");
		}
		boolean isOpen = true;//判断当前时间仓库是否在营业时间范围内
		//判断当前时间是不是仓库的营业时间范围内
		LocalTime nowTime = LocalTime.now();
		//获取截单时间
		LocalTime cutTime = LocalTime.parse(warehouseEntity.getCutTime(), DateTimeFormatter.ofPattern("HH:mm"));
		if (nowTime.isAfter(cutTime)) {
			isAddOrder = true;
		}
		log.info("[购买下单]仓库信息: ID={}, 营业时间:{}-{}", warehouseEntity.getId(), warehouseEntity.getBusinessStartTime(), warehouseEntity.getBusinessEndTime());

		//获取商品中的所有的所有sku信息
		List<Long> skuIds = dto.getOrderItem().stream().map(OrderSaveItemDTO::getProductSkuId).collect(Collectors.toList());
		List<SkuStockEntity> skuStocks = skuStockService.listByIds(skuIds);

		//获取商品信息
		List<Long> productIds = skuStocks.stream().map(SkuStockEntity::getProductId).distinct().collect(Collectors.toList());
		List<ProductEntity> products = productMapper.getIds(productIds);

		//判断需要几个运输单位ID 并且不等于空
		List<SkuStockEntity> skuTransportIds = skuStocks.stream().filter(q -> Func.isNotEmpty(q.getTransportUnitId())).toList();
		//  获取框架的价格信息
		List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
		if (skuTransportIds.size() > 0) {
			transportUnitEntities = transportUnitService.getIds(skuTransportIds.stream().map(SkuStockEntity::getTransportUnitId).collect(Collectors.toList()));
		}
		//获取商品当前价格
		//专采的情况
		List<SkuPpEntity> ppSku = skuPpService.getPpSku(skuIds);
		//专供
		List<SkuOemEntity> oemSku = skuOemService.getOemSku(skuIds, warehouseId);
		//判断当前仓库是总仓还是分仓
		Long warehouseIdSku = warehouseId;
		if (Objects.equals(warehouseEntity.getWarehouseType(), WarehouseTypeEnum.DIVIDE.getCode())) {
			warehouseIdSku = warehouseEntity.getParentId();
		}
		//获取仓库的库存信息
		List<SkuWarehouseRelationEntity> skuWarehouseRelations = skuWarehouseRelationMapper.getByWarehouseId(warehouseIdSku, skuIds);
		List<SkuWarehouseRelationEntity> skuWarehouseRelationsMy = skuWarehouseRelationMapper.getByWarehouseId(warehouseId, skuIds);

		List<SaleOrderItemEntity> saleOrderItemEntities = new ArrayList<>();
		List<SkuPpEntity> updatePpSku = new ArrayList<>();
		List<SkuOemEntity> updateOemSku = new ArrayList<>();
		List<SkuWarehouseRelationEntity> updateSkuWarehouseRelations = new ArrayList<>();

		//判断是不是添加运费
		boolean isDelivery = false;
		Integer deliveryPrice = 0;
		if (Objects.equals(dto.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
			isDelivery = true;
			deliveryPrice = warehouseEntity.getDeliveryFee();
		}
		boolean isProOpen = true; //判断当前购买的所有商品是不是存在营业时间
		for (OrderSaveItemDTO item : dto.getOrderItem()) {
			SaleOrderItemEntity saleOrderItemEntity = new SaleOrderItemEntity();
			saleOrderItemEntity.setWarehouseId(warehouseId);
			//获取对应的Sku信息
			SkuStockEntity skuStock = skuStocks.stream().filter(q -> q.getId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (skuStock == null) {
				throw new ServiceException("商品不存在");
			}
			//判断商品营业时间状态
//			if (!isOpen) {
			skuHoursVerification(warehouseEntity, skuStock, nowTime);
//			}
			//判断是不是加单状态
			if (isAddOrder) {
				if (Func.isEmpty(skuStock.getBusinessStartTime()) || Func.isEmpty(skuStock.getBusinessEndTime())) {
					isProOpen = false;
				}
			}
			log.info("[购买下单]当前订单是否为加单: {}", isAddOrder ? "是" : "否");

			saleOrderItemEntity.setProductId(skuStock.getProductId());
			//获取运输单位信息
			if (Func.isNotEmpty(skuStock.getTransportUnitId()) && skuStock.getIsContainBox() == 1) {
				TransportUnitEntity transportUnitEntity = transportUnitEntities.stream().filter(q -> q.getId().equals(skuStock.getTransportUnitId())).findFirst().orElse(null);
				assert transportUnitEntity != null;
				saleOrderItemEntity.setSupportTrans(transportUnitEntity.getTransportName());
				saleOrderItemEntity.setSupportTransPrice(transportUnitEntity.getPrice());
				saleOrderItemEntity.setSupportTransUnitId(transportUnitEntity.getId());
				//计算出需要多少运输单位
				saleOrderItemEntity.setSupportTransNum(item.getProductQuantity() * skuStock.getTransportConversionRate());
			}
			saleOrderItemEntity.setProductQuantity(item.getProductQuantity());
			saleOrderItemEntity.setProductSkuId(item.getProductSkuId());
			//获取商品信息
			ProductEntity productEntity = products.stream().filter(q -> q.getId().equals(skuStock.getProductId())).findFirst().orElse(null);
			if (productEntity == null) {
				throw new ServiceException("商品不存在");
			}
			saleOrderItemEntity.setProductPic(PicUtil.getPic(productEntity.getPic()));
			saleOrderItemEntity.setProductBrand(productEntity.getBrandName());
			saleOrderItemEntity.setProductUnit(productEntity.getUnitName());
			saleOrderItemEntity.setProductCategoryId(productEntity.getProductCategoryId());
			saleOrderItemEntity.setProductAttr(skuStock.getSpData());
			saleOrderItemEntity.setProductName(productEntity.getName());
			saleOrderItemEntity.setSkuCode(skuStock.getSkuCode());
			saleOrderItemEntity.setPackageGrossConversionRate(skuStock.getPackageGrossConversionRate());
			saleOrderItemEntity.setIsStandard(productEntity.getIsStandard());
			saleOrderItemEntity.setTransportConversionRate(skuStock.getTransportConversionRate());

			Integer price = 0;
			Integer addPrice = 0;
			Integer priceType = 0;
			SkuWarehouseRelationEntity skuWarehouseRelationMy = skuWarehouseRelationsMy.stream().filter(q -> q.getSkuId().equals(skuStock.getId())).findFirst().orElse(null);
			if (Func.isNotEmpty(skuWarehouseRelationMy)) {
				assert skuWarehouseRelationMy != null;
				addPrice = skuWarehouseRelationMy.getAddPrice();
			}
			//是否已经运算过价格
			boolean isCalcPrice = false;

			if (skuWarehouseRelationsMy.size() > 0 && !isCalcPrice) {
				SkuWarehouseRelationEntity skuWarehouseRelation = skuWarehouseRelationsMy.stream().filter(q -> q.getSkuId().equals(skuStock.getId())).findFirst().orElse(null);
				if (Func.isNotEmpty(skuWarehouseRelation)) {
					assert skuWarehouseRelation != null;
					price = skuWarehouseRelation.getPrice();
					priceType = skuWarehouseRelation.getPriceType();
					if (Func.isNotEmpty(skuWarehouseRelation.getLimitPurchaseQuantity())) {
						//判断是否有限购数量
						if (item.getProductQuantity() <= skuWarehouseRelation.getLimitPurchaseQuantity() - skuWarehouseRelation.getSold()) {
							if (Func.isNotEmpty(skuWarehouseRelation.getSold()))
								skuWarehouseRelation.setSold(skuWarehouseRelation.getSold() + item.getProductQuantity());
							else
								skuWarehouseRelation.setSold(item.getProductQuantity());
							updateSkuWarehouseRelations.add(skuWarehouseRelation);
						} else {
							throw new ServiceException("您购买的" + productEntity.getName() + "商品限购数量已满");
						}
					} else {
//						if (Func.isNotEmpty(skuWarehouseRelation.getSold()))
//							skuWarehouseRelation.setSold(skuWarehouseRelation.getSold() + item.getProductQuantity());
//						else
//							skuWarehouseRelation.setSold(item.getProductQuantity());
//						updateSkuWarehouseRelations.add(skuWarehouseRelation);
					}
					isCalcPrice = true;
				} else {
					log.error("[购买下单]获取的商品数据为空：" + skuWarehouseRelation);
					throw new ServiceException("数据异常,请联系管理员");
				}
			}
			int serviceFee = 0;
			if (skuStock.getIsServiceFee().equals(IsServiceFeeEnum.YES.getCode()))
				serviceFee = warehouseEntity.getServiceFee();
			Integer priceUnit = PriceCalculator.getPrice(price, priceType, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, 1);
			Integer priceSale = PriceCalculator.getPriceGrossWeight(price, priceType, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, new BigDecimal(1));
			saleOrderItemEntity.setProductSalePrice(priceSale);
			saleOrderItemEntity.setProductUnitPrice(priceUnit);

			if (isDelivery) {
				int freight = 0;
//				if (priceType.equals(1)){
//					freight+= PriceCalculator.getDeliveryFee(warehouseEntity.getDeliveryFee() , 1,BigDecimal.valueOf(item.getProductQuantity()) , skuStock.getPackageGrossConversionRate());
//				}else {
				freight += PriceCalculator.getDeliveryFee(warehouseEntity.getDeliveryFee(), 0, BigDecimal.valueOf(item.getProductQuantity()), skuStock.getPackageGrossConversionRate());
//				}
				saleOrderItemEntity.setDeliveryExpense(freight);
			}
			//仅商品的价格
			//saleOrderItemEntity.setProductPrice(priceUnit * item.getProductQuantity());
			saleOrderItemEntity.setProductPrice(getProductPrice(item.getProductQuantity(), skuStock.getPackageGrossConversionRate(), new BigDecimal(priceSale)));
			saleOrderItemEntity.setNote(item.getNote());

			if (skuStock.getIsServiceFee().equals(1))
				saleOrderItemEntity.setServiceExpense(getServerFee(warehouseEntity.getServiceFee(), skuStock.getPackageGrossConversionRate(), item.getProductQuantity()));
				//saleOrderItemEntity.setServiceExpense(  warehouseEntity.getServiceFee() * Integer.valueOf(String.valueOf((new BigDecimal(item.getProductQuantity()).multiply(skuStock.getPackageGrossConversionRate())).setScale(0, RoundingMode.UP))));
			else
				saleOrderItemEntity.setServiceExpense(0);

			log.info("[购买下单]商品SKU: {}{}, 单价: {}, 类型: {}", skuStock.getId(), productEntity.getName(), price, priceType);

			saleOrderItemEntities.add(saleOrderItemEntity);
		}
		log.info("[购买下单]准备保存订单: 总运费={}, 服务费={}", deliveryPrice, warehouseEntity.getServiceFee());

		log.info("[购买下单]获取到的SKU: {}", saleOrderItemEntities);
		//订单入库
		SaleOrderEntity saleOrder = saveOrder(dto, custPId, saleOrderItemEntities, receiveAddressEntity, warehouseId, updatePpSku, updateOemSku,
			updateSkuWarehouseRelations, deliveryPrice, warehouseEntity.getServiceFee(), isAddOrder, isProOpen, isDelivery, false, custId);

		//删除购物信息

		QueryWrapper<CartItemEntity> cartItemQueryWrapper = new QueryWrapper();
		cartItemQueryWrapper.in("sku_id", skuIds);
		cartItemQueryWrapper.eq("custom_id", custId);
		//cartItemMapper.delete(cartItemQueryWrapper);

		List<CartItemEntity> cartItemList = cartItemMapper.selectList(cartItemQueryWrapper);
		if (CollectionUtil.isNotEmpty(cartItemList)) {
			cartItemMapper.deletePhysical(cartItemList.stream().map(CartItemEntity::getId).collect(Collectors.toList()));
		}
		log.info("[购买下单]购物车信息已删除: SKU数量={}", skuIds.size());

		return saleOrder;
	}

	/**
	 * 保存订单
	 *
	 * @param dto
	 * @param custPId
	 * @param saleOrderItemEntities
	 * @param receiveAddressEntity
	 * @param warehouseId
	 * @param updatePpSku
	 * @param updateOemSku
	 * @param updateSkuWarehouseRelations
	 * @param deliveryPrice
	 * @param servicefee
	 * @param isAddOrder
	 * @param isProOpen
	 * @param isDelivery
	 * @param isSeckill
	 * @param custId
	 * @return
	 */
	private SaleOrderEntity saveOrder(OrderSaveDTO dto, Long custPId, List<SaleOrderItemEntity> saleOrderItemEntities,
									  CustReceiveAddressEntity receiveAddressEntity, Long warehouseId, List<SkuPpEntity> updatePpSku,
									  List<SkuOemEntity> updateOemSku, List<SkuWarehouseRelationEntity> updateSkuWarehouseRelations,
									  Integer deliveryPrice, Integer servicefee, boolean isAddOrder, boolean isProOpen, boolean isDelivery, boolean isSeckill, Long custId) {

		Integer deliveryPriceNum = 0;
		//判断总运费
		if (isDelivery) {
			deliveryPriceNum = saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum();
		}

		//判断信用额度
		CalculateRepaymentDTO calculateRepaymentDTO = new CalculateRepaymentDTO();
		calculateRepaymentDTO.setOrderIds(null);
		calculateRepaymentDTO.setIsSelectAll(true);
		calculateRepaymentDTO.setIsOutsideCycle(true);
		RepaymentAmountVO repaymentAmountVO = calculateRepaymentAmount(calculateRepaymentDTO);
		if (Func.isNotEmpty(repaymentAmountVO.getTotalRepaymentAmount()) && repaymentAmountVO.getTotalRepaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
			throw new ServiceException("您的信用额度没有及时归还，请归还之后在使用信用额度");
		}
		log.info("[购买下单]校验信用额度: 客户ID={}, 当前待还款金额={}", custId, repaymentAmountVO.getTotalRepaymentAmount());

		//更新购买信息
		if (updatePpSku != null && updatePpSku.size() > 0) {
			skuPpService.updateBatchById(updatePpSku);
		}
		if (updateOemSku != null && updateOemSku.size() > 0) {
			skuOemService.updateBatchById(updateOemSku);
		}
		if (updateSkuWarehouseRelations != null && updateSkuWarehouseRelations.size() > 0) {
			skuWarehouseRelationMapper.updateById(updateSkuWarehouseRelations);
		}
		log.info("[购买下单]更新购买信息: 更新PP商品数量={}, 更新OEM商品数量={}, 更新仓库关系商品数量={}",
			updatePpSku != null ? updatePpSku.size() : 0,
			updateOemSku != null ? updateOemSku.size() : 0,
			updateSkuWarehouseRelations != null ? updateSkuWarehouseRelations.size() : 0);

		String orderCode = GenerateNumberUtil.OrderCode();
		log.info("[购买下单]订单编号: {}", orderCode);
		//时间戳
		Long timeStamp = GenerateNumberUtil.TimeStamp();
		//订单总金额
		int totalAmountInt = getTotalAmount(saleOrderItemEntities, isDelivery);
//		for (SaleOrderItemEntity item : saleOrderItemEntities) {
//			if (Func.isNotEmpty(item.getSupportTransUnitId())) {
//				totalAmountInt += item.getProductPrice() + (item.getSupportTransPrice() * item.getSupportTransNum());
//			} else {
//				totalAmountInt += item.getProductPrice();
//			}
//			if (isDelivery) {
//				totalAmountInt += item.getDeliveryExpense();
//			}
//			if (Func.isNotEmpty(item.getServiceExpense())) {
//				totalAmountInt += item.getServiceExpense();
//			}
//		}
		log.info("[购买下单]验证最低结算金额: 总金额={}, 是否满足条件={}", totalAmountInt, isDelivery);

		validateMinimumAmount(deliveryPriceNum, totalAmountInt, isDelivery, null);

		CustWalletEntity sufficientAmount = custWalletService.isSufficientAmount(custId);
		if (sufficientAmount.getBalance() < 0) {
			totalAmountInt += Math.abs(sufficientAmount.getBalance());
		}
		log.info("[购买下单]计算订单总金额: 商品总金额={}, 运费总金额={}, 是否使用运费={}", totalAmountInt, deliveryPriceNum, isDelivery);

		// 支付金额
		Integer creditaPayAmount = 0;//信用额度支付
		Integer walletPayAmount = 0;//钱包支付
		Integer channelPayAmount = 0;//渠道支付
		if (Objects.equals(dto.getPayType(), PayTypeEnum.PLATFORM_QUOTA.getCode())) {
			creditaPayAmount = custService.freezeBalance(custId, totalAmountInt, orderCode);
		} else if (Objects.equals(dto.getPayType(), PayTypeEnum.WALLET.getCode())) {
			log.info("[购买下单]使用钱包支付: 下单金额={}, 钱包金额={}", totalAmountInt, sufficientAmount.getBalance());
			if (sufficientAmount.getBalance() - totalAmountInt >= 0) {
				walletPayAmount = custWalletService.freezeBalance(custId, totalAmountInt, orderCode, PayTypeEnum.WALLET.getCode());
			} else {
				dto.setPayType(PayTypeEnum.MIXED_PAYMENT.getCode());
				walletPayAmount = custWalletService.freezeBalance(custId, totalAmountInt, orderCode, PayTypeEnum.MIXED_PAYMENT.getCode());
				channelPayAmount = totalAmountInt - sufficientAmount.getBalance();
			}
		} else if (Objects.equals(dto.getPayType(), PayTypeEnum.PARTY_PAYMENT.getCode())) {
			channelPayAmount = totalAmountInt;
			//todo 第三方支付金额
		} else {
			throw new ServiceException("请选择支付方式");
		}
		log.info("[购买下单]保存订单基本信息: 订单号={}, 客户ID={}, 仓库ID={}, 支付类型={}",
			orderCode, custId, warehouseId, dto.getPayType());

		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			log.info("[购买下单]设置收货地址信息: 姓名={}, 手机={}, 地址={} {} {} {}",
				receiveAddressEntity.getName(), receiveAddressEntity.getPhone(),
				receiveAddressEntity.getProvince(), receiveAddressEntity.getCity(),
				receiveAddressEntity.getRegion(), receiveAddressEntity.getDetailAddress());
		}

		// 保存订单
		SaleOrderEntity saleOrder = new SaleOrderEntity();
		{
			saleOrder.setOrderNo(orderCode);
			saleOrder.setCustId(custPId);
			saleOrder.setOrderCustId(custId);
			saleOrder.setTotalAmount(totalAmountInt);
			saleOrder.setPayAmount(totalAmountInt);
			saleOrder.setCreditaPayAmount(creditaPayAmount);
			saleOrder.setWalletPayAmount(walletPayAmount);
			saleOrder.setChannelPayAmount(channelPayAmount);
			saleOrder.setPayType(dto.getPayType());
			saleOrder.setSourceType(BusinessConstant.APP_Order_TYPE);
			saleOrder.setDeliveryType(dto.getDeliveryType());
			saleOrder.setWarehouseId(warehouseId);
			if (sufficientAmount.getBalance() < 0) {
				saleOrder.setWalletAmountDiff(Math.abs(sufficientAmount.getBalance()));
			}
			saleOrder.setServiceFee(servicefee);
			if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
				saleOrder.setReceiverName(receiveAddressEntity.getName());
				saleOrder.setReceiverPhone(receiveAddressEntity.getPhone());
				saleOrder.setReceiverPostCode(receiveAddressEntity.getPostCode());
				saleOrder.setReceiverProvince(receiveAddressEntity.getProvince());
				saleOrder.setReceiverCity(receiveAddressEntity.getCity());
				saleOrder.setReceiverRegion(receiveAddressEntity.getRegion());
				saleOrder.setReceiverDetailAddress(receiveAddressEntity.getDetailAddress());
			} else {
				CustEntity custEntity = custService.getById(custPId);
				saleOrder.setReceiverName(custEntity.getCustName());
				saleOrder.setReceiverPhone(custEntity.getPhone());
			}

			saleOrder.setOrderTime(LocalDateTime.now());
			saleOrder.setDeliveryFee(deliveryPrice);
			if (isSeckill) {
				saleOrder.setOrderType(OrderTypeEnum.FLASH_SALE_ORDER.getCode());
			} else {
				saleOrder.setOrderType(OrderTypeEnum.PLAIN_ORDER.getCode());
			}
			saleOrder.setPayStatus(PayStatusEnum.PENDING_PAY.getCode());
			saleOrder.setNote(dto.getNote());
			saleOrder.setCreateTimeAt(timeStamp);
			saleOrder.setStatus(OrderEnum.PURCHASE_IN_PROGRESS.getCode());
			if (isAddOrder) {
				saleOrder.setAddOrder(BusinessConstant.IS_ADD_ORDER_YES);
			} else {
				saleOrder.setAddOrder(BusinessConstant.IS_ADD_ORDER_NO);
			}
			if (isAddOrder && isProOpen) {
				saleOrder.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_NO);
				saleOrder.setBatchNo(GenerateNumberUtil.PurchaseCodeF());
			} else {
				saleOrder.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_NO);
			}
			//获取所有的运费信息
			if (saleOrderItemEntities.size() > 0) {
				saleOrder.setDeliveryExpense(saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum());
			}
			this.save(saleOrder);
			//获取商品信息
			for (SaleOrderItemEntity saveItemDTO : saleOrderItemEntities) {
				saveItemDTO.setOrderId(saleOrder.getId());
				saveItemDTO.setOrderNo(orderCode);
			}
			saleOrderItemService.saveBatch(saleOrderItemEntities);


			SaleOrderSourceEntity orderSource=Objects.requireNonNull(BeanUtil.copyProperties(saleOrder, SaleOrderSourceEntity.class));
			saleOrderSourceService.save(orderSource);

			List<SaleOrderItemSourceEntity> listSource=new ArrayList<>();
			for (SaleOrderItemEntity itemOrder:saleOrderItemEntities ) {
				SaleOrderItemSourceEntity tep=Objects.requireNonNull(BeanUtil.copyProperties(itemOrder, SaleOrderItemSourceEntity.class));
				listSource.add(tep);
			}
			if (listSource.size()>0){
				saleOrderItemSourceService.saveBatch(listSource);
			}
		}
		log.info("[购买下单]订单保存成功: 订单号={}, 订单ID={}", orderCode, saleOrder.getId());

		return saleOrder;

	}


	/**
	 * 秒杀保存订单
	 *
	 * @param dto
	 * @param custId
	 * @param saleOrderItemEntities
	 * @param warehouseId
	 * @param deliveryPrice
	 * @param servicefee
	 * @param isAddOrder
	 * @param isProOpen
	 * @param isDelivery
	 * @param isSeckill
	 * @return
	 */
	private SaleOrderEntity seckillSaveOrder(OrderSaveMQDTO dto, Long custPId, List<SaleOrderItemEntity> saleOrderItemEntities,
											 Long warehouseId, Integer deliveryPrice, Integer servicefee, boolean isAddOrder,
											 boolean isProOpen, boolean isDelivery, boolean isSeckill, Long userId, Long custId) {
		Integer deliveryPriceNum = 0;
		//判断总运费
		if (isDelivery) {
			deliveryPriceNum = saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum();
		}

		String orderCode = GenerateNumberUtil.OrderCode();
		log.info("[购买下单]订单编号: {}", orderCode);
		//时间戳
		Long timeStamp = GenerateNumberUtil.TimeStamp();
		//订单总金额
		int totalAmountInt = getTotalAmount(saleOrderItemEntities, isDelivery);
//		for (SaleOrderItemEntity item : saleOrderItemEntities) {
//			if (Func.isNotEmpty(item.getSupportTransUnitId())) {
//				totalAmountInt += item.getProductPrice() + (item.getSupportTransPrice() * item.getSupportTransNum());
//			} else {
//				totalAmountInt += item.getProductPrice();
//			}
//			if (isDelivery) {
//				totalAmountInt += item.getDeliveryExpense();
//			}
//		}
		log.info("[购买下单]验证最低结算金额: 总金额={}, 是否满足条件={}", totalAmountInt, isDelivery);
		validateMinimumAmount(deliveryPriceNum, totalAmountInt, isDelivery, userId);
		CustWalletEntity sufficientAmount = custWalletService.isSufficientAmount(custId);
		if (sufficientAmount.getBalance() < 0) {
			totalAmountInt += Math.abs(sufficientAmount.getBalance());
		}
		log.info("[购买下单]计算订单总金额: 商品总金额={}, 运费总金额={}, 是否使用运费={}", totalAmountInt, deliveryPriceNum, isDelivery);
		// 支付金额
		Integer creditaPayAmount = 0;//信用额度支付
		Integer walletPayAmount = 0;//钱包支付
		Integer channelPayAmount = 0;//渠道支付
		// 保存订单
		SaleOrderEntity saleOrder = new SaleOrderEntity();
		{
			saleOrder.setOrderNo(orderCode);
			saleOrder.setCustId(custPId);
			saleOrder.setOrderCustId(custId);
			saleOrder.setTotalAmount(totalAmountInt);
			saleOrder.setPayAmount(totalAmountInt);
			saleOrder.setCreditaPayAmount(creditaPayAmount);
			saleOrder.setWalletPayAmount(walletPayAmount);
			saleOrder.setChannelPayAmount(channelPayAmount);
			saleOrder.setPayType(99);
			saleOrder.setSourceType(BusinessConstant.APP_Order_TYPE);
			saleOrder.setDeliveryType(dto.getDeliveryType());
			saleOrder.setWarehouseId(warehouseId);
			if (sufficientAmount.getBalance() < 0) {
				saleOrder.setWalletAmountDiff(Math.abs(sufficientAmount.getBalance()));
			}
			saleOrder.setServiceFee(servicefee);
			CustEntity custEntity = custService.getById(custId);
			saleOrder.setReceiverName(custEntity.getCustName());
			saleOrder.setReceiverPhone(custEntity.getPhone());
			saleOrder.setOrderTime(LocalDateTime.now());
			saleOrder.setDeliveryFee(deliveryPrice);
			if (isSeckill) {
				saleOrder.setOrderType(OrderTypeEnum.FLASH_SALE_ORDER.getCode());
			} else {
				saleOrder.setOrderType(OrderTypeEnum.PLAIN_ORDER.getCode());
			}
			saleOrder.setPayStatus(PayStatusEnum.PENDING_PAY.getCode());
			saleOrder.setNote(dto.getNote());
			saleOrder.setCreateTimeAt(timeStamp);
			saleOrder.setStatus(OrderEnum.PURCHASE_IN_PROGRESS.getCode());
			if (isAddOrder) {
				saleOrder.setAddOrder(BusinessConstant.IS_ADD_ORDER_YES);
			} else {
				saleOrder.setAddOrder(BusinessConstant.IS_ADD_ORDER_NO);
			}
			if (isAddOrder && isProOpen) {
				saleOrder.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_YES);
				saleOrder.setBatchNo(GenerateNumberUtil.PurchaseCodeF());
			} else {
				saleOrder.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_NO);
			}
			//获取所有的运费信息
			if (saleOrderItemEntities.size() > 0) {
				saleOrder.setDeliveryExpense(saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum());
			}
			this.save(saleOrder);
			//获取商品信息
			for (SaleOrderItemEntity saveItemDTO : saleOrderItemEntities) {
				saveItemDTO.setOrderId(saleOrder.getId());
				saveItemDTO.setOrderNo(orderCode);
			}
			saleOrderItemService.saveBatch(saleOrderItemEntities);
			List<SaleOrderItemSourceEntity> listSource=new ArrayList<>();
			for (SaleOrderItemEntity itemOrder:saleOrderItemEntities ) {
				SaleOrderItemSourceEntity tep=Objects.requireNonNull(BeanUtil.copyProperties(itemOrder, SaleOrderItemSourceEntity.class));
				listSource.add(tep);
			}
			if (listSource.size()>0){
				saleOrderItemSourceService.saveBatch(listSource);
			}
		}
		log.info("[购买下单]订单保存成功: 订单号={}, 订单ID={}", orderCode, saleOrder.getId());
		return saleOrder;

	}

	@Override
	public SettlementVO settlementPanicBuy(List<SettlementPanicBuyDTO> dto) {
		if (dto.size() != 1) {
			throw new ServiceException("数据异常");
		}
		log.info("校验输入数据: DTO数量={}", dto.size());
		SettlementPanicBuyDTO settlementDTO = dto.get(0);
		SettlementVO settlementVO = new SettlementVO();
		Long customId = custService.getCustomId();
		Long warehouseId = custService.getWarehouseId(customId);
		//判断活动的时间准确性
		SecKillActivitySkusEntity secKillActivitySkusEntity = GetSessionsDuring(settlementDTO.getSessionsId(),
			settlementDTO.getSkuId(), settlementDTO.getQuantity(), customId);
		if (Func.isEmpty(secKillActivitySkusEntity)) {
			//throw new ServiceException("活动已结束");
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_ENDED);
		}
		log.info("活动信息: {}", secKillActivitySkusEntity);

		//获取仓库信息
		WarehouseEntity warehouse = warehouseService.getById(warehouseId);
		OrderWarehouseVO orderWarehouseVO = Objects.requireNonNull(BeanUtil.copyProperties(warehouse, OrderWarehouseVO.class));
		//获取订单地址
		OrderAddressVO orderAddressVO = GetCutAddress(customId);
		log.info("获取客户信息: 客户ID={}, 仓库ID={}", customId, warehouseId);

		//总运费
		int freight = 0;
		//框的费用
		BigDecimal boxFreight = BigDecimal.ZERO;
		//商品总价
		BigDecimal productTotalPrice = BigDecimal.ZERO;
		//获取订单商品
		List<Long> ids = new ArrayList<>();
		ids.add(settlementDTO.getSkuId());
		List<OrderProductVO> orderProductVOS = baseMapper.getSaleOrderProduct(ids, warehouse.getId());
		BigDecimal grossWeight = BigDecimal.ZERO;
		for (OrderProductVO productVO : orderProductVOS) {
			assert settlementDTO != null;
			freight += PriceCalculator.getDeliveryFee(warehouse.getDeliveryFee(), 0, BigDecimal.valueOf(settlementDTO.getQuantity()), productVO.getPackageGrossConversionRate());
			//获取毛总
			BigDecimal multiply = productVO.getPackageGrossConversionRate().multiply(new BigDecimal(settlementDTO.getQuantity()));
			grossWeight = grossWeight.add(multiply);

			Integer priceSale = productVO.getPrice();
			//0-询价公式，1-直接基础价
			if (secKillActivitySkusEntity.getActivityBasePriceType().equals(0)) {
				priceSale = PriceCalculator.getFormulaPriceResult(productVO.getPrice(), productVO.getPriceType(),
					secKillActivitySkusEntity.getActivityBasePrice(), productVO.getPackageGrossConversionRate());
			} else if (secKillActivitySkusEntity.getActivityBasePriceType().equals(1)) {
				//当activity_base_price_type为1时activity_base_price的类型（0-按件，1-按斤）
				if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(0)) {
					priceSale = Integer.valueOf(String.valueOf(new BigDecimal(secKillActivitySkusEntity.getActivityBasePrice()).divide(productVO.getPackageGrossConversionRate(), 0, RoundingMode.HALF_UP)));
				} else if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(1)) {
					priceSale = Integer.valueOf(secKillActivitySkusEntity.getActivityBasePrice());
				}
			} else {
				throw new ServiceException("请检查基础价类型是否正确");
			}
			log.info("计算商品价格: 商品名称={}, SKU ID={}, 基础价类型={}, 活动基础价={}, 包装转换率={}",
				productVO.getProductName(), productVO.getProductSkuId(),
				secKillActivitySkusEntity.getActivityBasePriceType(), secKillActivitySkusEntity.getActivityBasePrice(),
				productVO.getPackageGrossConversionRate());

			//计算件单价类型（斤）
			int priceType = 1;
			//计算件单价
			Integer price = PriceCalculator.getPrice(priceSale, priceType, productVO.getAddPrice(), productVO.getFormula(),
				productVO.getPackageGrossConversionRate(), Integer.valueOf(productVO.getServiceFee().toString()), 1);
			//计算单价
			Integer priceGrossWeight = PriceCalculator.getPriceGrossWeight(priceSale, priceType, productVO.getAddPrice(), productVO.getFormula(), productVO.getPackageGrossConversionRate(), Integer.valueOf(productVO.getServiceFee().toString()), new BigDecimal(1));
			productVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(price));
			productVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(priceGrossWeight));

			log.info("设置商品信息: 商品名称={}, 单价={}, 类型={}, 支持运输单位ID={}",
				productVO.getProductName(), price, priceType, productVO.getSupportTransUnitId());
			if (Func.isNotEmpty(productVO.getSupportTransUnitId())) {
				int boxNum = settlementDTO.getQuantity() * productVO.getTransportConversionRate();
				productVO.setSupportTransNum(boxNum);
				//框的价格
				boxFreight = boxFreight.add(BigDecimal.valueOf(boxNum).multiply(CommonUtil.ConvertToBigDecimal(productVO.getSupportTransPrice())));
			}
			productVO.setSupportTransPrice(CommonUtil.ConvertToBigDecimal(productVO.getSupportTransPrice()));
			//商品的价格
			//productVO.setProductPrice(productVO.getProductUnitPrice().multiply(BigDecimal.valueOf(settlementDTO.getQuantity())));
			Integer productPrice = getProductPrice(settlementDTO.getQuantity(), productVO.getPackageGrossConversionRate(), new BigDecimal(priceGrossWeight));
			productVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(productPrice));
			productTotalPrice = productTotalPrice.add(productVO.getProductPrice());
			productVO.setProductQuantity(settlementDTO.getQuantity());
			productVO.setDelivery(productVO.getPackageGrossConversionRate().multiply(new BigDecimal(productVO.getProductQuantity())));


			log.info("计算总价: 商品总价={}, 运费={}, 框费用={}",
				productTotalPrice, freight, boxFreight);
		}
		//计算总价
		orderAddressVO.setDistributionCosts(CommonUtil.ConvertIntBigDecimal(freight));
		//不含邮费的总价格Objects.requireNonNull(CommonUtil.ConvertIntBigDecimal(freight))
		settlementVO.setTotalAmount(Objects.requireNonNull(boxFreight).add(productTotalPrice));
		settlementVO.setTotalWeight(grossWeight);
		settlementVO.setDeliveryFee(CommonUtil.ConvertIntBigDecimal(warehouse.getDeliveryFee()));
		settlementVO.setAddress(orderAddressVO);
		settlementVO.setWarehouse(orderWarehouseVO);
		settlementVO.setData(orderProductVOS);
		settlementVO.setTotalDelivery(CommonUtil.ConvertIntBigDecimal(freight));
		log.info("返回结算结果: 总金额={}, 总重量={}, 配送费={}, 地址={}, 仓库信息={}",
			settlementVO.getTotalAmount(), settlementVO.getTotalWeight(), settlementVO.getDeliveryFee(),
			orderAddressVO, orderWarehouseVO);
		return settlementVO;
	}

	@Override
	public SaleOrderEntity addSavePanicBuy(OrderSaveDTO dto) {
		Long custId = custService.getCustomId();
		CustEntity custPEntity = custService.getCustomPId();
		Long custPId = 0l;
		if (custPId == null) {
			custPId = custId;
		} else {
			custPId = custPEntity.getId();
		}
		Long warehouseId = 0l;
		//判断当前的订单是不是属于加单
		boolean isAddOrder = false;
		CustEntity customer = custService.getById(custId);
		if (customer == null) {
			throw new ServiceException("客户不存在");
		}

		//判断活动的时间准确性
		if (dto.getOrderItem().size() != 1) {
			throw new ServiceException("请选择商品异常");
		}
		OrderSaveItemDTO saveItemDTO = dto.getOrderItem().get(0);
		SecKillActivitySkusEntity secKillActivitySkusEntity = GetSessionsDuring(dto.getSessionsId(),
			saveItemDTO.getProductSkuId(), saveItemDTO.getProductQuantity(), custId);
		if (Func.isEmpty(secKillActivitySkusEntity)) {
			//throw new ServiceException("活动已结束");
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_ENDED);
		}
		warehouseId = customer.getWarehouseId();
		CustReceiveAddressEntity receiveAddressEntity = custReceiveAddressService.getById(dto.getReceiveAddressId());
		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			if (custReceiveAddressService.getById(dto.getReceiveAddressId()) == null) {
				throw new ServiceException("收货地址不存在");
			}
			if (customer.getId() == receiveAddressEntity.getCustId()) {
				throw new ServiceException("收货地址不属于该客户");
			}
		}
		if (customer.getWarehouseId() == null) {
			throw new ServiceException("客户没有选择仓库");
		}
		//获取仓库信息
		WarehouseEntity warehouseEntity = warehouseService.getById(customer.getWarehouseId());
		if (warehouseEntity == null) {
			throw new ServiceException("仓库不存在");
		}
		boolean isOpen = true;//判断当前时间仓库是否在营业时间范围内
		//判断当前时间是不是仓库的营业时间范围内
		//判断当前时间是不是仓库的营业时间范围内
		LocalTime nowTime = LocalTime.now();
		//获取截单时间
		LocalTime cutTime = LocalTime.parse(warehouseEntity.getCutTime(), DateTimeFormatter.ofPattern("HH:mm"));
		if (nowTime.isAfter(cutTime)) {
			isAddOrder = true;
		}
		//获取商品中的所有的所有sku信息
		List<Long> skuIds = dto.getOrderItem().stream().map(OrderSaveItemDTO::getProductSkuId).collect(Collectors.toList());
		List<SkuStockEntity> skuStocks = skuStockService.listByIds(skuIds);
		//获取商品信息
		List<Long> productIds = skuStocks.stream().map(SkuStockEntity::getProductId).distinct().collect(Collectors.toList());
		List<ProductEntity> products = productMapper.getIds(productIds);
		//判断需要几个运输单位ID 并且不等于空
		List<SkuStockEntity> skuTransportIds = skuStocks.stream().filter(q -> Func.isNotEmpty(q.getTransportUnitId())).toList();
		//  获取框架的价格信息
		List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
		if (skuTransportIds.size() > 0) {
			transportUnitEntities = transportUnitService.getIds(skuTransportIds.stream().map(SkuStockEntity::getTransportUnitId).collect(Collectors.toList()));
		}
		//获取仓库的库存信息
		List<SkuWarehouseRelationEntity> skuWarehouseRelationsMy = skuWarehouseRelationMapper.getByWarehouseId(warehouseId, skuIds);
		List<SaleOrderItemEntity> saleOrderItemEntities = new ArrayList<>();
		//判断是不是添加运费
		boolean isDelivery = false;
		Integer deliveryPrice = 0;
		if (Objects.equals(dto.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
			isDelivery = true;
			deliveryPrice = warehouseEntity.getDeliveryFee();
		}
		boolean isProOpen = true; //判断当前购买的所有商品是不是存在营业时间
		for (OrderSaveItemDTO item : dto.getOrderItem()) {
			SaleOrderItemEntity saleOrderItemEntity = new SaleOrderItemEntity();
			saleOrderItemEntity.setWarehouseId(warehouseId);
			//获取对应的Sku信息
			SkuStockEntity skuStock = skuStocks.stream().filter(q -> q.getId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (skuStock == null) {
				throw new ServiceException("商品不存在");
			}
			//判断商品营业时间状态
			//if (!isOpen) {
			skuHoursVerification(warehouseEntity, skuStock, nowTime);
			//}
			//判断是不是加单状态
			if (isAddOrder) {
				if (Func.isEmpty(skuStock.getBusinessStartTime()) || Func.isEmpty(skuStock.getBusinessEndTime())) {
					isProOpen = false;
				}
			}
			saleOrderItemEntity.setProductId(skuStock.getProductId());
			//获取运输单位信息
			if (Func.isNotEmpty(skuStock.getTransportUnitId()) && skuStock.getIsContainBox() == 1) {
				TransportUnitEntity transportUnitEntity = transportUnitEntities.stream().filter(q -> q.getId().equals(skuStock.getTransportUnitId())).findFirst().orElse(null);
				assert transportUnitEntity != null;
				saleOrderItemEntity.setSupportTrans(transportUnitEntity.getTransportName());
				saleOrderItemEntity.setSupportTransPrice(transportUnitEntity.getPrice());
				saleOrderItemEntity.setSupportTransUnitId(transportUnitEntity.getId());
				//计算出需要多少运输单位
				saleOrderItemEntity.setSupportTransNum(item.getProductQuantity() * skuStock.getTransportConversionRate());
			}
			saleOrderItemEntity.setProductQuantity(item.getProductQuantity());
			saleOrderItemEntity.setProductSkuId(item.getProductSkuId());
			//获取商品信息
			ProductEntity productEntity = products.stream().filter(q -> q.getId().equals(skuStock.getProductId())).findFirst().orElse(null);
			if (productEntity == null) {
				throw new ServiceException("商品不存在");
			}
			saleOrderItemEntity.setProductPic(productEntity.getPic());
			saleOrderItemEntity.setProductBrand(productEntity.getBrandName());
			saleOrderItemEntity.setProductUnit(productEntity.getUnitName());
			saleOrderItemEntity.setProductCategoryId(productEntity.getProductCategoryId());
			saleOrderItemEntity.setProductAttr(skuStock.getSpData());
			saleOrderItemEntity.setProductName(productEntity.getName());
			saleOrderItemEntity.setSkuCode(skuStock.getSkuCode());
			saleOrderItemEntity.setPackageGrossConversionRate(skuStock.getPackageGrossConversionRate());
			saleOrderItemEntity.setIsStandard(productEntity.getIsStandard());
			saleOrderItemEntity.setTransportConversionRate(skuStock.getTransportConversionRate());

			Integer price = 0;
			Integer addPrice = 0;
			Integer priceType = 0;
			//是否已经运算过价格
			if (skuWarehouseRelationsMy.size() > 0) {
				SkuWarehouseRelationEntity skuWarehouseRelation = skuWarehouseRelationsMy.stream().filter(q -> q.getSkuId().equals(skuStock.getId())).findFirst().orElse(null);
				if (Func.isNotEmpty(skuWarehouseRelation)) {
					assert skuWarehouseRelation != null;
					addPrice = skuWarehouseRelation.getAddPrice();
					price = skuWarehouseRelation.getPrice();
					priceType = skuWarehouseRelation.getPriceType();
				}
			}
			int serviceFee = 0;
			if (skuStock.getIsServiceFee().equals(IsServiceFeeEnum.YES.getCode()))
				serviceFee = warehouseEntity.getServiceFee();

			Integer priceSalePanic = price;
			Integer priceTypePanic = 1;
			//0-询价公式，1-直接基础价
			if (secKillActivitySkusEntity.getActivityBasePriceType().equals(0)) {
				priceSalePanic = PriceCalculator.getFormulaPriceResult(price, priceType, secKillActivitySkusEntity.getActivityBasePrice(), skuStock.getPackageGrossConversionRate());
			} else if (secKillActivitySkusEntity.getActivityBasePriceType().equals(1)) {
				//当activity_base_price_type为1时activity_base_price的类型（0-按件，1-按斤）
				if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(0)) {
					priceSalePanic = Integer.valueOf(String.valueOf(new BigDecimal(secKillActivitySkusEntity.getActivityBasePrice()).divide(skuStock.getPackageGrossConversionRate(), 0, RoundingMode.HALF_UP)));
				} else if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(1)) {
					priceSalePanic = Integer.valueOf(secKillActivitySkusEntity.getActivityBasePrice());
				}
			} else {
				throw new ServiceException("请检查基础价类型是否正确");
			}
			Integer priceUnit = PriceCalculator.getPrice(priceSalePanic, priceTypePanic, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, 1);
			Integer priceSale = PriceCalculator.getPriceGrossWeight(priceSalePanic, priceTypePanic, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, new BigDecimal(1));
			saleOrderItemEntity.setProductSalePrice(priceSale);
			saleOrderItemEntity.setProductUnitPrice(priceUnit);
			if (isDelivery) {
				int freight = 0;
				freight += PriceCalculator.getDeliveryFee(warehouseEntity.getDeliveryFee(), 0, BigDecimal.valueOf(item.getProductQuantity()), skuStock.getPackageGrossConversionRate());
				saleOrderItemEntity.setDeliveryExpense(freight);
			}
			//仅商品的价格
			//saleOrderItemEntity.setProductPrice(priceUnit * item.getProductQuantity());
			Integer productPrice = getProductPrice(item.getProductQuantity(), skuStock.getPackageGrossConversionRate(), new BigDecimal(priceSale));
			saleOrderItemEntity.setProductPrice(productPrice);
			saleOrderItemEntity.setNote(item.getNote());
			if (skuStock.getIsServiceFee().equals(1))
				saleOrderItemEntity.setServiceExpense(getServerFee(warehouseEntity.getServiceFee(), skuStock.getPackageGrossConversionRate(), item.getProductQuantity()));
			//saleOrderItemEntity.setServiceExpense(warehouseEntity.getServiceFee() * Integer.valueOf(String.valueOf((new BigDecimal(item.getProductQuantity()).multiply(skuStock.getPackageGrossConversionRate())).setScale(0, RoundingMode.UP))));
			saleOrderItemEntities.add(saleOrderItemEntity);
		}

		//订单入库
		SaleOrderEntity saleOrder = saveOrder(dto, custPId, saleOrderItemEntities, receiveAddressEntity, warehouseId, null, null,
			null, deliveryPrice, warehouseEntity.getServiceFee(), isAddOrder, isProOpen, isDelivery, true, custId);

		return saleOrder;
	}

	@Override
	public MyOrderStatusNumVO getMyOrderStatusNum() {
		Long customId = custService.getCustomId();
		return baseMapper.getMyOrderStatusNum(customId);
	}

	// 校验是否达到最低结算金额
	@Override
	public R validateMinimumSettlementAmount(SettlementAmountDTO dto) {
		Long custId = custService.getCustomId();
		CustEntity customer = custService.getById(custId);
		//获取仓库信息
		WarehouseEntity warehouseEntity = warehouseService.getById(customer.getWarehouseId());
		//验证skuIds 是否在营业时间范围内
		//仓库是否在营业时间
		if (dto.getSkuIds().size() > 0) {
			List<SkuStockEntity> skuStocks = skuStockService.listByIds(dto.getSkuIds());
			for (SkuStockEntity skuStock : skuStocks) {
				skuHoursVerification(warehouseEntity, skuStock, LocalTime.now());
			}
		}
		BigDecimal minimumAmount = warehouseService.getMinimumSettlementAmountByUserId();
		if (minimumAmount == null) {
			minimumAmount = BigDecimal.ZERO;
		}
		if (dto.getCurrentAmount().compareTo(minimumAmount) < 0) {
			return R.fail("订单金额未达到最低结算金额：" + minimumAmount);
		}
		return R.success("校验通过");
	}

	@Override
	@Transactional
	public void cancelSave(CancelSaveDTO dto) {
		SaleOrderEntity saleOrder = this.getById(dto.getOrderId());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}
		if (saleOrder.getIsCutOrder().equals(BusinessConstant.IS_CUT_ORDER_YES)) {
			throw new ServiceException("订单已截单");
		}
		if (Objects.equals(saleOrder.getStatus(), OrderEnum.CANCELLED.getCode())) {
			throw new ServiceException("订单已取消");
		}
		if (!Objects.equals(saleOrder.getStatus(), OrderEnum.PURCHASE_IN_PROGRESS.getCode()) && !Objects.equals(saleOrder.getIsCutOrder(), BusinessConstant.IS_CUT_ORDER_YES)) {
			throw new ServiceException("订单状态不正确订单取消失败");
		}
		saleOrder.setStatus(OrderEnum.CANCELLED.getCode());
		saleOrder.setCancelTime(LocalDateTime.now());
		saleOrder.setCancelUserId(AuthUtil.getUserId());
		saleOrder.setReturnReason(dto.getCancelReason());
		//退款逻辑
		//退款处理
		boolean isRefund = false;
		Long orderCancelRecordNo = IdWorker.getId();
		if (saleOrder.getPayStatus().equals(PayStatusEnum.PAID_SUCCESS.getCode())) {
			RefundDifferenceFun(saleOrder, saleOrder.getId(), saleOrder.getOrderNo(), saleOrder.getPayAmount(), 1, orderCancelRecordNo);
		} else if (saleOrder.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
			//判断订单是不是已经唤起了第三方支付
			if (saleOrder.getPayType().equals(PayTypeEnum.MIXED_PAYMENT.getCode()) && saleOrder.getWalletPayAmount() > 0) {
				//判断时间戳
				long timestamp = saleOrder.getPayTimeAt(); // 当前时间戳
				Instant instant = Instant.ofEpochMilli(timestamp).plus(1, ChronoUnit.MINUTES);
				long newTimestamp = instant.toEpochMilli();
				if (GenerateNumberUtil.TimeStamp() > newTimestamp) {
					custWalletService.unfreezeBalance(saleOrder.getCustId(), saleOrder.getWalletPayAmount(), saleOrder.getOrderNo(), saleOrder.getId());
				} else {
					throw new ServiceException("订单已唤起第三方支付，请稍后操作");
				}
			}
			saleOrder.setStatus(OrderEnum.CANCELLED.getCode());
			//更新订单信息
			baseMapper.updateById(saleOrder);
			isRefund = true;
		} else {
			throw new ServiceException("订单支付状态不正确订单取消失败");
		}

		if (saleOrder.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
			//订单详情里面处理
			List<SaleOrderItemEntity> saleOrderItemEntitiesList = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda().eq(SaleOrderItemEntity::getOrderId, dto.getOrderId()));
			for (SaleOrderItemEntity saleOrderItemEntity : saleOrderItemEntitiesList) {
				saleOrderItemEntity.setStatus(OrderEnum.CANCELLED.getCode());
				saleOrderItemService.updateById(saleOrderItemEntity);
			}
			this.updateById(saleOrder);
			cancelSkuStock(saleOrder, saleOrderItemEntitiesList);
		} else if (saleOrder.getPayStatus().equals(PayStatusEnum.PAID_SUCCESS.getCode())) {
			if (saleOrder.getPayType().equals(PayTypeEnum.PLATFORM_QUOTA.getCode())
				|| saleOrder.getPayType().equals(PayTypeEnum.WALLET.getCode())
				|| saleOrder.getPayType().equals(PayTypeEnum.PLATFORM_WALLET.getCode()) || isRefund) {
				//订单详情里面处理
				List<SaleOrderItemEntity> saleOrderItemEntitiesList = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda().eq(SaleOrderItemEntity::getOrderId, dto.getOrderId()));
				for (SaleOrderItemEntity saleOrderItemEntity : saleOrderItemEntitiesList) {
					saleOrderItemEntity.setStatus(OrderEnum.CANCELLED.getCode());
					saleOrderItemService.updateById(saleOrderItemEntity);
				}
				this.updateById(saleOrder);
				cancelSkuStock(saleOrder, saleOrderItemEntitiesList);
			}
		}
	}

//	/**
//	 * 截单更新订单状态
//	 *
//	 * @param orderIds
//	 */
//	@Override
//	public void UpdateCutOrder(List<Long> orderIds) {
//		baseMapper.UpdateCutOrder(orderIds);
//	}

	/**
	 * 退商品
	 *
	 * @param dto
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancelProduct(CancelProductDTO dto) {

		//获取订单信息
		SaleOrderEntity saleOrderEntity = this.getById(dto.getOrderId());
		log.info("【{}】取消商品", saleOrderEntity.getOrderNo());
		//验证订单
		validateOrder(saleOrderEntity);
		// 获取订单商品项
		List<SaleOrderItemEntity> saleOrderItemEntities = saleOrderItemService.list(
			Wrappers.<SaleOrderItemEntity>lambdaQuery()
				.eq(SaleOrderItemEntity::getOrderId, dto.getOrderId())
		);
		if (saleOrderItemEntities.isEmpty()) {
			throw new ServiceException("订单中没有商品");
		}

		List<Long> skuIds = saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getProductSkuId())).map(SaleOrderItemEntity::getProductSkuId).collect(Collectors.toList());
		log.info("订单商品项:{}", skuIds);
		//获取商品总共的数量
		int numOriginal = saleOrderItemEntities.stream().mapToInt(SaleOrderItemEntity::getProductQuantity).sum();
		int num = dto.getOrderItem().stream().mapToInt(OrderSaveItemDTO::getCancelQuantity).sum();
		//商品没有变化
		if (num == 0) return; //取消数量为0
		//取消订单
		if (num == numOriginal) {
			CancelSaveDTO cancelSaveDTO = new CancelSaveDTO();
			cancelSaveDTO.setOrderId(dto.getOrderId());
			cancelSave(cancelSaveDTO);
			return;
		}
		log.info("取消商品数量:{}", num);
		//取消单异常
		if (num > numOriginal) {
			throw new ServiceException("取消数量不能大于订单中商品总数量");
		}
		//判断商品是否全取消还是部分取消
		List<OrderSaveItemDTO> allCancelItem = new ArrayList<>();
		//部分取消
		List<OrderSaveItemDTO> CancelItem = new ArrayList<>();
		//更新的订单商品
		List<SaleOrderItemEntity> updateOrderItem = new ArrayList<>();
		List<SaleOrderItemEntity> ordeCancelItem = new ArrayList<>();
		QueryWrapper<SkuStockEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.in("id", skuIds);
		List<SkuStockEntity> list = skuStockService.list(queryWrapper);
		//删除订单商品
		List<SaleOrderItemEntity> delOrderItem = new ArrayList<>();
		for (OrderSaveItemDTO orderSaveItemDTO : dto.getOrderItem()) {
			SaleOrderItemEntity saleOrderItem = saleOrderItemEntities.stream().filter(q -> q.getProductSkuId().equals(orderSaveItemDTO.getProductSkuId())).findFirst().orElse(null);
			SaleOrderItemEntity ordeCancel = Objects.requireNonNull(BeanUtil.copyProperties(saleOrderItem, SaleOrderItemEntity.class));
			if (Func.isEmpty(saleOrderItem)) {
				throw new ServiceException("订单中没有该商品");
			}
			if (orderSaveItemDTO.getCancelQuantity() == 0) {
				continue;
			} else {
				assert saleOrderItem != null;
				if (Objects.equals(orderSaveItemDTO.getCancelQuantity(), saleOrderItem.getProductQuantity())) {
					allCancelItem.add(orderSaveItemDTO);
					delOrderItem.add(saleOrderItem);
					//saleOrderItemEntities.remove(saleOrderItem);
				} else if (orderSaveItemDTO.getCancelQuantity() > saleOrderItem.getProductQuantity()) {
					throw new ServiceException("订单中商品数据量小于取消里面的数量");
				} else if (orderSaveItemDTO.getCancelQuantity() < saleOrderItem.getProductQuantity()) {
					CancelItem.add(orderSaveItemDTO);
					//运输费用
					if (Objects.equals(saleOrderEntity.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
						int deliInt = saleOrderItem.getDeliveryExpense() / saleOrderItem.getProductQuantity();
						saleOrderItem.setDeliveryExpense(deliInt * (saleOrderItem.getProductQuantity() - orderSaveItemDTO.getCancelQuantity()));
						ordeCancel.setDeliveryExpense(deliInt * orderSaveItemDTO.getCancelQuantity());
					}
					//配套运输品计算
					if (Func.isNotEmpty(saleOrderItem.getSupportTransUnitId())) {
						SkuStockEntity skuStockEntity = list.stream().filter(i -> i.getId().equals(orderSaveItemDTO.getProductSkuId())).findFirst().orElse(null);
						if (Func.isNotEmpty(skuStockEntity)) {
							saleOrderItem.setSupportTransNum(SupportTransUtil.getSupportTransNum(saleOrderItem.getProductQuantity() - orderSaveItemDTO.getCancelQuantity(), new BigDecimal(skuStockEntity.getTransportConversionRate())));
							ordeCancel.setSupportTransNum(SupportTransUtil.getSupportTransNum(orderSaveItemDTO.getCancelQuantity(), new BigDecimal(skuStockEntity.getTransportConversionRate())));
						}
					}
					//处理订单商品
					saleOrderItem.setProductQuantity(saleOrderItem.getProductQuantity() - orderSaveItemDTO.getCancelQuantity());
					ordeCancel.setProductQuantity(orderSaveItemDTO.getCancelQuantity());
					//int i =new BigDecimal(saleOrderItem.getProductSalePrice()). new BigDecimal(saleOrderItem.getProductQuantity())  * saleOrderItem.getPackageGrossConversionRate();
					int i = getProductPrice(saleOrderItem.getProductQuantity(), saleOrderItem.getPackageGrossConversionRate(), new BigDecimal(saleOrderItem.getProductSalePrice()));
					saleOrderItem.setProductPrice(i);
					//ordeCancel.setProductPrice(ordeCancel.getProductQuantity() * ordeCancel.getProductUnitPrice());

					Integer productPrice = getProductPrice(ordeCancel.getProductQuantity(), ordeCancel.getPackageGrossConversionRate(), new BigDecimal(ordeCancel.getProductSalePrice()));
					ordeCancel.setProductPrice(productPrice);
					//服务费用
					if (Func.isNotEmpty(saleOrderItem.getServiceExpense())) {
						saleOrderItem.setServiceExpense(getServerFee(saleOrderEntity.getServiceFee(), saleOrderItem.getPackageGrossConversionRate(), saleOrderItem.getProductQuantity()));
						ordeCancel.setServiceExpense(getServerFee(saleOrderEntity.getServiceFee(), saleOrderItem.getPackageGrossConversionRate(), orderSaveItemDTO.getCancelQuantity()));
					}
					log.info("[{}]取消商品后剩余得商品数据量：{}", saleOrderEntity.getOrderNo(), saleOrderItem.getProductQuantity());
					updateOrderItem.add(saleOrderItem);
					ordeCancelItem.add(ordeCancel);
				}
			}
		}
		log.info("取消商品:{}", delOrderItem);
		log.info("更新的商品:{}", ordeCancelItem);
		//获取更新后的总数量和总价格
		//获取订单中商品的总数量
		//总金额
		AtomicReference<Integer> totalAmount = new AtomicReference<>(0);
		//运费
		AtomicReference<Integer> deliveryAmount = new AtomicReference<>(0);
		//框架费
		AtomicReference<Integer> supportTransAmount = new AtomicReference<>(0);
		delOrderItem.forEach(i -> {
			if (Func.isNotEmpty(i.getSupportTransNum())) {
				totalAmount.updateAndGet(v -> v + i.getProductPrice() + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense())
					+ (i.getSupportTransNum() * i.getSupportTransPrice()));
				supportTransAmount.updateAndGet(v -> v + (i.getSupportTransNum() * i.getSupportTransPrice()));

			} else {
				totalAmount.updateAndGet(v -> v + i.getProductPrice() + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense())
				);
			}
			deliveryAmount.updateAndGet(v -> v + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense()));
		});
		ordeCancelItem.forEach(i -> {
			if (Func.isNotEmpty(i.getSupportTransNum())) {
				totalAmount.updateAndGet(v -> v + i.getProductPrice() + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense())
					+ (i.getSupportTransNum() * i.getSupportTransPrice()));
				supportTransAmount.updateAndGet(v -> v + (i.getSupportTransNum() * i.getSupportTransPrice()));

			} else {
				totalAmount.updateAndGet(v -> v + i.getProductPrice() + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense())
				);
			}
			deliveryAmount.updateAndGet(v -> v + (Func.isEmpty(i.getDeliveryExpense()) ? 0 : i.getDeliveryExpense()));
		});
		//判断与订单上面的总金额情况
		if (saleOrderEntity.getTotalAmount() >= totalAmount.get() && totalAmount.get() > 0) {
			//减少的金额
			boolean isRefund = false;
			Long OrderItemCancelRecordNo = IdWorker.getId();
			//判断是否全部取消
			List<SaleOrderItemCancelRecordEntity> cancelRecordEntities = new ArrayList<>();
			if (CancelItem.size() > 0) {
				CancelItem.forEach(i -> {
					SaleOrderItemEntity saleOrderItem = saleOrderItemEntities.stream().filter(a -> a.getProductSkuId().equals(i.getProductSkuId())).findFirst().orElse(null);
					SaleOrderItemCancelRecordEntity cancelRecordEntity = Objects.requireNonNull(BeanUtil.copyProperties(saleOrderItem, SaleOrderItemCancelRecordEntity.class));
					cancelRecordEntity.setId(null);
					cancelRecordEntity.setSpData(saleOrderItem.getProductAttr());
					cancelRecordEntity.setCancelQuantity(i.getCancelQuantity());
					//cancelRecordEntity.setProductPrice(i.getCancelQuantity() * cancelRecordEntity.getProductUnitPrice());
					Integer productPrice = getProductPrice(i.getProductQuantity(), cancelRecordEntity.getPackageGrossConversionRate(), new BigDecimal(cancelRecordEntity.getProductSalePrice()));
					cancelRecordEntity.setProductPrice(productPrice);
					cancelRecordEntity.setCancelReason(dto.getCancelReason());
					cancelRecordEntity.setCancelTime(LocalDateTime.now());
					cancelRecordEntity.setCancelUserId(AuthUtil.getUserId());
					cancelRecordEntity.setBusinessNo(OrderItemCancelRecordNo);
					cancelRecordEntity.setOrderItemId(saleOrderItem.getId());

					log.info("[{}]取消商品:{}", saleOrderEntity.getOrderNo(), cancelRecordEntity);
					List<SaleOrderItemEntity> saleOrderItemUpdates = updateOrderItem.stream().filter(u -> u.getProductSkuId()
						.equals(i.getProductSkuId())).toList();
					if (saleOrderItemUpdates.size() > 0) {
						SaleOrderItemEntity orderItemEntity = saleOrderItemUpdates.get(0);
						cancelRecordEntity.setDeliveryExpense(orderItemEntity.getDeliveryExpense());
						cancelRecordEntity.setSupportTransNum(orderItemEntity.getSupportTransNum());
						cancelRecordEntity.setServiceExpense(orderItemEntity.getServiceExpense());
						log.info("[{}]取消商品服务费:{}", saleOrderEntity.getOrderNo(), cancelRecordEntity);
					}
					cancelRecordEntities.add(cancelRecordEntity);
				});
			}
			if (allCancelItem.size() > 0) {
				allCancelItem.forEach(i -> {
					SaleOrderItemEntity saleOrderItem = saleOrderItemEntities.stream().filter(a -> a.getProductSkuId().equals(i.getProductSkuId())).findFirst().orElse(null);
					SaleOrderItemCancelRecordEntity cancelRecordEntity = Objects.requireNonNull(BeanUtil.copyProperties(saleOrderItem, SaleOrderItemCancelRecordEntity.class));
					cancelRecordEntity.setId(null);
					cancelRecordEntity.setSpData(saleOrderItem.getProductAttr());
					cancelRecordEntity.setCancelQuantity(i.getCancelQuantity());
//					cancelRecordEntity.setProductPrice(i.getCancelQuantity() * cancelRecordEntity.getProductUnitPrice());
					Integer productPrice = getProductPrice(i.getProductQuantity(), cancelRecordEntity.getPackageGrossConversionRate(), new BigDecimal(cancelRecordEntity.getProductSalePrice()));
					cancelRecordEntity.setProductPrice(productPrice);
					cancelRecordEntity.setCancelReason(dto.getCancelReason());
					cancelRecordEntity.setCancelTime(LocalDateTime.now());
					cancelRecordEntity.setCancelUserId(AuthUtil.getUserId());
					cancelRecordEntity.setBusinessNo(OrderItemCancelRecordNo);
					cancelRecordEntity.setOrderItemId(saleOrderItem.getId());
					cancelRecordEntity.setDeliveryExpense(saleOrderItem.getDeliveryExpense());
					cancelRecordEntity.setServiceExpense(saleOrderItem.getServiceExpense());
					cancelRecordEntities.add(cancelRecordEntity);
				});
			}
			//判断订单金额是不是满足总金额
			Integer deliveryPriceNum = 0;
			if (saleOrderEntity.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
				deliveryPriceNum = saleOrderEntity.getDeliveryExpense() - cancelRecordEntities.stream().mapToInt(SaleOrderItemCancelRecordEntity::getDeliveryExpense).sum();
			}
			Integer totalAmountInt = saleOrderEntity.getTotalAmount();
			validateMinimumAmount(deliveryPriceNum, totalAmountInt - totalAmount.get(), saleOrderEntity.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode()), null);

			log.info("取消商品报错取消表中的数据:{}", cancelRecordEntities);
			if (cancelRecordEntities.size() > 0) {
				saleOrderItemCancelRecordService.saveBatch(cancelRecordEntities);
			}
			if (saleOrderEntity.getPayStatus().equals(PayStatusEnum.PAID_SUCCESS.getCode())) {
				log.info("退款金额:{}", totalAmount.get());
				RefundDifferenceFun(saleOrderEntity, saleOrderEntity.getId(), saleOrderEntity.getOrderNo(), totalAmount.get(), 2, OrderItemCancelRecordNo);
			} else if (saleOrderEntity.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
				isRefund = true;
			} else {
				throw new ServiceException("订单支付状态异常");
			}
			if (saleOrderEntity.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
				log.info("订单待支付退商品");
				//更新订单商品信息
				if (updateOrderItem.size() > 0)
					saleOrderItemService.updateBatchById(updateOrderItem);
				//删除订单商品
				if (delOrderItem.size() > 0)
					saleOrderItemService.removeBatchByIds(delOrderItem);
				//更新库存
				cancelSkuItemStock(saleOrderEntity, cancelRecordEntities);
			} else if (saleOrderEntity.getPayStatus().equals(PayStatusEnum.PAID_SUCCESS.getCode())) {
				log.info("订单待支付退商品{}");
//				if (saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_QUOTA.getCode())
//					|| saleOrderEntity.getPayType().equals(PayTypeEnum.WALLET.getCode())
//					|| saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_WALLET.getCode())) {
				//更新订单商品信息
				if (updateOrderItem.size() > 0)
					saleOrderItemService.updateBatchById(updateOrderItem);
				//删除订单商品
				if (delOrderItem.size() > 0)
					saleOrderItemService.removeBatchByIds(delOrderItem);
				//更新库存
				cancelSkuItemStock(saleOrderEntity, cancelRecordEntities);
			}
//			}

		}
	}

	/**
	 * 更新客户余额
	 *
	 * @param saleOrderEntity
	 */
	private void updateCustAmount(SaleOrderEntity saleOrderEntity) {
		//获取客户信息
		CustEntity custEntity = custService.getById(saleOrderEntity.getCustId());
		if (custEntity == null) {
			throw new ServiceException("客户不存在");
		}
		if (saleOrderEntity.getCreditaPayAmount() != null && saleOrderEntity.getCreditaPayAmount() > 0) {
			//信用额度支付
			custEntity.setFreezeCredit(custEntity.getFreezeCredit() - saleOrderEntity.getCreditaPayAmount());
		}
		if (saleOrderEntity.getWalletPayAmount() != null && saleOrderEntity.getWalletPayAmount() > 0) {
			//钱包支付
			custWalletService.confirmPayment(saleOrderEntity.getCustId(), saleOrderEntity.getChannelPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
		}
		//更新客户信息
		custService.updateById(custEntity);
	}


	@Override
	public IPage<MyOrderListVO> getMyOrderList(Query query, MyOrderListDTO dto) {
		IPage<MyOrderListVO> page = Condition.getPage(query);
		CustEntity customPId = custService.getCustomPId();
		dto.setUserId(customPId.getId());
		//todo 获取客户ID
		//dto.setUserId(1899702448679829506L);
		if (Func.isNotEmpty(dto.getStatus())) {
			dto.setStatusList(Arrays.stream(dto.getStatus().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
		}
		if (Func.isNotEmpty(dto.getPayStatus())) {
			dto.setPayStatusList(Arrays.stream(dto.getPayStatus().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
		}
		dto.setStatusList(AnalysisUtil.ParseCommaSeparatedString(dto.getStatus()));
		dto.setPayStatusList(AnalysisUtil.ParseCommaSeparatedString(dto.getPayStatus()));
		IPage<MyOrderListVO> result = baseMapper.getMyOrderList(page, dto);

		if (result.getRecords().size() > 0) {
			List<Long> ids = result.getRecords().stream().map(MyOrderListVO::getId).collect(Collectors.toList());
			//获取补差金额
			QueryWrapper<SaleOrderDiffEntity> wrapper = new QueryWrapper<>();
			wrapper.in("order_id", ids);
			wrapper.eq("difference_type", DifferenceEnum.ADD_DIFFERENCE.getCode());
			wrapper.eq("difference_status", DifferenceStatusEnum.WAIT.getCode());
			List<SaleOrderDiffEntity> saleOrderDiffEntities = saleOrderDiffService.list(wrapper);

			List<Long> orderIds = result.getRecords().stream().map(MyOrderListVO::getId).collect(Collectors.toList());
			List<SaleOrderItemEntity> orderItemEntities = saleOrderItemService.list(new QueryWrapper<SaleOrderItemEntity>().in("order_id", orderIds));
			result.getRecords().forEach(i -> {
				List<SaleOrderItemEntity> orderItemEntities1 = orderItemEntities.stream().filter(j -> j.getOrderId().equals(i.getId())).toList();
				List<MyOrderItemListVO> orderItemList = new ArrayList<>();
				orderItemEntities1.forEach(j -> {
					MyOrderItemListVO myOrderItemListVO = new MyOrderItemListVO();
					BeanUtils.copyProperties(j, myOrderItemListVO);
					myOrderItemListVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(j.getProductPrice()));
					myOrderItemListVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(j.getProductUnitPrice()));
					myOrderItemListVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(j.getProductSalePrice()));
					myOrderItemListVO.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(j.getSupportTransPrice()));
					myOrderItemListVO.setServiceExpense(CommonUtil.ConvertIntBigDecimal(j.getServiceExpense()));
					myOrderItemListVO.setServiceFee(CommonUtil.ConvertToBigDecimal(i.getServiceFee()));
					orderItemList.add(myOrderItemListVO);
				});
				if (Func.isNotEmpty(orderItemList)) {
					i.setOrderItemList(orderItemList);
				}
				SaleOrderDiffEntity saleOrderDiffEntity = saleOrderDiffEntities.stream().filter(j -> j.getOrderId().equals(i.getId())).findFirst().orElse(null);
				if (Func.isNotEmpty(saleOrderDiffEntity)) {
					assert saleOrderDiffEntity != null;
					i.setMakeUpDiffAmount(CommonUtil.ConvertIntBigDecimal(saleOrderDiffEntity.getDifferenceAmount()));
				}
				i.setProductNum(orderItemEntities1.stream().mapToInt(SaleOrderItemEntity::getProductQuantity).sum());
				i.setProductTypeNum(orderItemEntities1.stream().map(SaleOrderItemEntity::getProductId).distinct().toList().size());
				i.setProductTransNum(orderItemEntities1.stream().filter(q -> Func.isNotEmpty(q.getSupportTransNum())).mapToInt(SaleOrderItemEntity::getSupportTransNum).sum());
				if ((i.getPayStatus().equals(1) || i.getPayStatus().equals(0)) && i.getStatus().equals(0)) {
					i.setStatusName(OrderStatusNameEnum.PAY_IN_PROGRESS.getMessage());
				}
				if (i.getPayStatus().equals(2) && i.getStatus().equals(0)) {
					i.setStatusName(OrderStatusNameEnum.PURCHASE_IN_PROGRESS.getMessage());
				}
				if (i.getStatus().equals(1)) {
					i.setStatusName(OrderStatusNameEnum.PENDING_PICKUP.getMessage());
				}
				if (i.getStatus().equals(2)) {
					i.setStatusName(OrderStatusNameEnum.COMPLETED.getMessage());
				}
				if (i.getStatus().equals(3)) {
					i.setStatusName(OrderStatusNameEnum.PROCESSING.getMessage());
				}
				if (i.getStatus().equals(4)) {
					i.setStatusName(OrderStatusNameEnum.CANCELLED.getMessage());
				}

				//i.setStatusName(DictCache.getValue("ORDER_STATUS",  i.getStatus()));
				i.setDeliveryTypeName(DictCache.getValue(DictEnum.DELIVERY_TYPE, i.getDeliveryType()));
				i.setTotalAmount(CommonUtil.ConvertToBigDecimal(i.getTotalAmount()));
			});
		}

		return result;
	}

	@Override
	public SettlementVO settlement(List<SettlementDTO> dto) {
		SettlementVO settlementVO = new SettlementVO();
		List<OrderProductVO> orderProductVOList = new ArrayList<>();
		//todo 获取客户ID
		//Long customId=1899702448679829506l;
		Long customId = custService.getCustomId();
		Long warehouseId = custService.getWarehouseId(customId);
		//获取仓库信息
		WarehouseEntity warehouse = warehouseService.getById(warehouseId);
		OrderWarehouseVO orderWarehouseVO = Objects.requireNonNull(BeanUtil.copyProperties(warehouse, OrderWarehouseVO.class));
		//获取订单地址
		OrderAddressVO orderAddressVO = GetCutAddress(customId);
		List<Long> skuIdList = dto.stream().map(SettlementDTO::getSkuId).distinct().collect(Collectors.toList());

		//判断当前时间是不是仓库的营业时间范围内
		LocalTime nowTime = LocalTime.now();

		//获取采购车里面的数据
		QueryWrapper<CartItemEntity> wrapperCart = new QueryWrapper<>();
		wrapperCart.eq("custom_id", customId);
		wrapperCart.in("sku_id", skuIdList);
		List<CartItemEntity> cartItemEntities = cartItemMapper.selectList(wrapperCart);
		for (SettlementDTO item : dto) {
			CartItemEntity cartItemEntity = cartItemEntities.stream().filter(i -> i.getSkuId().equals(item.getSkuId())).findFirst().orElse(null);
			if (Func.isNotEmpty(cartItemEntity)) {
				assert cartItemEntity != null;
				item.setQuantity(cartItemEntity.getQuantity());
			}
		}
		//todo 是否删除购物车数据
		//删除购物信息
		//cartItemMapper.deleteByIds(cartItemEntities.stream().map(i -> i.getId()).collect(Collectors.toList()));
		//总运费
		int freight = 0;
		//框的费用
		BigDecimal boxFreight = BigDecimal.ZERO;
		//商品总价
		BigDecimal productTotalPrice = BigDecimal.ZERO;
		//获取订单商品
		List<Long> ids = dto.stream().map(SettlementDTO::getSkuId).distinct().collect(Collectors.toList());
		List<OrderProductVO> orderProductVOS = baseMapper.getSaleOrderProduct(ids, warehouse.getId());
		BigDecimal grossWeight = BigDecimal.ZERO;

		//获取商品中的所有的所有sku信息
		List<Long> skuIds = orderProductVOS.stream().map(OrderProductVO::getProductSkuId).collect(Collectors.toList());
		List<SkuStockEntity> skuStocks = skuStockService.listByIds(skuIds);

		for (OrderProductVO productVO : orderProductVOS) {
			SettlementDTO settlementDTO = dto.stream().filter(f -> f.getSkuId().equals(productVO.getProductSkuId())).findFirst().orElse(null);
			SkuStockEntity skuStockEntity = skuStocks.stream().filter(i -> i.getId().equals(productVO.getProductSkuId())).findFirst().orElse(null);
			if (skuStockEntity == null) {
				throw new ServiceException("商品不存在");
			}
			skuHoursVerification(warehouse, skuStockEntity, nowTime);
			if (Func.isEmpty(settlementDTO)) {

			}
//			if (productVO.getPriceType().equals(1)){
//				assert settlementDTO != null;
//				freight+= PriceCalculator.getDeliveryFee(warehouse.getDeliveryFee() , 1,BigDecimal.valueOf(settlementDTO.getQuantity()) , productVO.getPackageGrossConversionRate());
//			}else {
			assert settlementDTO != null;
			int deliveryFre = PriceCalculator.getDeliveryFee(warehouse.getDeliveryFee(), 0, BigDecimal.valueOf(settlementDTO.getQuantity()), productVO.getPackageGrossConversionRate());
			freight += deliveryFre;
//			}

			//获取毛总
			BigDecimal multiplyWeight = productVO.getPackageGrossConversionRate().multiply(new BigDecimal(settlementDTO.getQuantity()));
			grossWeight = grossWeight.add(multiplyWeight);

			//计算件单价
			Integer price = PriceCalculator.getPrice(productVO.getPrice(), productVO.getPriceType(), productVO.getAddPrice(), productVO.getFormula(), productVO.getPackageGrossConversionRate(), Integer.valueOf(productVO.getServiceFee().toString()), 1);
			//计算单价
			Integer priceGrossWeight = PriceCalculator.getPriceGrossWeight(productVO.getPrice(), productVO.getPriceType(), productVO.getAddPrice(), productVO.getFormula(), productVO.getPackageGrossConversionRate(), Integer.valueOf(productVO.getServiceFee().toString()), new BigDecimal(1));
			productVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(price));
			productVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(priceGrossWeight));

			if (Func.isNotEmpty(productVO.getSupportTransUnitId()) && skuStockEntity.getIsContainBox() == 1) {
				int boxNum = settlementDTO.getQuantity() * productVO.getTransportConversionRate();
				productVO.setSupportTransNum(boxNum);
				//框的价格
				boxFreight = boxFreight.add(BigDecimal.valueOf(boxNum).multiply(CommonUtil.ConvertToBigDecimal(productVO.getSupportTransPrice())));
			}
			productVO.setSupportTransPrice(CommonUtil.ConvertToBigDecimal(productVO.getSupportTransPrice()));
			//商品的价格
			//productVO.setProductPrice(productVO.getProductUnitPrice().multiply(BigDecimal.valueOf(settlementDTO.getQuantity())));

			Integer productPrice = getProductPrice(settlementDTO.getQuantity(), productVO.getPackageGrossConversionRate(), new BigDecimal(priceGrossWeight));
			productVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(productPrice));
			productTotalPrice = productTotalPrice.add(productVO.getProductPrice());
			productVO.setProductQuantity(settlementDTO.getQuantity());
			productVO.setServiceFee(CommonUtil.ConvertToBigDecimal(productVO.getServiceFee()));
			if (productVO.getIsServiceFee().equals(1))
				if (Func.isNotEmpty(warehouse.getServiceFee()) && warehouse.getServiceFee() > 0)
					productVO.setServiceExpense(multiplyWeight.multiply(CommonUtil.ConvertIntBigDecimal(warehouse.getServiceFee())).setScale(2, RoundingMode.UP));
				else
					productVO.setServiceExpense(BigDecimal.ZERO);
			else {
				productVO.setServiceExpense(BigDecimal.ZERO);
				productVO.setServiceFee(BigDecimal.ZERO);
			}


			productVO.setDelivery(new BigDecimal(warehouse.getDeliveryFee()).multiply(grossWeight));

			// 图片处理
			productVO.setProductPic(PicUtil.getPic(productVO.getProductPic()));
		}
		//计算总价
		orderAddressVO.setDistributionCosts(CommonUtil.ConvertIntBigDecimal(freight));
		//不含邮费的总价格Objects.requireNonNull(CommonUtil.ConvertIntBigDecimal(freight))
		settlementVO.setTotalAmount(Objects.requireNonNull(boxFreight).add(productTotalPrice));
		settlementVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(warehouse.getServiceFee()));
		settlementVO.setTotalWeight(grossWeight);
		settlementVO.setDeliveryFee(CommonUtil.ConvertIntBigDecimal(warehouse.getDeliveryFee()));
		settlementVO.setAddress(orderAddressVO);
		settlementVO.setWarehouse(orderWarehouseVO);
		settlementVO.setData(orderProductVOS);
		settlementVO.setTotalDelivery(CommonUtil.ConvertIntBigDecimal(freight));
		//获取复制钱包
		CustEntity customEntity = custService.getCustomPId();
		CustWalletEntity sufficientAmount = custWalletService.isSufficientAmount(customEntity.getId());
		if (sufficientAmount.getBalance() < 0) {
			settlementVO.setTotalAmount(settlementVO.getTotalAmount().add(CommonUtil.ConvertIntBigDecimal(Math.abs(sufficientAmount.getBalance()))));
		}

		return settlementVO;
	}

	@Override
	public TakeCodeVO getTakeCode(TakeCodeDTO dto) {
		TakeCodeVO takeCodeVO = new TakeCodeVO();
		// 构建 JSON 字符串
		String messageStr = String.format("{\"orderId\":\"%s\"}", dto.getOrderId());
		try {
			takeCodeVO.setQrCodeUrl(QRCodeGeneratorUtil.generateQRCodeBase64(messageStr, 350, 350));
		} catch (WriterException e) {
			throw new RuntimeException(e);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		return takeCodeVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public HomepageUnFilledVO getHomepageUnFilled() {
		// 获取当前登录客户ID
		Long custId = custService.getCustomId();

		// 直接查询待补款订单信息
		List<HomepageUnFilledSingleVO> singleVOList = baseMapper.selectUnFilledOrders(custId);

		// 构建返回数据
		HomepageUnFilledVO result = new HomepageUnFilledVO();
		result.setSingleVOS(singleVOList);

		// 计算总待补款金额
		int totalNeedToFillAmount = singleVOList.stream()
			.mapToInt(HomepageUnFilledSingleVO::getNeedToFillAmount)
			.sum();
		result.setNeedToFillAmountAll(totalNeedToFillAmount);

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean homepageFillPay(HomepageUnFilledVO homepageUnFilledVO) {
		if (homepageUnFilledVO == null || homepageUnFilledVO.getSingleVOS() == null || homepageUnFilledVO.getSingleVOS().isEmpty()) {
			throw new ServiceException("支付数据不能为空");
		}

		// TODO: 调用支付接口
//		Integer needToFillAmountAll = homepageUnFilledVO.getNeedToFillAmountAll();
//		if (needToFillAmountAll == null || needToFillAmountAll <= 0) {
//			throw new ServiceException("支付金额不能为空");
//		}

		// 获取所有差异记录ID和订单ID
		List<Long> diffIds = homepageUnFilledVO.getSingleVOS().stream()
			.map(HomepageUnFilledSingleVO::getOrderDiffId)
			.collect(Collectors.toList());

		List<Long> orderIds = homepageUnFilledVO.getSingleVOS().stream()
			.map(HomepageUnFilledSingleVO::getOrderId)
			.collect(Collectors.toList());

		// 获取所有需要更新的订单
		List<SaleOrderEntity> orders = this.listByIds(orderIds);
		if (orders.size() != orderIds.size()) {
			throw new ServiceException("部分订单不存在");
		}

		// 更新订单金额
		Map<Long, Integer> orderPayAmounts = homepageUnFilledVO.getSingleVOS().stream()
			.collect(Collectors.toMap(
				HomepageUnFilledSingleVO::getOrderId,
				HomepageUnFilledSingleVO::getNeedToFillAmount
			));

		List<SaleOrderEntity> updatedOrders = orders.stream()
			.peek(order -> {
				Integer additionalAmount = orderPayAmounts.get(order.getId());
				if (additionalAmount != null) {
					order.setTotalAmount(order.getTotalAmount() + additionalAmount);
					order.setPayAmount(order.getPayAmount() + additionalAmount);
				}
			})
			.collect(Collectors.toList());

		// 批量更新订单
		boolean updateOrdersResult = this.updateBatchById(updatedOrders);
		if (!updateOrdersResult) {
			throw new ServiceException("更新订单金额失败");
		}

		// 批量更新差异记录状态为处理中(2)
		boolean updateDiffResult = saleOrderDiffService.update(
			new LambdaUpdateWrapper<SaleOrderDiffEntity>()
				.in(SaleOrderDiffEntity::getId, diffIds)
				.set(SaleOrderDiffEntity::getDifferenceStatus, 2)
		);

		if (!updateDiffResult) {
			throw new ServiceException("更新差异记录状态失败");
		}

		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public PayResult goPay(GoPayDTO dto) {
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		//获取当前用户的钱包金额
		CustEntity cust = custService.getCustomPId();
		log.info("[{}]第三支付,钱包支付：{},第三方支付：{}", saleOrder.getOrderNo(), saleOrder.getWalletPayAmount(), saleOrder.getChannelPayAmount());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}

//		if ((saleOrder.getPayAmount() - saleOrder.getChannelPayAmount()) != amount) {
//			throw new ServiceException("金额与退款金额不一致");
//		}

		PayContext<SaleOrderEntity> payContext = PayContext.<SaleOrderEntity>builder().build();
		payContext.setOrderNo(saleOrder.getOrderNo());
		payContext.setTotalAmount(saleOrder.getTotalAmount());
		payContext.setCustId(cust.getId());
		payContext.setProductTitle("订单支付");
		payContext.setOpenId(userOauthService.getOpenId());
		payContext.setMchCreateIp("***********");
		payContext.setProductDesc("购买商品信息");
		payContext.setNote("购买商品-支付订单");
		payContext.setExtend(saleOrder);
		log.info("[{}]第三支付或钱包支付，唤起第三方支付", saleOrder.getOrderNo());
		return payEngine.pay(payContext, PaySceneTypeEnum.NORMAL);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void internalPayment(GoPayDTO goPayDTO, Integer code) {
		SaleOrderEntity saleOrder = baseMapper.selectById(goPayDTO.getOrderId());
		CustEntity cust = custService.getCustomPId();
		if (code.equals(PayTypeEnum.PLATFORM_QUOTA.getCode())) {
			log.info("[{}]使用平台额度支付,信用额度金额{}", saleOrder.getOrderNo(), saleOrder.getCreditaPayAmount());
			cust.setFreezeCredit(cust.getFreezeCredit() - saleOrder.getTotalAmount());
			custService.updateById(cust);
			//todo 是否做记录
			if (saleOrder.getCreditaPayAmount() > 0) {
				//添加冻结使用记录
				CustCreditUsageRecordEntity custCredit = new CustCreditUsageRecordEntity();
				custCredit.setCustId(cust.getId());
				custCredit.setDescription("采购使用信用额度");
				custCredit.setUsedAmount(-saleOrder.getCreditaPayAmount());
				custCredit.setBalance(cust.getUseCredit());
				custCredit.setBizType(CreditUsagBizTypeEnum.PAYMENT.getCode());
				custCredit.setOrderCode(saleOrder.getOrderNo());
				custCredit.setBizNo(saleOrder.getId().toString());
				custCredit.setUsageTime(LocalDateTime.now());
				custCredit.setStatus(WalletTradeStatusEnum.SUCCESSFUL.getCode());
				custCreditUsageRecordService.save(custCredit);
				log.info("[{}]添加使用记录", saleOrder.getOrderNo());
			}
			log.info("[{}]信用额度支付成功", saleOrder.getOrderNo());
		} else if (code.equals(PayTypeEnum.WALLET.getCode())) {
			log.info("[{}]使用钱包支付,钱包金额{}", saleOrder.getOrderNo(), saleOrder.getWalletPayAmount());
			CustWalletEntity custWalletEntity = custWalletService.getOne(new QueryWrapper<CustWalletEntity>().eq("cust_id", cust.getId()));
			custWalletEntity.setFreezeBalance(custWalletEntity.getFreezeBalance() - saleOrder.getTotalAmount());
			custWalletService.updateById(custWalletEntity);
			//todo 是否做记录
			//添加交易记录
			CustWalletTradeRecordEntity walletTradeRecord = new CustWalletTradeRecordEntity();
			{
				CustWalletEntity custWallet = custWalletService.getOne(Wrappers.<CustWalletEntity>lambdaQuery().eq(CustWalletEntity::getCustId, cust.getId()));
				walletTradeRecord.setCustId(cust.getId());
				walletTradeRecord.setAmount(-saleOrder.getWalletPayAmount());
				walletTradeRecord.setBizType(WalletTradeBizTypeEnum.PAYMENT.getCode());
				walletTradeRecord.setBalance(custWallet.getBalance());
				walletTradeRecord.setBizNo(saleOrder.getOrderNo());
				walletTradeRecord.setStatus(WalletTradeStatusEnum.SUCCESSFUL.getCode());
				custWalletTradeRecordService.save(walletTradeRecord);        //保存交易记录
				log.info("[{}]添加交易记录", saleOrder.getOrderNo());
			}
			log.info("[{}]钱包支付成功", saleOrder.getOrderNo());
		}
		saleOrder.setPayStatus(PayStatusEnum.PAID_SUCCESS.getCode());
		saleOrder.setPayTime(LocalDateTime.now());

		skuStockService.updateSale(saleOrder.getId());
		baseMapper.updateById(saleOrder);
		if (saleOrder.getOrderType().equals(OrderTypeEnum.FLASH_SALE_ORDER.getCode())) {
			log.info("[{}]秒杀订单调整", saleOrder.getOrderNo());
			QueryWrapper<SaleOrderItemEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("order_id", saleOrder.getId());
			List<SaleOrderItemEntity> list = saleOrderItemService.list(queryWrapper);
			int sum = list.stream().map(i -> i.getProductQuantity()).mapToInt(Integer::intValue).sum();
			//如果是秒杀进行减库存
			secKillActivitySkusMapper.update(new LambdaUpdateWrapper<SecKillActivitySkusEntity>()
				.eq(SecKillActivitySkusEntity::getSessionsId, saleOrder.getSessionsId())
				.eq(SecKillActivitySkusEntity::getSkuId, list.get(0).getProductSkuId())
				.setSql("sale = sale + " + sum));
			log.info("[{}]秒杀订单调整成功", saleOrder.getOrderNo());
		}
		// 推送订阅消息
		messageSenderService.pushMessage(
			MessageTypeEnum.PAY,
			MessageJumpEnum.ORDER,
			AuthUtil.getUserId(),
			String.valueOf(saleOrder.getId()),
			saleOrder.getOrderNo(),
			CommonUtil.ConvertIntBigDecimal(saleOrder.getPayAmount()).toPlainString() + "元",
			DateTimeUtil.formatDateTime(saleOrder.getPayTime()),
			PayTypeEnum.getByCode(code).getMessage(),
			"订单支付成功"
		);
	}

	/**
	 * 退款
	 *
	 * @param dto 退款信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void refund(RefundDTO dto) {
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		CustEntity cust = custService.getCustomPId();
		RefundContext<SaleOrderEntity> context = RefundContext.<SaleOrderEntity>builder()
			.oriOrderNo(saleOrder.getOrderNo())
			.totalAmount(dto.getAmount())
			.custId(cust.getId())
			.note(dto.getMessage())
			.extend(saleOrder)
			.build();
		engine.refund(context, RefundSceneTypeEnum.PRODUCT_REFUND);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CancelOrderDetailsVO cancelOrderDetails(CancelOrderDetailsDTO dto) {
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}

		CancelOrderDetailsVO cancelOrderDetailsVO = Objects.requireNonNull(BeanUtil.copyProperties(saleOrder, CancelOrderDetailsVO.class));

		cancelOrderDetailsVO.setWalletAmountDiff(CommonUtil.ConvertIntBigDecimal(saleOrder.getWalletAmountDiff()));
		cancelOrderDetailsVO.setChannelPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getChannelPayAmount()));
		cancelOrderDetailsVO.setCreditaPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getCreditaPayAmount()));
		cancelOrderDetailsVO.setWalletPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getWalletPayAmount()));
		cancelOrderDetailsVO.setPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getPayAmount()));
		cancelOrderDetailsVO.setTotalAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getTotalAmount()));
		cancelOrderDetailsVO.setDeliveryTypeName(DictCache.getValue("DELIVERY_TYPE", saleOrder.getDeliveryType()));

		List<CancelOrderDetailsItemVO> lists = new ArrayList<>();
		List<SaleOrderItemEntity> saleOrderItemEntities = saleOrderItemService.list(new QueryWrapper<SaleOrderItemEntity>().eq("order_id", dto.getOrderId()));
		if (saleOrderItemEntities.size() > 0) {
			for (SaleOrderItemEntity temp : saleOrderItemEntities) {
				CancelOrderDetailsItemVO cancelOrderDetailsItemVO = Objects.requireNonNull(BeanUtil.copyProperties(temp, CancelOrderDetailsItemVO.class));
				cancelOrderDetailsItemVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(temp.getProductPrice()));
				cancelOrderDetailsItemVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(temp.getProductUnitPrice()));
				cancelOrderDetailsItemVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(temp.getProductSalePrice()));
				cancelOrderDetailsItemVO.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(temp.getSupportTransPrice()));
				cancelOrderDetailsItemVO.setDeliveryExpense(CommonUtil.ConvertIntBigDecimal(temp.getDeliveryExpense()));
				lists.add(cancelOrderDetailsItemVO);
			}
		}
		cancelOrderDetailsVO.setData(lists);
		return cancelOrderDetailsVO;
	}

	@Override
	@Transactional
	public void deleteOrder(CancelOrderDetailsDTO dto) {
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}
		if (!saleOrder.getStatus().equals(OrderEnum.CANCELLED.getCode())) {
			throw new ServiceException("订单不是已取消状态，不能删除");
		}
		saleOrderItemService.remove(new QueryWrapper<SaleOrderItemEntity>().eq("order_id", dto.getOrderId()));
		baseMapper.deleteById(dto.getOrderId());
	}

	@Override
	public OrderDetailsVO orderDetails(OrderDetailsDTO dto) {
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}
		OrderDetailsVO orderDetailsVO = Objects.requireNonNull(BeanUtil.copyProperties(saleOrder, OrderDetailsVO.class));
		orderDetailsVO.setChannelPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getChannelPayAmount()));
		orderDetailsVO.setCreditaPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getCreditaPayAmount()));
		orderDetailsVO.setWalletPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getWalletPayAmount()));
		orderDetailsVO.setPayAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getPayAmount()));
		orderDetailsVO.setTotalAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getTotalAmount()));
		orderDetailsVO.setWalletAmountDiff(CommonUtil.ConvertIntBigDecimal(saleOrder.getWalletAmountDiff()));
		orderDetailsVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(saleOrder.getServiceFee()));
		orderDetailsVO.setDeliveryExpense(CommonUtil.ConvertIntBigDecimal(saleOrder.getDeliveryExpense()));

		orderDetailsVO.setPayStatus(saleOrder.getPayStatus());
		orderDetailsVO.setStatusName(OrderEnum.getNameByCode(saleOrder.getStatus()));
		if (saleOrder.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
			orderDetailsVO.setStatusName(PayStatusEnum.getNameByCode(saleOrder.getPayStatus()));
		}
		CustEntity cust = custService.getById(saleOrder.getCustId());
		if (cust != null) {
			orderDetailsVO.setCustName(cust.getCustName());
		}
		orderDetailsVO.setDeliveryTypeName(DictCache.getValue(DictEnum.DELIVERY_TYPE, saleOrder.getDeliveryType()));
		if (saleOrder.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			if (Func.isNotEmpty(saleOrder.getReceiverRegion())
				&& Func.isNotEmpty(saleOrder.getReceiverCity())
				&& Func.isNotEmpty(saleOrder.getReceiverProvince())) {
				Region region = RegionCache.getByCode(saleOrder.getReceiverRegion());
				Region region1 = RegionCache.getByCode(saleOrder.getReceiverCity());
				Region region2 = RegionCache.getByCode(saleOrder.getReceiverProvince());

				StringBuilder addressBuilder = new StringBuilder();
				if (region != null) {
					addressBuilder.append(region.getName());
				}
				if (region1 != null) {
					addressBuilder.append(region1.getName());
				}
				if (region2 != null) {
					addressBuilder.append(region2.getName());
				}
				if (Func.isNotEmpty(saleOrder.getReceiverDetailAddress())) {
					addressBuilder.append(saleOrder.getReceiverDetailAddress());
				}

				String deliveryAddress = addressBuilder.toString();
				if (Func.isNotEmpty(deliveryAddress)) {
					orderDetailsVO.setReceiverDetailAddress(deliveryAddress);
				}
			}
		} else {
			//获取仓库地址
			WarehouseEntity orderWarehouseVO = warehouseService.getById(saleOrder.getWarehouseId());
			if (orderWarehouseVO != null) {
				orderDetailsVO.setWarehouseIdName(orderWarehouseVO.getWarehouseName());
				String warehouseAddress = "";
				if (Func.isNotEmpty(orderWarehouseVO.getRegionName())) {
					warehouseAddress += orderWarehouseVO.getRegionName();
				}
				if (Func.isNotEmpty(orderWarehouseVO.getAddress())) {
					warehouseAddress += orderWarehouseVO.getAddress();
				}
				if (Func.isNotEmpty(warehouseAddress)) {
					orderDetailsVO.setReceiverDetailAddress(warehouseAddress);
				}
				orderDetailsVO.setBusinessStartTime(orderWarehouseVO.getBusinessStartTime());
				orderDetailsVO.setBusinessEndTime(orderWarehouseVO.getBusinessEndTime());
			}
		}
		List<OrderDetailsItemVO> lists = new ArrayList<>();
		List<SaleOrderItemEntity> saleOrderItemEntities = saleOrderItemService.list(new QueryWrapper<SaleOrderItemEntity>().eq("order_id", dto.getOrderId()));
		if (saleOrderItemEntities.size() > 0) {
			for (SaleOrderItemEntity temp : saleOrderItemEntities) {
				OrderDetailsItemVO orderDetailsItemVO = Objects.requireNonNull(BeanUtil.copyProperties(temp, OrderDetailsItemVO.class));
				orderDetailsItemVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(temp.getProductPrice()));
				orderDetailsItemVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(temp.getProductUnitPrice()));
				orderDetailsItemVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(temp.getProductSalePrice()));
				orderDetailsItemVO.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(temp.getSupportTransPrice()));
				orderDetailsItemVO.setDeliveryExpense(CommonUtil.ConvertIntBigDecimal(temp.getDeliveryExpense()));
				orderDetailsItemVO.setServiceExpense(CommonUtil.ConvertIntBigDecimal(temp.getServiceExpense()));
				orderDetailsItemVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(saleOrder.getServiceFee()));
				if (Func.isNotEmpty(temp.getProductWeight())) {
					orderDetailsItemVO.setProductWeight(CommonUtil.WeightIntBigDecimal(temp.getProductWeight()));
				} else {
					orderDetailsItemVO.setProductWeight(BigDecimal.ZERO);
				}
				lists.add(orderDetailsItemVO);
			}
		}
		List<OrderDetailsAfterSalesVO> afterSalesServiceVOS = new ArrayList<>();
		//获取售后单
		List<OrderAfterSalesServiceEntity> saleAfterSaleEntities = iOrderAfterSalesServiceService.list(new QueryWrapper<OrderAfterSalesServiceEntity>().eq("order_id", dto.getOrderId()));
		if (saleAfterSaleEntities.size() > 0) {
			for (OrderAfterSalesServiceEntity temp : saleAfterSaleEntities) {
				OrderDetailsAfterSalesVO orderAfterSalesServiceVO = Objects.requireNonNull(BeanUtil.copyProperties(temp, OrderDetailsAfterSalesVO.class));
				orderAfterSalesServiceVO.setAmount(CommonUtil.ConvertIntBigDecimal(temp.getAmount()));
				orderAfterSalesServiceVO.setFinanceAmount(CommonUtil.ConvertIntBigDecimal(temp.getFinanceAmount()));
				if (temp.getStatus() == 2 || temp.getStatus() == 3) {
					orderAfterSalesServiceVO.setStatusName(AfterSalesStatusEnum.RETURNED.getMessage());
				} else if (temp.getStatus() == 4) {
					orderAfterSalesServiceVO.setStatusName(AfterSalesStatusEnum.REJECTED.getMessage());
				}else{
					orderAfterSalesServiceVO.setStatusName(AfterSalesStatusEnum.REFUNDED.getMessage());
				}
				afterSalesServiceVOS.add(orderAfterSalesServiceVO);
			}
		}
		//查看充值金额信息
		List<OrderDetailsWalletVO> walletVOList = new ArrayList<>();
		List<CustWalletTradeRecordEntity> recordEntities = custWalletTradeRecordService.list(new QueryWrapper<CustWalletTradeRecordEntity>().eq("biz_no", dto.getOrderId().toString()));
		if (recordEntities.size() > 0) {
			for (CustWalletTradeRecordEntity temp : recordEntities) {
				OrderDetailsWalletVO orderDetailsWalletVO = new OrderDetailsWalletVO();
				orderDetailsWalletVO.setAmount(CommonUtil.ConvertIntBigDecimal(temp.getAmount()));
				orderDetailsWalletVO.setTypeName("上个订单的多退少补");
				walletVOList.add(orderDetailsWalletVO);
			}
			orderDetailsVO.setWalletList(walletVOList);
			Integer totalAmount = walletVOList.stream().map(i -> CommonUtil.WeightBigDecimalInt(i.getAmount())).mapToInt(i -> i).sum();
			orderDetailsVO.setWalletAmount(CommonUtil.ConvertIntBigDecimal(totalAmount));
		}
		orderDetailsVO.setSalesList(afterSalesServiceVOS);
		orderDetailsVO.setItemVOList(lists);
		int sum = lists.stream().mapToInt(i -> CommonUtil.ConvertBigDecimalInt(i.getServiceExpense())).sum();
		orderDetailsVO.setServiceExpense(CommonUtil.ConvertIntBigDecimal(sum));

		//获取以前订单金额和差额
		SaleOrderOriginalEntity saleOrderOriginalEntity = saleOrderOriginalMapper.selectById(saleOrder.getId());
		if (Func.isNotEmpty(saleOrderOriginalEntity)) {
			orderDetailsVO.setTotalAmountOld(CommonUtil.ConvertIntBigDecimal(saleOrderOriginalEntity.getTotalAmount()));
			orderDetailsVO.setAmountDifference(orderDetailsVO.getTotalAmountOld().subtract(orderDetailsVO.getTotalAmount()));
		}
		//退补差
		List<SaleOrderDiffEntity> orderDiff = saleOrderDiffMapper.selectList(new QueryWrapper<SaleOrderDiffEntity>().eq("order_id", dto.getOrderId())).stream().toList();
		//判断订单是否出库之后的数据
		if (saleOrder.getStatus().equals(OrderEnum.PENDING_PICKUP.getCode())||saleOrder.getStatus().equals(OrderEnum.PENDING_RECEIPT.getCode())||
			saleOrder.getStatus().equals(OrderEnum.COMPLETED.getCode())|| saleOrder.getStatus().equals(OrderEnum.PROCESSING.getCode())){

		}else {
			//订单的支付成功元数据
			List<SaleOrderItemSourceEntity> sourceEntities=saleOrderItemSourceService.list(new QueryWrapper<SaleOrderItemSourceEntity>().eq("order_id", dto.getOrderId()));
			if (sourceEntities.size()>0){

			}
		}

		if(orderDiff.size()>0){
			SaleOrderDiffEntity saleOrderDiffEntity = orderDiff.get(0);
			if (saleOrderDiffEntity.getDifferenceType().equals(DifferenceEnum.RETURN_DIFFERENCE.getCode())){
				orderDetailsVO.setDiffName(DifferenceEnum.RETURN_DIFFERENCE.getMessage());
			}else{
				orderDetailsVO.setDiffName(DifferenceEnum.ADD_DIFFERENCE.getMessage());
			}






			if (saleOrderDiffEntity.getDifferenceStatus().equals(DifferenceStatusEnum.SUCCESSFUL.getCode())){
				orderDetailsVO.setIsDiff(true);
			}else {
				orderDetailsVO.setIsDiff(false);
			}
		}
		// 是否可查询差异信息（包含取消、出库明细及查询数据）
		if (OrderEnum.PENDING_PICKUP.getCode().equals(saleOrder.getStatus())
			|| OrderEnum.COMPLETED.getCode().equals(saleOrder.getStatus())
			|| OrderEnum.PENDING_RECEIPT.getCode().equals(saleOrder.getStatus())
		) {
			orderDetailsVO.setIsQueryDiff(true);
			return orderDetailsVO;
		}
		// 查询是否有取消记录
		long countCancel = saleOrderItemCancelRecordService.count(Wrappers.<SaleOrderItemCancelRecordEntity>lambdaQuery().eq(SaleOrderItemCancelRecordEntity::getOrderId, saleOrder.getId()));
		if (countCancel > 0) {
			orderDetailsVO.setIsQueryDiff(true);
		}

		return orderDetailsVO;
	}


	/**
	 * 退差公共方法
	 *
	 * @param orderId 订单ID
	 * @param orderNo 订单编号
	 * @param amount  退差金额
	 * @param type    1:订单 2:商品 3：售后
	 */
	private void RefundDifferenceFun(SaleOrderEntity saleOrderEntity, Long orderId, String orderNo, Integer amount, int type, Long businessId) {
		if (saleOrderEntity == null) {
			QueryWrapper<SaleOrderEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("id", orderId).or().eq("order_no", orderNo);
			saleOrderEntity = this.getOne(queryWrapper);
		}
		// 是否需要推送退款消息
		boolean isPush = true;
		//退款原因
		String reason = "取消订单";
		switch (type) {
			case 1: {
				log.info("[{}]订单退差开始", saleOrderEntity.getOrderNo());
				//判断与订单上面的总金额情况
				if (saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_QUOTA.getCode())) {
					log.info("[{}]信用额度退差：{}", saleOrderEntity.getOrderNo(), amount);
					//减少的金额
					if (amount == null || saleOrderEntity.getCreditaPayAmount() == null || !amount.equals(saleOrderEntity.getCreditaPayAmount())) {
						throw new ServiceException("退差金额与订单金额不一致(信用额度)");
					}
					if (saleOrderEntity.getCreditaPayAmount() != null && saleOrderEntity.getCreditaPayAmount() > 0) {
						//信用额度记录
						custService.unfreezeCredit(saleOrderEntity.getCustId(), saleOrderEntity.getCreditaPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					saleOrderEntity.setStatus(OrderEnum.CANCELLED.getCode());
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_WALLET.getCode())) {
					log.info("[{}]信用额度、钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					if (saleOrderEntity.getCreditaPayAmount() != null && saleOrderEntity.getCreditaPayAmount() > 0) {
						//信用额度记录
						custService.unfreezeCredit(saleOrderEntity.getCustId(), saleOrderEntity.getCreditaPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					if (saleOrderEntity.getWalletPayAmount() != null && saleOrderEntity.getWalletPayAmount() > 0) {
						//钱包余额记录
						custWalletService.unfreezeBalance(saleOrderEntity.getCustId(), saleOrderEntity.getWalletPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					saleOrderEntity.setStatus(OrderEnum.CANCELLED.getCode());
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.WALLET.getCode())) {
					log.info("[{}]钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					if (!Objects.equals(amount, saleOrderEntity.getWalletPayAmount())) {
						throw new ServiceException("退差金额与订单金额不一致(钱包额度)");
					}
					if (saleOrderEntity.getWalletPayAmount() != null && saleOrderEntity.getWalletPayAmount() > 0) {
						//钱包余额记录
						custWalletService.unfreezeBalance(saleOrderEntity.getCustId(), saleOrderEntity.getWalletPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					saleOrderEntity.setStatus(OrderEnum.CANCELLED.getCode());
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else {
					log.info("[{}]第三方支付、钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					int reduceAmount = saleOrderEntity.getTotalAmount() - amount;
					if (saleOrderEntity.getPayType().equals(PayTypeEnum.MIXED_PAYMENT.getCode())) {
						if ((saleOrderEntity.getChannelPayAmount() + saleOrderEntity.getWalletPayAmount()) == amount)
							throw new ServiceException("订单金额与退差金额不符(钱包和第三方支付)");
						reduceAmount = amount - saleOrderEntity.getWalletPayAmount();
					} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.PARTY_PAYMENT.getCode())) {
						if (!saleOrderEntity.getChannelPayAmount().equals(amount))
							throw new ServiceException("订单金额与退差金额不符(第三方支付)");
						reduceAmount = amount;
					}
					if (Objects.equals(saleOrderEntity.getTotalAmount(), amount)) {
						log.info("[{}]第三方支付退差：{}", saleOrderEntity.getOrderNo(), amount);
						if (saleOrderEntity.getChannelPayAmount() != null && saleOrderEntity.getChannelPayAmount() > 0) {
							isPush = false;
							RefundContext<SaleOrderEntity> context = RefundContext.<SaleOrderEntity>builder()
								.mchCreateIp(WebUtil.getIP())
								.businessId(orderId)
								.oriOrderNo(orderNo)
								.totalAmount(reduceAmount)
								.custId(saleOrderEntity.getCustId())
								.note("取消订单退款")
								.extend(saleOrderEntity)
								.build();
							engine.refund(context, RefundSceneTypeEnum.CANCEL);
						}
					} else {
						throw new ServiceException("订单金额与退差金额不符");
					}
				}
				break;
			}
			case 2: {
				reason = "取消商品";
				log.info("[{}]商品退差开始", saleOrderEntity.getOrderNo());
				Integer reduceAmount = 0;
				//判断与订单上面的总金额情况
				if (saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_QUOTA.getCode())) {
					log.info("[{}]信用额度退差：{}", saleOrderEntity.getOrderNo(), amount);
					//减少的金额
					if (amount > saleOrderEntity.getCreditaPayAmount()) {
						throw new ServiceException("退差金额与订单金额不一致(信用额度)");
					}
					if (saleOrderEntity.getCreditaPayAmount() != null && saleOrderEntity.getCreditaPayAmount() > 0) {
						//信用额度记录
						custService.unfreezeCredit(saleOrderEntity.getCustId(), amount, saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					saleOrderEntity.setCreditaPayAmount(saleOrderEntity.getCreditaPayAmount() - amount);
					saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
					saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.PLATFORM_WALLET.getCode())) {
					log.info("[{}]信用额度、钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					Integer reduceAmountC = saleOrderEntity.getWalletPayAmount();
					if (amount <= saleOrderEntity.getWalletPayAmount()) {
						reduceAmountC = amount;
					}
					if (saleOrderEntity.getWalletPayAmount() != null && saleOrderEntity.getWalletPayAmount() > 0) {
						//钱包余额记录
						custWalletService.unfreezeBalance(saleOrderEntity.getCustId(), reduceAmountC, saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					reduceAmount = amount - saleOrderEntity.getWalletPayAmount();
					if (reduceAmount > 0) {
						if (saleOrderEntity.getCreditaPayAmount() != null && saleOrderEntity.getCreditaPayAmount() > 0) {
							//信用额度记录
							custService.unfreezeCredit(saleOrderEntity.getCustId(), reduceAmount, saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
						}
						saleOrderEntity.setCreditaPayAmount(saleOrderEntity.getCreditaPayAmount() - reduceAmount);
					}

					saleOrderEntity.setWalletPayAmount(saleOrderEntity.getWalletPayAmount() - reduceAmountC);
					saleOrderEntity.setCreditaPayAmount(saleOrderEntity.getCreditaPayAmount() - reduceAmount);
					saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
					saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.WALLET.getCode())) {
					log.info("[{}]钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					if (amount > saleOrderEntity.getWalletPayAmount()) {
						throw new ServiceException("退差金额与订单金额不一致(钱包额度)");
					}
					if (saleOrderEntity.getWalletPayAmount() != null && saleOrderEntity.getWalletPayAmount() > 0) {
						//钱包余额记录
						custWalletService.unfreezeBalance(saleOrderEntity.getCustId(), saleOrderEntity.getWalletPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
					}
					saleOrderEntity.setWalletPayAmount(saleOrderEntity.getWalletPayAmount() - amount);
					saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
					saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);

					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.MIXED_PAYMENT.getCode())) {
					log.info("[{}]第三方支付、钱包退差：{}", saleOrderEntity.getOrderNo(), amount);
					if ((saleOrderEntity.getChannelPayAmount() + saleOrderEntity.getWalletPayAmount()) < amount)
						throw new ServiceException("订单金额与退差金额不符(混合支付)");
					if (saleOrderEntity.getWalletPayAmount() >= amount) {
						//钱包余额记录
						custWalletService.unfreezeBalance(saleOrderEntity.getCustId(), saleOrderEntity.getWalletPayAmount(), saleOrderEntity.getOrderNo(), saleOrderEntity.getId());
						saleOrderEntity.setWalletPayAmount(saleOrderEntity.getWalletPayAmount() - amount);
						saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
						saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);
						baseMapper.updateById(saleOrderEntity);
						break;
					} else {
						isPush = false;
						reduceAmount = amount - saleOrderEntity.getWalletPayAmount();
						saleOrderEntity.setWalletPayAmount(0);
						saleOrderEntity.setChannelPayAmount(saleOrderEntity.getChannelPayAmount() - reduceAmount);
						log.info("第三方支付退差：{}", reduceAmount);
						RefundContext<SaleOrderEntity> context = RefundContext.<SaleOrderEntity>builder()
							.mchCreateIp(WebUtil.getIP())
							.businessId(businessId)
							.oriOrderNo(orderNo)
							.totalAmount(reduceAmount)
							.custId(saleOrderEntity.getCustId())
							.note("取消商品退款")
							.extend(saleOrderEntity)
							.build();
						engine.refund(context, RefundSceneTypeEnum.PRODUCT_REFUND);
						saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
						saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);
						//更新订单信息
						baseMapper.updateById(saleOrderEntity);
					}
				} else if (saleOrderEntity.getPayType().equals(PayTypeEnum.PARTY_PAYMENT.getCode())) {
					log.info("[{}]第三方支付退差：{}", saleOrderEntity.getOrderNo(), amount);
					if (saleOrderEntity.getChannelPayAmount() < amount)
						throw new ServiceException("订单金额与退差金额不符(第三方支付)");
					reduceAmount = amount;
					saleOrderEntity.setChannelPayAmount(saleOrderEntity.getChannelPayAmount() - amount);
					saleOrderEntity.setTotalAmount(saleOrderEntity.getTotalAmount() - amount);
					saleOrderEntity.setPayAmount(saleOrderEntity.getPayAmount() - amount);
					if (saleOrderEntity.getTotalAmount() >= amount) {
						if (saleOrderEntity.getChannelPayAmount() != null && saleOrderEntity.getChannelPayAmount() > 0) {
							isPush = false;
							RefundContext<SaleOrderEntity> context = RefundContext.<SaleOrderEntity>builder()
								.mchCreateIp(WebUtil.getIP())
								.businessId(businessId)
								.oriOrderNo(orderNo)
								.totalAmount(reduceAmount)
								.custId(saleOrderEntity.getCustId())
								.note("取消订单退款")
								.extend(saleOrderEntity)
								.build();
							engine.refund(context, RefundSceneTypeEnum.PRODUCT_REFUND);
						}
					}
					//更新订单信息
					baseMapper.updateById(saleOrderEntity);
				} else {
					throw new ServiceException("订单金额与退差金额不符");
				}
				break;
			}
			case 3: {
				break;
			}
			default:
				throw new ServiceException("退差类型错误");

		}

		if (!isPush) {
			return;
		}
		// 推送订阅消息
		messageSenderService.pushMessage(
			MessageTypeEnum.REFUND,
			MessageJumpEnum.ORDER,
			AuthUtil.getUserId(),
			String.valueOf(saleOrderEntity.getId()),
			saleOrderEntity.getOrderNo(),
			CommonUtil.ConvertIntBigDecimal(amount).toPlainString() + "元",
			DateUtil.formatDateTime(new Date()),
			reason,
			"订单退款"
		);
	}

	/**
	 * 获取客户地址
	 *
	 * @param cutId
	 * @return
	 */
	private OrderAddressVO GetCutAddress(Long cutId) {
		OrderAddressVO orderAddressVO = new OrderAddressVO();
		CustReceiveAddressEntity custReceiveAddressEntity = custReceiveAddressService.getDefaultAddress(cutId);
		if (Func.isNotEmpty(custReceiveAddressEntity)) {
			orderAddressVO = Objects.requireNonNull(BeanUtil.copyProperties(custReceiveAddressEntity, OrderAddressVO.class));

		}
		// 如果 regionName 已存在则无需处理
		if (Func.isEmpty(orderAddressVO.getRegionName())) {
			String fullAddress = buildFullAddress(
				orderAddressVO.getRegion(),
				orderAddressVO.getCity(),
				orderAddressVO.getProvince(),
				orderAddressVO.getDetailAddress()
			);
			orderAddressVO.setRegionName(fullAddress);
		} else {
			orderAddressVO.setRegionName(orderAddressVO.getRegionName() + " " + orderAddressVO.getDetailAddress());
		}
		log.info("获取客户地址：{}", orderAddressVO);
		return orderAddressVO;
	}

	/**
	 * 合并地址
	 *
	 * @param regionCode
	 * @param cityCode
	 * @param provinceCode
	 * @param detailAddress
	 * @return
	 */
	private String buildFullAddress(String regionCode, String cityCode, String provinceCode, String detailAddress) {
		StringBuilder address = new StringBuilder();
		Optional.ofNullable(RegionCache.getByCode(regionCode)).ifPresent(r -> address.append(r.getName()));
		Optional.ofNullable(RegionCache.getByCode(cityCode)).ifPresent(r -> address.append(r.getName()));
		Optional.ofNullable(RegionCache.getByCode(provinceCode)).ifPresent(r -> address.append(r.getName()));
		if (Func.isNotEmpty(detailAddress)) {
			address.append(detailAddress);
		}
		log.info("合并地址：{}", address);
		return address.toString();
	}

	/**
	 * 判断是否在活动时间
	 *
	 * @param sessionId 场次ID
	 * @param skuId     商品SKUID
	 * @return
	 */
	private SecKillActivitySkusEntity GetSessionsDuring(Long sessionId, Long skuId, int quantity, Long custId) {
		// 查询活动场次
		SecKillActivitySessionsEntity session = secKillActivitySessionsMapper.selectById(sessionId);
		if (session == null) {
			log.warn("活动场次不存在: sessionId={}", sessionId);
			//throw new ServiceException("活动场次不存在");
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_NOT_EXIST);
		}
		if (session.getStatus() == 0) {
			log.warn("活动已结束: sessionId={}", sessionId);
//			throw new ServiceException("活动已结束");
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_ENDED);
		}
		//判断活动是不是已经结束
		SecKillActivityEntity secKillActivityEntity = secKillActivityMapper.selectById(session.getActivityId());
		if (secKillActivityEntity.getStatus() == 0) {
			log.warn("活动已结束: sessionId={}", sessionId);
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_ENDED);
			//throw new ServiceException(40001,"活动已结束");
		}
		// 验证时间范围
		LocalTime now = LocalTime.now();
		// 使用Redis缓存获取场次时间
		SessionTimeVO sessionTime = secKillActivitySessionsService.getSessionTime(sessionId);
		LocalTime start = LocalTime.parse(sessionTime.getStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
		LocalTime end = LocalTime.parse(sessionTime.getEndTime(), DateTimeFormatter.ofPattern("HH:mm"));

		if (now.isBefore(start) || now.isAfter(end)) {
			log.warn("当前时间不在活动时间内: 当前时间={}, 活动时间=[{}, {}]", now, start, end);
//			throw new ServiceException("活动时间已结束");
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_TIME_EXPIRED);
		}

		// 查询秒杀商品信息
		SecKillActivitySkusEntity skusEntity = secKillActivitySkusMapper.selectOne(new QueryWrapper<SecKillActivitySkusEntity>().lambda()
			.eq(SecKillActivitySkusEntity::getSessionsId, sessionId)
			.eq(SecKillActivitySkusEntity::getSkuId, skuId)
			.eq(SecKillActivitySkusEntity::getStatus, 1));

		if (skusEntity == null) {
			log.warn("未找到对应秒杀商品: sessionId={}, skuId={}", sessionId, skuId);
			//throw new ServiceException("秒杀商品不存在");
			throw new ServiceException(ActivityErrorCodeEnum.SKU_NOT_EXIST);
		}

		// 库存检查
		if (skusEntity.getActivityStock().equals(skusEntity.getSale())) {
			log.info("商品已售罄: skuId={}", skuId);
//			throw new ServiceException("本商品已经售罄");
			throw new ServiceException(ActivityErrorCodeEnum.OUT_OF_STOCK);
		}

		if (skusEntity.getActivityStock() - skusEntity.getSale() < quantity) {
			log.info("库存不足: 需要 {}, 剩余 {}", quantity, skusEntity.getActivityStock() - skusEntity.getSale());
//			throw new ServiceException("您下单的数量大于了购买剩余数量");
			throw new ServiceException(ActivityErrorCodeEnum.STOCK_NOT_ENOUGH);
		}

		// 查询用户历史订单
		List<SaleOrderEntity> orders = baseMapper.selectList(Wrappers.<SaleOrderEntity>lambdaQuery()
			.eq(SaleOrderEntity::getCustId, custId)
			.eq(SaleOrderEntity::getSessionsId, sessionId));

		if (orders.isEmpty()) {
			return skusEntity;
		}

		List<Long> orderIds = orders.stream().map(SaleOrderEntity::getId).collect(Collectors.toList());

		List<SaleOrderItemEntity> items = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>lambdaQuery()
			.in(SaleOrderItemEntity::getOrderId, orderIds)
			.eq(SaleOrderItemEntity::getProductSkuId, skuId));

		if (items.isEmpty()) {
			return skusEntity;
		}

		int totalBought = items.stream().mapToInt(SaleOrderItemEntity::getProductQuantity).sum();

		if (totalBought + quantity > skusEntity.getUserLimit()) {
			log.warn("用户已购买数量+本次购买数量超过限制: 已买={}, 本次={}, 限制={}", totalBought, quantity, skusEntity.getUserLimit());
//			throw new ServiceException("您已经购买了商品上限，不能再购买");
			throw new ServiceException(ActivityErrorCodeEnum.USER_LIMIT_EXCEEDED);
		}

		return skusEntity;
	}

	/**
	 * 取消订单修改库存量
	 *
	 * @param saleOrder             订单
	 * @param saleOrderItemEntities 订单详情商品
	 */
	private void cancelSkuStock(SaleOrderEntity saleOrder, List<SaleOrderItemEntity> saleOrderItemEntities) {
		//获取订单中的所有商品SKU
		List<Long> skuIds = saleOrderItemEntities.stream().map(SaleOrderItemEntity::getProductSkuId).distinct().collect(Collectors.toList());
		//秒杀
		if (saleOrder.getOrderType().equals(OrderTypeEnum.FLASH_SALE_ORDER.getCode())) {
			SaleOrderItemEntity orderItemEntity = saleOrderItemEntities.get(0);
			secKillActivitySkusMapper.update(new LambdaUpdateWrapper<SecKillActivitySkusEntity>()
				.eq(SecKillActivitySkusEntity::getSessionsId, saleOrder.getSessionsId())
				.eq(SecKillActivitySkusEntity::getSkuId, orderItemEntity.getProductSkuId())
				.setSql("sale = sale - " + orderItemEntity.getProductQuantity()));
			log.info("秒杀释放sku的购买数量");
		} else {
			//释放sku的购买数量
			//专采
			if (skuIds.size() > 0) {
				log.info("专采释放sku的购买数量");
				List<SkuPpEntity> ppSku = skuPpService.getPpSku(skuIds);
				if (ppSku.size() > 0) {
					for (SkuPpEntity skuPpEntity : ppSku) {
						if (Func.isNotEmpty(skuPpEntity.getSold()))
							skuPpEntity.setSold(skuPpEntity.getSold() - saleOrderItemEntities.stream().filter(q -> q.getProductSkuId().equals(skuPpEntity.getSkuId())).findFirst().get().getProductQuantity());
						else
							skuPpEntity.setSold(0);
						skuIds.remove(skuPpEntity.getSkuId());
					}
					skuPpService.updateBatchById(ppSku);
				}
			}
			//专供
			if (skuIds.size() > 0) {
				log.info("专供释放sku的购买数量");
				List<SkuOemEntity> oemSku = skuOemService.getOemSku(skuIds, saleOrder.getWarehouseId());
				if (oemSku.size() > 0) {
					for (SkuOemEntity skuOemEntity : oemSku) {
						if (Func.isNotEmpty(skuOemEntity.getSold()))
							skuOemEntity.setSold(skuOemEntity.getSold() - saleOrderItemEntities.stream().filter(q -> q.getProductSkuId().equals(skuOemEntity.getSkuId())).findFirst().get().getProductQuantity());
						else
							skuOemEntity.setSold(0);
						skuIds.remove(skuOemEntity.getSkuId());
					}
					skuOemService.updateBatchById(oemSku);
				}
			}
			//总仓
			if (skuIds.size() > 0) {
				log.info("总仓释放sku的购买数量");
				//获取总仓ID
				Long warehouseId = saleOrder.getWarehouseId();
				WarehouseEntity warehouseEntity = warehouseService.getById(warehouseId);
				if (Objects.equals(warehouseEntity.getWarehouseType(), WarehouseTypeEnum.DIVIDE.getCode())) {
					warehouseId = warehouseEntity.getParentId();
				}
				//获取仓库的库存信息
				List<SkuWarehouseRelationEntity> skuWarehouseRelations = skuWarehouseRelationMapper.getByWarehouseId(warehouseId, skuIds);
				if (skuWarehouseRelations.size() > 0) {
					for (SkuWarehouseRelationEntity skuWarehouseRelationEntity : skuWarehouseRelations) {
						if (Func.isNotEmpty(skuWarehouseRelationEntity.getSold()))
							skuWarehouseRelationEntity.setSold(skuWarehouseRelationEntity.getSold() - saleOrderItemEntities.stream().filter(q -> q.getProductSkuId().equals(skuWarehouseRelationEntity.getSkuId())).findFirst().get().getProductQuantity());
						else
							skuWarehouseRelationEntity.setSold(0);
					}
					skuWarehouseRelationMapper.updateById(skuWarehouseRelations);
				}
			}
		}
		if (saleOrder.getPayStatus().equals(PayStatusEnum.PENDING_PAY.getCode())) {
			messageSenderService.pushMessage(
				MessageTypeEnum.ORDER_STATUS,
				MessageJumpEnum.ORDER,
				AuthUtil.getUserId(),
				String.valueOf(saleOrder.getId()),
				saleOrder.getOrderNo(), customProperties.getMerchantName(),
				DateUtil.formatDateTime(saleOrder.getOrderTime()),
				OrderEnum.CANCELLED.getMessage(),
				"取消订单"
			);
		}

	}

	/**
	 * 取消订单商品修改库存量
	 *
	 * @param saleOrder             订单
	 * @param saleOrderItemEntities 取消订单详情商品
	 */
	private void cancelSkuItemStock(SaleOrderEntity saleOrder, List<SaleOrderItemCancelRecordEntity> saleOrderItemEntities) {
		log.info("取消订单商品修改库存量");
		//获取订单中的所有商品SKU
		List<Long> skuIds = saleOrderItemEntities.stream().map(SaleOrderItemCancelRecordEntity::getProductSkuId).distinct().collect(Collectors.toList());
		//秒杀
		if (saleOrder.getOrderType().equals(OrderTypeEnum.FLASH_SALE_ORDER.getCode())) {
			SaleOrderItemCancelRecordEntity orderItemEntity = saleOrderItemEntities.get(0);
			secKillActivitySkusMapper.update(new LambdaUpdateWrapper<SecKillActivitySkusEntity>()
				.eq(SecKillActivitySkusEntity::getSessionsId, saleOrder.getSessionsId())
				.eq(SecKillActivitySkusEntity::getSkuId, orderItemEntity.getProductSkuId())
				.setSql("sale = sale - " + orderItemEntity.getCancelQuantity()));
			log.info("取消订单商品修改库存量: {}", orderItemEntity.getProductSkuId());
		} else {
			//释放sku的购买数量
			//专采
			if (skuIds.size() > 0) {
				log.info("取消订单商品修改专采库存量: {}", skuIds);
				List<SkuPpEntity> ppSku = skuPpService.getPpSku(skuIds);
				if (ppSku.size() > 0) {
					for (SkuPpEntity skuPpEntity : ppSku) {
						if (Func.isNotEmpty(skuPpEntity.getSold()))
							skuPpEntity.setSold(skuPpEntity.getSold() - saleOrderItemEntities.stream().
								filter(q -> q.getProductSkuId().equals(skuPpEntity.getSkuId())).findFirst().get().getCancelQuantity());
						else
							skuPpEntity.setSold(0);
						skuIds.remove(skuPpEntity.getSkuId());
					}
					skuPpService.updateBatchById(ppSku);
				}
			}
			//专供
			if (skuIds.size() > 0) {
				log.info("取消订单商品修改专供库存量: {}", skuIds);
				List<SkuOemEntity> oemSku = skuOemService.getOemSku(skuIds, saleOrder.getWarehouseId());
				if (oemSku.size() > 0) {
					for (SkuOemEntity skuOemEntity : oemSku) {
						if (Func.isNotEmpty(skuOemEntity.getSold()))
							skuOemEntity.setSold(skuOemEntity.getSold() - saleOrderItemEntities.stream().
								filter(q -> q.getProductSkuId().equals(skuOemEntity.getSkuId())).findFirst().get().getCancelQuantity());
						else
							skuOemEntity.setSold(0);
						skuIds.remove(skuOemEntity.getSkuId());
					}
					skuOemService.updateBatchById(oemSku);
				}
			}
			//总仓
			if (skuIds.size() > 0) {
				log.info("取消订单商品修改总仓库存量: {}", skuIds);
				//获取总仓ID
				Long warehouseId = saleOrder.getWarehouseId();
				WarehouseEntity warehouseEntity = warehouseService.getById(warehouseId);
				if (Objects.equals(warehouseEntity.getWarehouseType(), WarehouseTypeEnum.DIVIDE.getCode())) {
					warehouseId = warehouseEntity.getParentId();
				}
				//获取仓库的库存信息
				List<SkuWarehouseRelationEntity> skuWarehouseRelations = skuWarehouseRelationMapper.getByWarehouseId(warehouseId, skuIds);
				if (skuWarehouseRelations.size() > 0) {
					for (SkuWarehouseRelationEntity skuWarehouseRelationEntity : skuWarehouseRelations) {
						if (Func.isNotEmpty(skuWarehouseRelationEntity.getSold()))
							skuWarehouseRelationEntity.setSold(skuWarehouseRelationEntity.getSold() - saleOrderItemEntities.stream()
								.filter(q -> q.getProductSkuId().equals(skuWarehouseRelationEntity.getSkuId())).findFirst().get().getCancelQuantity());
						else
							skuWarehouseRelationEntity.setSold(0);
					}
					skuWarehouseRelationMapper.updateById(skuWarehouseRelations);
				}
			}
		}
	}

	@Override
	public IPage<CreditRepaymentVO> getCreditRepaymentOrderList(Query query) {
		IPage<CreditRepaymentVO> page = Condition.getPage(query);
		Long userId = custService.getCustomId();
		return baseMapper.getCreditRepaymentOrderList(page, userId);
	}

	@Override
	public RepaymentAmountVO calculateRepaymentAmount(CalculateRepaymentDTO dto) {
		Long userId = custService.getCustomId();
		List<Long> orderIds = null;

		if (dto.getIsSelectAll() != null && !dto.getIsSelectAll()) {
			// 如果不是全选，但没有提供订单ID，则直接返回0
			if (Func.isEmpty(dto.getOrderIds())) {
				return new RepaymentAmountVO(BigDecimal.ZERO);
			}
			orderIds = dto.getOrderIds();
		}

		// 如果是全选，orderIds为null，mapper会查询该用户所有逾期订单
		Integer totalAmountInt = baseMapper.sumCreditRepaymentAmount(userId, orderIds, dto.getIsOutsideCycle());

		BigDecimal totalAmount = CommonUtil.ConvertIntBigDecimal(totalAmountInt);
		return new RepaymentAmountVO(totalAmount);
	}

	/**
	 * 结算还款对接支付接口
	 *
	 * @param dto 订单信息
	 * @return CreditRepaymentPayResultVO
	 */
	@Override
	public CreditRepaymentPayResultVO settleAndRepayWithPayment(CalculateRepaymentDTO dto) {
		Long userId = custService.getCustomId();
		List<Long> orderIds = null;
		if (dto.getIsSelectAll() != null && !dto.getIsSelectAll()) {
			// 如果不是全选，但没有提供订单ID，则直接返回0
			if (Func.isEmpty(dto.getOrderIds())) {
				return null;
			}
			orderIds = dto.getOrderIds();
		}

		// 如果是全选，orderIds为null，mapper会查询该用户所有逾期订单
		List<CreditRepaymentVO> creditRepaymentVOList = baseMapper.getCreditRepaymentOrderAllList(userId, orderIds);
		if (Func.isNotEmpty(creditRepaymentVOList)) {
			PayContext<List<CreditRepaymentVO>> payContext = PayContext.<List<CreditRepaymentVO>>builder().build();
			//生成还款付款单号
			payContext.setOrderNo(GenerateNumberUtil.creditRepaymentCode());
			payContext.setTotalAmount(
				CommonUtil.ConvertBigDecimalInt(
					creditRepaymentVOList.stream()
						.map(CreditRepaymentVO::getCreditaPayAmount)
						.reduce(BigDecimal.ZERO, BigDecimal::add)
				)
			);
			payContext.setCustId(userId);
			payContext.setProductTitle("信用额度订单支付还款");
			payContext.setOpenId(userOauthService.getOpenId());
			payContext.setMchCreateIp(WebUtil.getIP());
			payContext.setProductDesc("信用额度订单支付还款");
			payContext.setNote("信用额度订单支付还款");
			payContext.setExtend(creditRepaymentVOList);
			PayResult payResult = payEngine.pay(payContext, PaySceneTypeEnum.CREDIT_REPAYMENT);

			// 创建并返回扩展的支付结果VO
			CreditRepaymentPayResultVO resultVO = new CreditRepaymentPayResultVO();
			// 复制PayResult的属性
			BeanUtil.copyProperties(payResult, resultVO);
			// 设置新增的属性
			resultVO.setPayAmount(creditRepaymentVOList.stream()
				.map(CreditRepaymentVO::getCreditaPayAmount)
				.reduce(BigDecimal.ZERO, BigDecimal::add));
			resultVO.setPayTime(LocalDateTime.now());

			return resultVO;
		}
		return null;
	}

	@Override
	@Transactional
	public SaleOrderEntity addSavePanicBuyMQ(OrderSaveMQDTO dto) {
//		Long custId = custService.getCustomId();
//		CustEntity custPId = custService.getCustomPId();
		Long warehouseId = 0l;
		//判断当前的订单是不是属于加单
		boolean isAddOrder = false;
		CustEntity customer = custService.getById(dto.getCustId());
		if (customer == null) {
			throw new ServiceException("客户不存在");
		}
		Long custPId = 0l;
		//判断父级是不是存在
		if (Func.isNotEmpty(customer.getParentId()) && !customer.getParentId().equals(0l)) {
			custPId = customer.getParentId();
		} else {
			custPId = customer.getId();
		}
		//判断活动的时间准确性
		if (dto.getOrderItem().size() != 1) {
			throw new ServiceException("请选择商品异常");
		}
		OrderSaveItemDTO saveItemDTO = dto.getOrderItem().get(0);
		SecKillActivitySkusEntity secKillActivitySkusEntity = GetSessionsDuring(dto.getSessionsId(),
			saveItemDTO.getProductSkuId(), saveItemDTO.getProductQuantity(), dto.getCustId());
		if (Func.isEmpty(secKillActivitySkusEntity)) {
			throw new ServiceException(ActivityErrorCodeEnum.ACTIVITY_ENDED);
		}
		warehouseId = customer.getWarehouseId();
		if (customer.getWarehouseId() == null) {
			throw new ServiceException("客户没有选择仓库");
		}
		//获取仓库信息
		WarehouseEntity warehouseEntity = warehouseService.getById(customer.getWarehouseId());
		if (warehouseEntity == null) {
			throw new ServiceException("仓库不存在");
		}
		boolean isOpen = true;//判断当前时间仓库是否在营业时间范围内
		//判断当前时间是不是仓库的营业时间范围内
		LocalTime nowTime = LocalTime.now();
		// 获取子集时间
		LocalTime childEndTime = LocalTime.parse(warehouseEntity.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
		LocalTime childStartTime = LocalTime.parse(warehouseEntity.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
		// 验证子集时间是否在父级时间范围内
		if ((nowTime.isBefore(childStartTime) || nowTime.isAfter(childEndTime))) {
			isOpen = false;
		}
		//获取截单时间
		LocalTime cutTime = LocalTime.parse(warehouseEntity.getCutTime(), DateTimeFormatter.ofPattern("HH:mm"));
		if (nowTime.isAfter(cutTime)) {
			isAddOrder = true;
		}
		//获取商品中的所有的所有sku信息
		List<Long> skuIds = dto.getOrderItem().stream().map(OrderSaveItemDTO::getProductSkuId).collect(Collectors.toList());
		List<SkuStockEntity> skuStocks = skuStockService.listByIds(skuIds);
		//获取商品信息
		List<Long> productIds = skuStocks.stream().map(SkuStockEntity::getProductId).distinct().collect(Collectors.toList());
		List<ProductEntity> products = productMapper.getIds(productIds);
		//判断需要几个运输单位ID 并且不等于空
		List<SkuStockEntity> skuTransportIds = skuStocks.stream().filter(q -> Func.isNotEmpty(q.getTransportUnitId())).toList();
		//  获取框架的价格信息
		List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
		if (skuTransportIds.size() > 0) {
			transportUnitEntities = transportUnitService.getIds(skuTransportIds.stream().map(SkuStockEntity::getTransportUnitId).collect(Collectors.toList()));
		}
		//获取仓库的库存信息
		List<SkuWarehouseRelationEntity> skuWarehouseRelationsMy = skuWarehouseRelationMapper.getByWarehouseId(warehouseId, skuIds);
		List<SaleOrderItemEntity> saleOrderItemEntities = new ArrayList<>();
		//判断是不是添加运费
		boolean isDelivery = false;
		Integer deliveryPrice = 0;
		if (Objects.equals(dto.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
			isDelivery = true;
			deliveryPrice = warehouseEntity.getDeliveryFee();
		}
		boolean isProOpen = true; //判断当前购买的所有商品是不是存在营业时间
		for (OrderSaveItemDTO item : dto.getOrderItem()) {
			SaleOrderItemEntity saleOrderItemEntity = new SaleOrderItemEntity();
			saleOrderItemEntity.setWarehouseId(warehouseId);
			//获取对应的Sku信息
			SkuStockEntity skuStock = skuStocks.stream().filter(q -> q.getId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (skuStock == null) {
				throw new ServiceException("商品不存在");
			}
			//判断商品营业时间状态
			if (!isOpen) {
				if (Func.isNotEmpty(skuStock.getBusinessStartTime()) && Func.isNotEmpty(skuStock.getBusinessEndTime())) {
					LocalTime skuEndTime = LocalTime.parse(skuStock.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
					LocalTime skuStartTime = LocalTime.parse(skuStock.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
					if (nowTime.isBefore(skuStartTime) || nowTime.isAfter(skuEndTime)) {
						throw new ServiceException("当前时间不在该商品的营业时间范围内,不能下单");
					}
				} else {
					throw new ServiceException("商品不在该仓库的营业时间范围内,不能下单");
				}
			}
			//判断是不是加单状态
			if (isAddOrder) {
				if (Func.isEmpty(skuStock.getBusinessStartTime()) || Func.isEmpty(skuStock.getBusinessEndTime())) {
					isProOpen = false;
				}
			}
			saleOrderItemEntity.setProductId(skuStock.getProductId());
			//获取运输单位信息
			if (Func.isNotEmpty(skuStock.getTransportUnitId()) && skuStock.getIsContainBox() == 1) {
				TransportUnitEntity transportUnitEntity = transportUnitEntities.stream().filter(q -> q.getId().equals(skuStock.getTransportUnitId())).findFirst().orElse(null);
				assert transportUnitEntity != null;
				saleOrderItemEntity.setSupportTrans(transportUnitEntity.getTransportName());
				saleOrderItemEntity.setSupportTransPrice(transportUnitEntity.getPrice());
				saleOrderItemEntity.setSupportTransUnitId(transportUnitEntity.getId());
				//计算出需要多少运输单位
				saleOrderItemEntity.setSupportTransNum(item.getProductQuantity() * skuStock.getTransportConversionRate());
			}
			saleOrderItemEntity.setProductQuantity(item.getProductQuantity());
			saleOrderItemEntity.setProductSkuId(item.getProductSkuId());
			//获取商品信息
			ProductEntity productEntity = products.stream().filter(q -> q.getId().equals(skuStock.getProductId())).findFirst().orElse(null);
			if (productEntity == null) {
				throw new ServiceException("商品不存在");
			}
			saleOrderItemEntity.setProductPic(productEntity.getPic());
			saleOrderItemEntity.setProductBrand(productEntity.getBrandName());
			saleOrderItemEntity.setProductUnit(productEntity.getUnitName());
			saleOrderItemEntity.setProductCategoryId(productEntity.getProductCategoryId());
			saleOrderItemEntity.setProductAttr(skuStock.getSpData());
			saleOrderItemEntity.setProductName(productEntity.getName());
			saleOrderItemEntity.setSkuCode(skuStock.getSkuCode());
			saleOrderItemEntity.setPackageGrossConversionRate(skuStock.getPackageGrossConversionRate());
			saleOrderItemEntity.setIsStandard(productEntity.getIsStandard());
			saleOrderItemEntity.setTransportConversionRate(skuStock.getTransportConversionRate());

			Integer price = 0;
			Integer addPrice = 0;
			Integer priceType = 0;
			//是否已经运算过价格
			if (skuWarehouseRelationsMy.size() > 0) {
				SkuWarehouseRelationEntity skuWarehouseRelation = skuWarehouseRelationsMy.stream().filter(q -> q.getSkuId().equals(skuStock.getId())).findFirst().orElse(null);
				if (Func.isNotEmpty(skuWarehouseRelation)) {
					assert skuWarehouseRelation != null;
					addPrice = skuWarehouseRelation.getAddPrice();
					price = skuWarehouseRelation.getPrice();
					priceType = skuWarehouseRelation.getPriceType();
				}
			}
			int serviceFee = 0;
			if (skuStock.getIsServiceFee().equals(IsServiceFeeEnum.YES.getCode()))
				serviceFee = warehouseEntity.getServiceFee();

			Integer priceSalePanic = price;
			Integer priceTypePanic = 1;
			//0-询价公式，1-直接基础价
			if (secKillActivitySkusEntity.getActivityBasePriceType().equals(0)) {
				priceSalePanic = PriceCalculator.getFormulaPriceResult(price, priceType, secKillActivitySkusEntity.getActivityBasePrice(), skuStock.getPackageGrossConversionRate());
			} else if (secKillActivitySkusEntity.getActivityBasePriceType().equals(1)) {
				//当activity_base_price_type为1时activity_base_price的类型（0-按件，1-按斤）
				if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(0)) {
					priceSalePanic = Integer.valueOf(String.valueOf(new BigDecimal(secKillActivitySkusEntity.getActivityBasePrice()).divide(skuStock.getPackageGrossConversionRate(), 0, RoundingMode.HALF_UP)));
				} else if (secKillActivitySkusEntity.getActivityBasePriceNumberType().equals(1)) {
					priceSalePanic = Integer.valueOf(secKillActivitySkusEntity.getActivityBasePrice());
				}
			} else {
				throw new ServiceException("请检查基础价类型是否正确");
			}
			Integer priceUnit = PriceCalculator.getPrice(priceSalePanic, priceTypePanic, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, 1);
			Integer priceSale = PriceCalculator.getPriceGrossWeight(priceSalePanic, priceTypePanic, addPrice, warehouseEntity.getFormula(), skuStock.getPackageGrossConversionRate(), serviceFee, new BigDecimal(1));
			saleOrderItemEntity.setProductSalePrice(priceSale);
			saleOrderItemEntity.setProductUnitPrice(priceUnit);
			if (isDelivery) {
				int freight = 0;
				freight += PriceCalculator.getDeliveryFee(warehouseEntity.getDeliveryFee(), 0, BigDecimal.valueOf(item.getProductQuantity()), skuStock.getPackageGrossConversionRate());
				saleOrderItemEntity.setDeliveryExpense(freight);
			}
			//仅商品的价格
			//saleOrderItemEntity.setProductPrice(priceUnit * item.getProductQuantity());
			Integer productPrice = getProductPrice(item.getProductQuantity(), skuStock.getPackageGrossConversionRate(), new BigDecimal(priceSale));
			saleOrderItemEntity.setProductPrice(productPrice);
			saleOrderItemEntity.setNote(item.getNote());
			if (skuStock.getIsServiceFee().equals(1))
				saleOrderItemEntity.setServiceExpense(getServerFee(warehouseEntity.getServiceFee(), skuStock.getPackageGrossConversionRate(), item.getProductQuantity()));
			//saleOrderItemEntity.setServiceExpense(warehouseEntity.getServiceFee() * Integer.valueOf(String.valueOf((new BigDecimal(item.getProductQuantity()).multiply(skuStock.getPackageGrossConversionRate())).setScale(0, RoundingMode.UP))));
			saleOrderItemEntities.add(saleOrderItemEntity);
		}

		//订单入库
		SaleOrderEntity saleOrder = seckillSaveOrder(dto, custPId, saleOrderItemEntities, warehouseId,
			deliveryPrice, warehouseEntity.getServiceFee(), isAddOrder, isProOpen, isDelivery, true, customer.getBladeUserId(), dto.getCustId());

		return saleOrder;
	}

	/**
	 * 秒杀提交订单
	 *
	 * @param dto
	 * @return
	 */
	@Override
	@Transactional
	public SaleOrderEntity seckillAddSavePanicBuy(SeckillOrderSaveDTO dto) {
		//获取订单
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		if (saleOrder == null) {
			throw new ServiceException("订单不存在");
		}
		//获取订单详情
		List<SaleOrderItemEntity> saleOrderItem = saleOrderItemService.list(new QueryWrapper<SaleOrderItemEntity>()
			.eq("order_id", dto.getOrderId()));
		if (saleOrderItem.size() != dto.getOrderItem().size()) {
			throw new ServiceException("请选择商品异常");
		}
		Long custId = custService.getCustomId();
		CustEntity custPId = custService.getCustomPId();
		Long warehouseId = 0l;
		//判断当前的订单是不是属于加单
		boolean isAddOrder = false;
		CustEntity customer = custService.getById(custId);
		if (customer == null) {
			throw new ServiceException("客户不存在");
		}
		//判断活动的时间准确性
		if (dto.getOrderItem().size() != 1) {
			throw new ServiceException("请选择商品异常");
		}
		warehouseId = customer.getWarehouseId();
		CustReceiveAddressEntity receiveAddressEntity = custReceiveAddressService.getById(dto.getReceiveAddressId());
		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			if (custReceiveAddressService.getById(dto.getReceiveAddressId()) == null) {
				throw new ServiceException("收货地址不存在");
			}
			if (customer.getId() == receiveAddressEntity.getCustId()) {
				throw new ServiceException("收货地址不属于该客户");
			}
		}
		if (customer.getWarehouseId() == null) {
			throw new ServiceException("客户没有选择仓库");
		}
		//获取仓库信息
		WarehouseEntity warehouseEntity = warehouseService.getById(customer.getWarehouseId());
		if (warehouseEntity == null) {
			throw new ServiceException("仓库不存在");
		}
		List<SaleOrderItemEntity> saleOrderItemEntities = new ArrayList<>();
		//判断是不是添加运费
		boolean isDelivery = false;
		Integer deliveryPrice = 0;
		if (Objects.equals(dto.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
			isDelivery = true;
			deliveryPrice = warehouseEntity.getDeliveryFee();
		}
		boolean isProOpen = true; //判断当前购买的所有商品是不是存在营业时间
		for (OrderSaveItemDTO item : dto.getOrderItem()) {
			//获取对应的Sku信息
			SaleOrderItemEntity saleOrderItemEntity = saleOrderItem.stream().filter(q -> q.getProductSkuId().equals(item.getProductSkuId())).findFirst().orElse(null);
			if (saleOrderItemEntity == null) {
				throw new ServiceException("商品不存在");
			}
			if (isDelivery) {
				int freight = 0;
				freight += PriceCalculator.getDeliveryFee(warehouseEntity.getDeliveryFee(), 0, BigDecimal.valueOf(item.getProductQuantity()), saleOrderItemEntity.getPackageGrossConversionRate());
				saleOrderItemEntity.setDeliveryExpense(freight);
			}
			saleOrderItemEntities.add(saleOrderItemEntity);
		}

		Integer deliveryPriceNum = 0;
		//判断总运费
		if (isDelivery) {
			deliveryPriceNum = saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum();
		}
		//判断信用额度
		CalculateRepaymentDTO calculateRepaymentDTO = new CalculateRepaymentDTO();
		calculateRepaymentDTO.setOrderIds(null);
		calculateRepaymentDTO.setIsSelectAll(true);
		calculateRepaymentDTO.setIsOutsideCycle(true);
		RepaymentAmountVO repaymentAmountVO = calculateRepaymentAmount(calculateRepaymentDTO);
		if (Func.isNotEmpty(repaymentAmountVO.getTotalRepaymentAmount()) && repaymentAmountVO.getTotalRepaymentAmount().compareTo(BigDecimal.ZERO) > 0) {
			throw new ServiceException("您的信用额度没有及时归还，请归还之后在使用信用额度");
		}
		log.info("[购买下单]校验信用额度: 客户ID={}, 当前待还款金额={}", custId, repaymentAmountVO.getTotalRepaymentAmount());
		//订单总金额
		int totalAmountInt = getTotalAmount(saleOrderItemEntities, isDelivery);
//		for (SaleOrderItemEntity item : saleOrderItemEntities) {
//			if (Func.isNotEmpty(item.getSupportTransUnitId())) {
//				totalAmountInt += item.getProductPrice() + (item.getSupportTransPrice() * item.getSupportTransNum());
//			} else {
//				totalAmountInt += item.getProductPrice();
//			}
//			if (isDelivery) {
//				totalAmountInt += item.getDeliveryExpense();
//			}
//		}
		log.info("[购买下单]验证最低结算金额: 总金额={}, 是否满足条件={}", totalAmountInt, isDelivery);

		validateMinimumAmount(deliveryPriceNum, totalAmountInt, isDelivery, customer.getBladeUserId());

		CustWalletEntity sufficientAmount = custWalletService.isSufficientAmount(custId);
		if (sufficientAmount.getBalance() < 0) {
			totalAmountInt += Math.abs(sufficientAmount.getBalance());
		}
		log.info("[购买下单]计算订单总金额: 商品总金额={}, 运费总金额={}, 是否使用运费={}", totalAmountInt, deliveryPriceNum, isDelivery);

		// 支付金额
		Integer creditaPayAmount = 0;//信用额度支付
		Integer walletPayAmount = 0;//钱包支付
		Integer channelPayAmount = 0;//渠道支付
		if (Objects.equals(dto.getPayType(), PayTypeEnum.PLATFORM_QUOTA.getCode())) {
			creditaPayAmount = custService.freezeBalance(custId, totalAmountInt, saleOrder.getOrderNo());
		} else if (Objects.equals(dto.getPayType(), PayTypeEnum.WALLET.getCode())) {
			log.info("[购买下单]使用钱包支付: 下单金额={}, 钱包金额={}", totalAmountInt, sufficientAmount.getBalance());
			if (sufficientAmount.getBalance() - totalAmountInt >= 0) {
				walletPayAmount = custWalletService.freezeBalance(custId, totalAmountInt, saleOrder.getOrderNo(), PayTypeEnum.WALLET.getCode());
			} else {
				dto.setPayType(PayTypeEnum.MIXED_PAYMENT.getCode());
				walletPayAmount = custWalletService.freezeBalance(custId, totalAmountInt, saleOrder.getOrderNo(), PayTypeEnum.MIXED_PAYMENT.getCode());
				channelPayAmount = totalAmountInt - sufficientAmount.getBalance();
			}
		} else if (Objects.equals(dto.getPayType(), PayTypeEnum.PARTY_PAYMENT.getCode())) {
			channelPayAmount = totalAmountInt;
			//todo 第三方支付金额
		} else {
			throw new ServiceException("请选择支付方式");
		}
		log.info("[购买下单]保存订单基本信息: 订单号={}, 客户ID={}, 仓库ID={}, 支付类型={}",
			saleOrder.getOrderNo(), custId, warehouseId, dto.getPayType());

		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			log.info("[购买下单]设置收货地址信息: 姓名={}, 手机={}, 地址={} {} {} {}",
				receiveAddressEntity.getName(), receiveAddressEntity.getPhone(),
				receiveAddressEntity.getProvince(), receiveAddressEntity.getCity(),
				receiveAddressEntity.getRegion(), receiveAddressEntity.getDetailAddress());
		}
		// 保存订单
		saleOrder.setTotalAmount(totalAmountInt);
		saleOrder.setPayAmount(totalAmountInt);
		saleOrder.setCreditaPayAmount(creditaPayAmount);
		saleOrder.setWalletPayAmount(walletPayAmount);
		saleOrder.setChannelPayAmount(channelPayAmount);
		saleOrder.setPayType(dto.getPayType());
		saleOrder.setDeliveryType(dto.getDeliveryType());
		if (sufficientAmount.getBalance() < 0) {
			saleOrder.setWalletAmountDiff(Math.abs(sufficientAmount.getBalance()));
		}
		if (dto.getDeliveryType().equals(DeliveryEnum.DELIVERY.getCode())) {
			saleOrder.setReceiverName(receiveAddressEntity.getName());
			saleOrder.setReceiverPhone(receiveAddressEntity.getPhone());
			saleOrder.setReceiverPostCode(receiveAddressEntity.getPostCode());
			saleOrder.setReceiverProvince(receiveAddressEntity.getProvince());
			saleOrder.setReceiverCity(receiveAddressEntity.getCity());
			saleOrder.setReceiverRegion(receiveAddressEntity.getRegion());
			saleOrder.setReceiverDetailAddress(receiveAddressEntity.getDetailAddress());
		} else {
			CustEntity custEntity = custService.getById(custId);
			saleOrder.setReceiverName(custEntity.getCustName());
			saleOrder.setReceiverPhone(custEntity.getPhone());
		}
		saleOrder.setOrderTime(LocalDateTime.now());
		saleOrder.setDeliveryFee(deliveryPrice);
		saleOrder.setNote(dto.getNote());
		//获取所有的运费信息
		if (saleOrderItemEntities.size() > 0) {
			saleOrder.setDeliveryExpense(saleOrderItemEntities.stream().filter(i -> Func.isNotEmpty(i.getDeliveryExpense())).mapToInt(SaleOrderItemEntity::getDeliveryExpense).sum());
		}
		//更新订单信息
		super.updateById(saleOrder);
		//更新订单详情
		saleOrderItemService.updateBatchById(saleOrderItemEntities);
		log.info("[购买下单]订单保存成功: 订单号={}, 订单ID={}", saleOrder.getOrderNo(), saleOrder.getId());
		return saleOrder;
	}

	/***
	 * 秒杀结算
	 * @param dto
	 * @return
	 */
	@Override
	public SettlementVO seckillSettlement(SeckillSettlementDTO dto) {
		SettlementVO settlementVO = new SettlementVO();
		//获取订单
		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		//获取订单详情
		QueryWrapper<SaleOrderItemEntity> wrapper = new QueryWrapper<>();
		wrapper.eq("order_id", dto.getOrderId());
		List<SaleOrderItemEntity> saleOrderItemEntities = saleOrderItemService.list(wrapper);
		//获取仓库信息
		WarehouseEntity warehouse = warehouseService.getById(saleOrder.getWarehouseId());
		int freight = 0;
		//框的费用
		BigDecimal boxFreight = BigDecimal.ZERO;
		BigDecimal grossWeight = BigDecimal.ZERO;
		List<OrderProductVO> orderProductVOS = new ArrayList<>();
		List<Long> skuIds = saleOrderItemEntities.stream().map(i -> i.getProductSkuId()).toList();
		QueryWrapper<SkuWarehouseRelationEntity> wrsku = new QueryWrapper<>();
		wrsku.in("sku_id", skuIds);
		wrsku.eq("warehouse_id", warehouse.getId());
		List<SkuWarehouseRelationEntity> skuWarehouseRelationEntities = skuWarehouseRelationMapper.selectList(wrsku);
		List<SkuStockEntity> skuStockEntities = skuStockService.list(new QueryWrapper<SkuStockEntity>().in("id", skuIds));

		for (SaleOrderItemEntity orderItem : saleOrderItemEntities) {
			//复制商品信息到OrderProductVO
			OrderProductVO productVO = Objects.requireNonNull(BeanUtil.copyProperties(orderItem, OrderProductVO.class));

			Stream<SkuWarehouseRelationEntity> skuWarehouseRelationEntityStream = skuWarehouseRelationEntities.stream().filter(i -> i.getSkuId().equals(orderItem.getProductSkuId()));
			SkuWarehouseRelationEntity skuWarehouseRelationEntity = skuWarehouseRelationEntityStream.findFirst().orElse(null);
			if (skuWarehouseRelationEntity != null) {
				productVO.setAddPrice(skuWarehouseRelationEntity.getAddPrice());
				productVO.setPriceType(skuWarehouseRelationEntity.getPriceType());
				productVO.setPrice(skuWarehouseRelationEntity.getPrice());

			}
			List<SkuStockEntity> stockEntities = skuStockEntities.stream().filter(i -> i.getId().equals(orderItem.getProductSkuId())).toList();
			if (stockEntities.size() > 0) {
				SkuStockEntity skuStockEntity = stockEntities.get(0);
				productVO.setIsServiceFee(skuStockEntity.getIsServiceFee());
			}
			productVO.setFormula(warehouse.getFormula());
			productVO.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(orderItem.getSupportTransPrice()));
			productVO.setProductPrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductPrice()));
			productVO.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductUnitPrice()));
			productVO.setServiceExpense(CommonUtil.ConvertIntBigDecimal(orderItem.getServiceExpense()));
			productVO.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductSalePrice()));
			productVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(saleOrder.getServiceFee()));

			int deliveryFre = PriceCalculator.getDeliveryFee(warehouse.getDeliveryFee(), 0, BigDecimal.valueOf(orderItem.getProductQuantity()), orderItem.getPackageGrossConversionRate());
			freight += deliveryFre;
			//获取毛总
			BigDecimal multiplyWeight = productVO.getPackageGrossConversionRate().multiply(new BigDecimal(orderItem.getProductQuantity()));
			grossWeight = grossWeight.add(multiplyWeight);
			productVO.setDelivery(new BigDecimal(warehouse.getDeliveryFee()).multiply(grossWeight));
			orderProductVOS.add(productVO);
		}
		OrderAddressVO orderAddressVO = GetCutAddress(saleOrder.getCustId());
		OrderWarehouseVO orderWarehouseVO = Objects.requireNonNull(BeanUtil.copyProperties(warehouse, OrderWarehouseVO.class));
		//计算总价
		orderAddressVO.setDistributionCosts(CommonUtil.ConvertIntBigDecimal(freight));
		//不含邮费的总价格Objects.requireNonNull(CommonUtil.ConvertIntBigDecimal(freight))
		settlementVO.setTotalAmount(CommonUtil.ConvertIntBigDecimal(saleOrder.getTotalAmount()));
		settlementVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(warehouse.getServiceFee()));
		settlementVO.setTotalWeight(grossWeight);
		settlementVO.setDeliveryFee(CommonUtil.ConvertIntBigDecimal(warehouse.getDeliveryFee()));
		settlementVO.setAddress(orderAddressVO);
		settlementVO.setWarehouse(orderWarehouseVO);
		settlementVO.setData(orderProductVOS);
		settlementVO.setTotalDelivery(CommonUtil.ConvertIntBigDecimal(freight));

		CustWalletEntity sufficientAmount = custWalletService.isSufficientAmount(saleOrder.getCustId());
		if (sufficientAmount.getBalance() < 0) {
			settlementVO.setTotalAmount(settlementVO.getTotalAmount().add(CommonUtil.ConvertIntBigDecimal(Math.abs(sufficientAmount.getBalance()))));
		}
		return settlementVO;
	}

	@Override
	@Transactional
	public CancelAmountProductVO cancelAmountProduct(CancelAmountProductDTO dto) {
		log.info("取消商品");
		//获取订单信息
		SaleOrderEntity saleOrderEntity = this.getById(dto.getOrderId());
		//验证订单
		validateOrder(saleOrderEntity);
		// 获取订单商品项
		List<SaleOrderItemEntity> saleOrderItemEntities = saleOrderItemService.list(
			Wrappers.<SaleOrderItemEntity>lambdaQuery()
				.eq(SaleOrderItemEntity::getOrderId, dto.getOrderId())
		);
		if (saleOrderItemEntities.isEmpty()) {
			throw new ServiceException("订单中没有商品");
		}

		List<SaleOrderItemEntity> ordeCancelItem = new ArrayList<>();
		for (CancelAmountProductDDTO item : dto.getList()) {
			if (item.getQuantity() == 0) continue;

			SaleOrderItemEntity saleOrderItemEntity = saleOrderItemEntities.stream().filter(i -> i.getProductSkuId().equals(item.getSkuId())).findFirst().orElse(null);
			if (saleOrderItemEntity == null) {
				throw new ServiceException("商品不存在");
			}
			if (item.getQuantity() > saleOrderItemEntity.getProductQuantity()) {
				throw new ServiceException("商品数量超出");
			}
			//运输费用
			SaleOrderItemEntity saleOrderItem = new SaleOrderItemEntity();
			if (Objects.equals(saleOrderEntity.getDeliveryType(), DeliveryEnum.DELIVERY.getCode())) {
				int deliInt = saleOrderItemEntity.getDeliveryExpense() / saleOrderItemEntity.getProductQuantity();
				saleOrderItem.setDeliveryExpense(deliInt * item.getQuantity());
			}
			//配套运输品计算
			if (Func.isNotEmpty(saleOrderItemEntity.getSupportTransUnitId())) {
				saleOrderItem.setSupportTransPrice(saleOrderItemEntity.getSupportTransPrice());
				saleOrderItem.setSupportTransNum(SupportTransUtil.getSupportTransNum(item.getQuantity(), new BigDecimal(saleOrderItemEntity.getTransportConversionRate())));
			}
			//处理订单商品
			saleOrderItem.setProductQuantity(item.getQuantity());
			BigDecimal mpc = CommonUtil.ConvertIntBigDecimal(saleOrderItemEntity.getProductSalePrice()).multiply(new BigDecimal(item.getQuantity()).multiply(saleOrderItemEntity.getPackageGrossConversionRate()));
			saleOrderItem.setProductPrice(CommonUtil.ConvertBigDecimalInt(mpc));
			//服务费用
			if (Func.isNotEmpty(saleOrderItemEntity.getServiceExpense())) {
				int deliInt = saleOrderItemEntity.getServiceExpense() / saleOrderItemEntity.getProductQuantity();
				saleOrderItem.setServiceExpense(deliInt * item.getQuantity());
			}
			ordeCancelItem.add(saleOrderItem);
		}
		Integer totalAmount = 0;
		for (SaleOrderItemEntity item : ordeCancelItem) {
			//商品价格
			totalAmount += item.getProductPrice();
			//运费
			totalAmount += (Func.isEmpty(item.getDeliveryExpense()) ? 0 : item.getDeliveryExpense());
			//配套运输品
			if (Func.isNotEmpty(item.getSupportTransNum())) {
				totalAmount += item.getSupportTransNum() * item.getSupportTransPrice();
			}
		}
		CancelAmountProductVO cancelAmountProductVO = new CancelAmountProductVO();
		cancelAmountProductVO.setTotalAmount(CommonUtil.ConvertIntBigDecimal(saleOrderEntity.getTotalAmount() - totalAmount));
		cancelAmountProductVO.setCancelAmount(CommonUtil.ConvertIntBigDecimal(totalAmount));
		//获取仓库的最低配套费用和最低结算金额
		WarehouseEntity warehouse = warehouseService.getOne(Wrappers.<WarehouseEntity>query().lambda().eq(WarehouseEntity::getId, saleOrderEntity.getWarehouseId()));
		if (Func.isNotEmpty(warehouse)) {
			cancelAmountProductVO.setMinSettlementAmount(warehouse.getMiniSettlementAmount());
			cancelAmountProductVO.setMinDeliveryAmount(warehouse.getMiniDeliveryAmount());
		}

		return cancelAmountProductVO;
	}

	@Override
	public OriginalOrderDetailsVO originalOrderDetails(OrderDetailsDTO dto) {
//		SaleOrderEntity saleOrder = baseMapper.selectById(dto.getOrderId());
		List<SaleOrderItemEntity> item = saleOrderItemService.list(Wrappers.<SaleOrderItemEntity>query().lambda().eq(SaleOrderItemEntity::getOrderId, dto.getOrderId()));

		SaleOrderOriginalEntity saleOrderOriginal = saleOrderOriginalMapper.selectById(dto.getOrderId());
		List<SaleOrderItemOriginalEntity> itemOriginal = saleOrderItemOriginalMapper.selectList(Wrappers.<SaleOrderItemOriginalEntity>query().lambda().eq(SaleOrderItemOriginalEntity::getOrderId, dto.getOrderId()));

		OriginalOrderDetailsVO originalOrderDetailsVO = new OriginalOrderDetailsVO();
		originalOrderDetailsVO.setOrderNo(saleOrderOriginal.getOrderNo());
		originalOrderDetailsVO.setTotalAmount(CommonUtil.ConvertIntBigDecimal(saleOrderOriginal.getTotalAmount()));
		originalOrderDetailsVO.setServiceFee(CommonUtil.ConvertIntBigDecimal(saleOrderOriginal.getServiceFee()));
		List<OrderDetailsOriginalVO> orderDetailsOriginalVOList = new ArrayList<>();
		for (SaleOrderItemOriginalEntity orderItem : itemOriginal) {

			OrderDetailsOriginalVO temp = Objects.requireNonNull(BeanUtil.copyProperties(orderItem, OrderDetailsOriginalVO.class));
			temp.setProductPrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductPrice()));
			temp.setProductUnitPrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductUnitPrice()));
			temp.setProductSalePrice(CommonUtil.ConvertIntBigDecimal(orderItem.getProductSalePrice()));
			temp.setServiceExpense(CommonUtil.ConvertIntBigDecimal(orderItem.getServiceExpense()));
			temp.setDeliveryExpense(CommonUtil.ConvertIntBigDecimal(orderItem.getDeliveryExpense()));
			temp.setProductWeight(CommonUtil.WeightIntBigDecimal(orderItem.getProductWeight()));

			SaleOrderItemEntity saleOrderItemEntity = item.stream().filter(i -> i.getId().equals(orderItem.getId())).findFirst().orElse(null);
			if (saleOrderItemEntity != null) {
				temp.setOutboundNum(saleOrderItemEntity.getProductQuantity());
				temp.setWeight(new BigDecimal(saleOrderItemEntity.getProductQuantity()).multiply(saleOrderItemEntity.getPackageGrossConversionRate()));
				temp.setOutboundWeight(CommonUtil.WeightIntBigDecimal(saleOrderItemEntity.getProductWeight()));

				temp.setDiffQuantity(orderItem.getProductQuantity() - saleOrderItemEntity.getProductQuantity());

				BigDecimal productWeight = new BigDecimal(orderItem.getProductQuantity()).multiply(orderItem.getPackageGrossConversionRate());

				temp.setDiffWeight(productWeight.subtract(CommonUtil.WeightIntBigDecimal(saleOrderItemEntity.getProductWeight())));
				temp.setDifference(CommonUtil.ConvertIntBigDecimal(orderItem.getProductPrice() - saleOrderItemEntity.getProductPrice()));

				if (Func.isNotEmpty(saleOrderItemEntity.getSupportTransUnitId())) {
					temp.setTransQuantity(saleOrderItemEntity.getSupportTransNum());
					temp.setTransDiffQuantity(orderItem.getSupportTransNum() - saleOrderItemEntity.getSupportTransNum());
					temp.setTransDifference(CommonUtil.ConvertIntBigDecimal(temp.getTransDiffQuantity() * saleOrderItemEntity.getSupportTransPrice()));
				}
			}
			orderDetailsOriginalVOList.add(temp);
		}
		originalOrderDetailsVO.setOrderDetails(orderDetailsOriginalVOList);

		return originalOrderDetailsVO;
	}

	/**
	 * 验证订单
	 *
	 * @param saleOrderEntity 订单实体
	 */
	private void validateOrder(SaleOrderEntity saleOrderEntity) {
		if (saleOrderEntity == null) {
			throw new ServiceException("订单不存在");
		}
		log.info("取消商品的订单：{}", saleOrderEntity);
		// 截单后不能取消
		if (BusinessConstant.IS_CUT_ORDER_YES.equals(saleOrderEntity.getIsCutOrder())) {
			throw new ServiceException("订单已截单，无法取消");
		}
		// 检查订单状态是否允许取消
		if (!OrderEnum.PURCHASE_IN_PROGRESS.getCode().equals(saleOrderEntity.getStatus())) {
			throw new ServiceException("当前订单状态不允许取消");
		}
	}

	/**
	 * 判断 最小结算金额，运输费金额
	 *
	 * @param deliveryPrice   运费
	 * @param settlementPrice 最小金额
	 */
	private void validateMinimumAmount(Integer deliveryPrice, Integer settlementPrice, boolean isDelivery, Long userId) {

		ValidateMinimumAmountVO minimumAmountVO = warehouseService.getMinimumAmountByUserId(userId);
		if (minimumAmountVO.getSettlementPrice() == null) {
			minimumAmountVO.setSettlementPrice(0);
		}
		if (settlementPrice.compareTo(minimumAmountVO.getSettlementPrice()) < 0) {
			throw new ServiceException("订单金额未达到最低结算金额：" + CommonUtil.ConvertIntBigDecimal(minimumAmountVO.getSettlementPrice()));
		}
		if (isDelivery) {
			if (minimumAmountVO.getDeliveryPrice() == null) {
				minimumAmountVO.setDeliveryPrice(0);
			}
			if (deliveryPrice.compareTo(minimumAmountVO.getDeliveryPrice()) < 0) {
				throw new ServiceException("订单运输金额未达到最低运输金额：" + CommonUtil.ConvertIntBigDecimal(minimumAmountVO.getDeliveryPrice()));
			}
		}
	}

	/**
	 * 获取商品价格
	 *
	 * @param quantity  数量
	 * @param rate      换算比例
	 * @param priceSale 销售价
	 * @return
	 */
	private Integer getProductPrice(Integer quantity, BigDecimal rate, BigDecimal priceSale) {
		BigDecimal integerW = new BigDecimal(quantity).multiply(rate);
		return Integer.valueOf(String.valueOf((priceSale).multiply(integerW).setScale(0, RoundingMode.UP)));
	}

	/**
	 * sku时间验证
	 *
	 * @param skuStock
	 * @return
	 */
	private void skuHoursVerification(WarehouseEntity warehouse, SkuStockEntity skuStock, LocalTime nowTime) {
		// 获取子集时间
		if (Func.isNotEmpty(skuStock.getBusinessStartTime()) && Func.isNotEmpty(skuStock.getBusinessEndTime()) && skuStock.getIsBusiness() == 1) {
			LocalTime skuEndTime = LocalTime.parse(skuStock.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
			LocalTime skuStartTime = LocalTime.parse(skuStock.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
			log.info("[{}]营业时间验证{}-{}", skuStock.getSkuCode(), skuEndTime, skuStartTime);
			if (nowTime.isBefore(skuStartTime) || nowTime.isAfter(skuEndTime)) {
				throw new ServiceException("当前时间不在该商品的营业时间范围内,不能下单");
			}
		} else {
			if (Func.isNotEmpty(warehouse.getBusinessStartTime()) && Func.isNotEmpty(warehouse.getBusinessEndTime())) {
				LocalTime childEndTime = LocalTime.parse(warehouse.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
				LocalTime childStartTime = LocalTime.parse(warehouse.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
				log.info("[{}]营业时间验证{}-{}", warehouse.getWarehouseName(), childStartTime, childEndTime);
				// 验证子集时间是否在父级时间范围内
				if ((nowTime.isBefore(childStartTime) || nowTime.isAfter(childEndTime))) {
					throw new ServiceException("下单时间必须在档口的营业时间范围内");
				}
			}
		}
	}

	/**
	 * 计算总金额
	 *
	 * @param saleOrderItemEntities
	 * @param isDelivery
	 * @return
	 */
	private Integer getTotalAmount(List<SaleOrderItemEntity> saleOrderItemEntities, boolean isDelivery) {
		int totalAmountInt = 0;
		for (SaleOrderItemEntity item : saleOrderItemEntities) {
			//商品总价
			totalAmountInt += item.getProductPrice();
			//配套运输费用
			if (Func.isNotEmpty(item.getSupportTransUnitId())) {
				totalAmountInt += (item.getSupportTransPrice() * item.getSupportTransNum());
			}
			//运费
			if (isDelivery) {
				totalAmountInt += item.getDeliveryExpense();
			}
			//服务费 包含在商品价格里面了
		}
		return totalAmountInt;
	}

	/**
	 * 获取服务费
	 *
	 * @param serverFee 服务单价
	 * @param gw        毛重比例
	 * @param quantity  数量
	 * @return
	 */
	public Integer getServerFee(Integer serverFee, BigDecimal gw, Integer quantity) {
		//毛重
		BigDecimal gwWeight = gw.multiply(BigDecimal.valueOf(quantity));
		//服务费
		BigDecimal serverFeePrice = BigDecimal.valueOf(serverFee).multiply(gwWeight);

		return serverFeePrice.setScale(0, RoundingMode.HALF_UP).intValueExact();
	}
}
