# 用户操作记录
truncate chy_cust_credit_record;
truncate chy_cust_credit_usage_record;
truncate chy_cust_wallet_trade_record;
truncate chy_cust_withdraw_record;
#--订单
truncate chy_sale_order;
truncate chy_sale_order_item;
truncate chy_sale_order_diff;
#--采购
truncate chy_purchase_order;
truncate chy_purchase_order_item;
truncate chy_purchase_order_sale_item;
#--售后
truncate chy_order_after_sales_service;
truncate chy_order_after_sales_service_item;
truncate chy_order_after_sales_service_item_money;
truncate chy_order_after_sales_service_item_supplier;
#--转运
truncate chy_transport_order;
truncate chy_transport_order_item;
#--订单商品取消记录表
truncate chy_sale_order_item_cancel_record;

#清空表
truncate chy_replenishment_order;
truncate chy_replenishment_order_item;

truncate chy_warehouse_store;
truncate chy_warehouse_store_item;

truncate chy_warehouse_outbound;
truncate chy_warehouse_outbound_item;
truncate chy_warehouse_store_outbound_relation;

truncate chy_inventory;
truncate chy_inventory_support_trans;
truncate chy_inventory_check;
truncate chy_inventory_check_base;
truncate chy_inventory_diff;


truncate chy_warehouse_support_trans;
# 供应商对账单
truncate chy_supplier_reconciliation;
truncate chy_supplier_reconciliation_item;
truncate chy_supplier_reconciliation_record;
truncate chy_supplier_reconciliation_payment;

# 第三方支付(无特殊原因不清理,否则无法退款)
#truncate chy_pay_order;
#truncate chy_pay_order_extension;
#truncate chy_refund_order;
#truncate chy_refund_order_extension;

#差异盘点
truncate chy_warehouse_store_diff;
# 转运费
truncate chy_transport_fee;
truncate chy_transport_fee_item;
# 历史订单数据
truncate chy_sale_order_original;
truncate chy_sale_order_item_original;
