package org.springblade.modules.business.cust.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.Func;
import org.springblade.message.enums.MessageJumpEnum;
import org.springblade.message.enums.MessageReceiveTypeEnum;
import org.springblade.message.enums.MessageTypeEnum;
import org.springblade.message.pojo.dto.MessagePushDTO;
import org.springblade.message.service.RabbitMQMessageSender;
import org.springblade.modules.system.service.IUserOauthService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 统一处理消息发送
 */
@Slf4j
@Service
@AllArgsConstructor
public class MessageSenderService {

	private final IUserOauthService userOauthService;
	private final RabbitMQMessageSender messageSender;

	/**
	 * 推送消息到客户端小程序
	 *
	 * @param messageType 消息类型
	 * @param custId      客户id
	 * @param messageJump 跳转地址
	 * @param businessId  跳转地址主业务id
	 * @param params      参数值
	 */
	@Async("messageExecutor")
	public void pushMessage(MessageTypeEnum messageType, Long custId, MessageJumpEnum messageJump, String businessId, String... params) {
		log.info("[message sender]开始处理客户端[{}]推送消息，参数：{}", messageType.getMessage(), params);
		this.doPushCustMessage(messageType, custId, messageJump, businessId, params);
	}

	/**
	 * 批量推送消息到客户端小程序
	 */
	@Async("messageExecutor")
	public void batchPushCustMessage(MessageTypeEnum messageType, List<Long> custIds, String... params) {
		log.info("[message sender]开始批量处理客户端[{}]推送消息，参数：{}", messageType.getMessage(), params);
		custIds.forEach(custId -> doPushCustMessage(messageType, custId, null, null, params));
	}

	/**
	 * 通用实现-推送消息到客户端小程序
	 *
	 * @param messageType 消息模板类型
	 * @param custId      客户id
	 * @param params      参数
	 */
	private void doPushCustMessage(MessageTypeEnum messageType, Long custId, MessageJumpEnum messageJump, String businessId, String... params) {
		try {
			MessagePushDTO dto = new MessagePushDTO();
			dto.setMsgType(messageType.getCode());
			dto.setJump(Func.notNull(messageJump) ? messageJump.getCode() : null);
			dto.setBusinessId(businessId);
			dto.setOpenId(userOauthService.getOpenIdByCustId(custId));
			dto.setContent(MessageTypeEnum.setParam(messageType, params));
			messageSender.send(dto, MessageReceiveTypeEnum.CUST);
		} catch (Exception e) {
			log.error("[message sender]推送客户端订阅消息失败,参数：{}", params, e);
		}
	}

	/**
	 * 推送消息到供应链端小程序
	 *
	 * @param messageType 消息模板类型
	 * @param custId      客户id
	 * @param params      参数
	 */
	@Async("messageExecutor")
	public void pushSupplyMessage(MessageTypeEnum messageType, Long custId, String... params) {
		log.info("[message sender]开始处理供应链端[{}]推送消息，参数：{}", messageType.getMessage(), params);
		this.doPushSupplyMessage(messageType, custId, params);
	}

	/**
	 * 批量推送消息到供应链端小程序
	 */
	@Async("messageExecutor")
	public void batchPushSupplyMessage(MessageTypeEnum messageType, List<Long> userIds, String... params) {
		log.info("[message sender]开始批量处理供应链端[{}]推送消息，参数：{}", messageType.getMessage(), params);
		userIds.forEach(userId -> doPushSupplyMessage(messageType, userId, params));
	}

	/**
	 * 通用实现-推送消息到供应链端小程序
	 *
	 * @param messageType 消息类型
	 * @param userId      用户id
	 * @param params      参数
	 */
	private void doPushSupplyMessage(MessageTypeEnum messageType, Long userId, String... params) {
		try {
			MessagePushDTO dto = new MessagePushDTO();
			dto.setMsgType(messageType.getCode());
			dto.setOpenId(userOauthService.getOpenIdByUserId(userId));
			dto.setContent(MessageTypeEnum.setParam(messageType, params));
			messageSender.send(dto, MessageReceiveTypeEnum.SUPPLY);
		} catch (Exception e) {
			log.error("[message sender]推送供应链端订阅消息失败,参数：{}", params, e);
		}
	}
}
