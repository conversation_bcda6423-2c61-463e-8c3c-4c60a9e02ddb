/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.warehouse.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.business.warehouse.excel.WarehouseStoreExcel;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseStoreQueryDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseStatisticsQueryDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreEntity;
import org.springblade.modules.business.warehouse.pojo.vo.*;

import java.util.List;

/**
 * 入库记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface WarehouseStoreMapper extends BaseMapper<WarehouseStoreEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page            分页参数
	 * @param queryDTO        查询参数
	 * @param excludeBizTypes
	 * @return List<WarehouseStoreVO>
	 */
	List<WarehouseStorePageVO> selectWarehouseStorePage(IPage page, @Param("ew") WarehouseStoreQueryDTO queryDTO, @Param("excludeBizTypes") List<Integer> excludeBizTypes);
	/**
	 * 根据备货单号分页 自定义分页
	 *
	 * @param page 分页参数
	 * @param storeNo 备货单号
	 * @return IPage<WarehouseStoreVO>
	 */
	IPage<WarehouseScopeStoreNoPageVO> selectByStoreNo(IPage page, @Param("storeNo") String storeNo);
	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<WarehouseStoreExcel>
	 */
	List<WarehouseStoreExcel> exportWarehouseStore(@Param("ew") Wrapper<WarehouseStoreEntity> queryWrapper);

	/**
	 * 查询入库批次记录
	 *
	 * @param warehouseId   仓库id
	 * @param productSkuIds 商品sku id集合
	 */
	List<WarehouseStoreBatchVO> listByWarehouseAndSku(@Param("warehouseId") Long warehouseId, @Param("skuIds") List<Long> productSkuIds);

	/**
	 * 根据入库批次ID和skuID查询入库记录
	 *
	 * @param storeId 入库批次id
	 * @param skuId   skuID
	 */
    WarehouseStoreRecordVO getStoreByStoreIdAndSku(@Param("storeId") Long storeId,@Param("skuId") Long skuId);

	/**
	 * 根据关联ids统计出库数量及重量
	 */
    List<AggQuantityWeightVO> aggQuantityAndWeight(@Param("ids") List<Long> ids);

	/**
	 * 为差异详情页查询入库记录
	 *
	 * @param warehouseId  仓库ID
	 * @param supplierId   供应商ID
	 * @param productSkuId 商品SKU ID
	 * @param startTime    开始时间
	 * @param endTime      结束时间
	 * @return 入库记录列表
	 */
	List<WarehouseStoreRecordVO> selectInboundRecordsForDiffDetail(@Param("warehouseId") Long warehouseId, @Param("supplierId") Long supplierId, @Param("productSkuId") Long productSkuId, @Param("startTime") String startTime, @Param("endTime") String endTime);

	/**
	 * =================================== 入库记录统计 ===================================
	 */

	/**
	 * 按照分区汇总
	 *
	 * @param page     分页参数
	 * @param queryDTO 查询参数
	 * @return List<PartitionSummaryVO>
	 */
	List<PartitionSummaryVO> getPartitionSummary(IPage<PartitionSummaryVO> page, @Param("ew") WarehouseStatisticsQueryDTO queryDTO);

	/**
	 * 按照商品汇总
	 *
	 * @param page     分页参数
	 * @param queryDTO 查询参数
	 * @return List<ProductSummaryVO>
	 */
	List<ProductSummaryVO> getProductSummary(IPage<ProductSummaryVO> page, @Param("ew") WarehouseStatisticsQueryDTO queryDTO);

	/**
	 * 按照供应商汇总
	 *
	 * @param page     分页参数
	 * @param queryDTO 查询参数
	 * @return List<SupplierSummaryVO>
	 */
	List<SupplierSummaryVO> getSupplierSummary(IPage<SupplierSummaryVO> page, @Param("ew") WarehouseStatisticsQueryDTO queryDTO);

	/**
	 * 获取采购员汇总统计
	 */
	List<PurchaserSummaryVO> getPurchaserSummary(IPage<PurchaserSummaryVO> page, @Param("ew") WarehouseStatisticsQueryDTO warehouseStatisticsQueryDTO);

	/**
	 * 获取统计总计
	 */
	TotalSummaryVO getStatisticsTotal(@Param("ew") WarehouseStatisticsQueryDTO queryDTO);

	/**
	 * 获取供应商供货明细统计
	 */
	List<SupplierStatisticsVO> getSupplierDetailSummary(@Param("warehouseId") Long warehouseId, @Param("supplierId") Long supplierId, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("productSkuId") Long productSkuId);
}
