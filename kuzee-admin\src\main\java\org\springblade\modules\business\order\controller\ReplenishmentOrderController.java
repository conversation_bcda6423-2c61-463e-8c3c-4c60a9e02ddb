/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.enums.DeliveryEnum;
import org.springblade.common.pojo.vo.IdVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.service.IReplenishmentOrderService;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 供应商备货表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/replenishmentOrder")
@Tag(name = "供应商备货表", description = "供应商备货表接口")
public class ReplenishmentOrderController extends BladeController {

	private final IReplenishmentOrderService replenishmentOrderService;
	private final OssBuilder ossBuilder;
	/**
	 * 供应商备货详情表 详情
	 */
	@GetMapping("/allocationDetail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "某个商品分配详情", description = "传入采购单id和sku ")
	public R<List<ReplenishmentOrderAllocationVO>> allocationDetail(@Parameter(description = "采购单id", required = true) @RequestParam("purchaseOrderId") Long purchaseOrderId,
																	@Parameter(description = "sku id", required = true) @RequestParam("skuId") Long skuId) {
		return R.data(replenishmentOrderService.listAllocationDetail(purchaseOrderId, skuId));
	}

	/**
	 * 供应商备货表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入replenishmentOrder")
	public R save(@Valid @RequestBody ReplenishmentOrderDTO dto) {
		return R.data(replenishmentOrderService.save(dto));
	}

	/**
	 * 查询供应商备货单
	 */
	@GetMapping("/listBySupplier")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "查询供应商备货单(供应商端)", description  = "传入查询条件")
	public R<IPage<ReplenishmentListSupplierPageVO>> listBySupplier(@Valid ReplenishmentListSupplierPageQueryDTO dto, Query query) {
		IPage<ReplenishmentListSupplierPageVO> pages = replenishmentOrderService.listBySupplier(dto, Condition.getPage(query));
		return R.data(pages);
	}

	@PostMapping("/complete/{orderId}")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "标记完成(供应商端)", description  = "传入备货单id")
	public R complete(@Parameter(description = "备货单id", required = true) @PathVariable("orderId") Long orderId) {
		replenishmentOrderService.complete(orderId);
		return R.success();
	}

//	/**
//	 * 供应商备货表 修改
//	 */
//	@PostMapping("/update")
//	@ApiOperationSupport(order = 5)
//	@Operation(summary = "修改", description  = "传入replenishmentOrder")
//	public R update(@Valid @RequestBody ReplenishmentOrderUpdateDTO dto) {
//		replenishmentOrderService.update(dto);
//		return R.success();
//	}


	/**
	 * 供应商备货表
	 */
	@GetMapping("/replenishmentList")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "供应商备货表", description  = "供应商备货表")
	public R<IPage<ReplenishmentListVO>> replenishmentList(ReplenishmentListDTO dto, Query query) {
		IPage<ReplenishmentListVO> pages = replenishmentOrderService.replenishmentList(dto, query);
		return R.data(pages);
	}


	/**
	 * 供应商备货表详情
	 */
	@GetMapping("/replenishmentDetail")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "供应商备货表详情", description  = "供应商备货表详情")
	public R<List<ReplenishmentDetailVO>> replenishmentDetail(ReplenishmentDetailDTO dto) {
		List<ReplenishmentDetailVO> temp = replenishmentOrderService.replenishmentDetail(dto);
		return R.data(temp);
	}


	/**
	 * 供应商关联备货单列表
	 */
	@GetMapping("/supplier/replenishment")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "供应商关联备货单列表", description = "供应商关联备货单列表")
	public R<List<IdVO>> replenishmentList(
		@Parameter(description = "仓库id", required = true) @RequestParam("warehouseId") Long warehouseId,
		@Parameter(description = "供应商id", required = true) @RequestParam("supplierId") Long supplierId
	) {
		return R.data(replenishmentOrderService.replenishmentListBySupplierId(warehouseId, supplierId));
	}

	/**
	 * 根据采购单查询所有供应商备货单信息
	 */
	@GetMapping("/supplierReplenishment")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "(采`购员小程序-销售采购单)根据采购单id查询所有供应商备货单信息", description = "根据采购单查询所有供应商备货单信息")
	public R<List<SupplierReplenishmentOrderVO>> supplierReplenishment(@Parameter(description = "采购单id", required = true) @RequestParam Long purchaseOrderId) {
		List<SupplierReplenishmentOrderVO> list = replenishmentOrderService.listSupplierReplenishment(purchaseOrderId);
		list.forEach(l -> l.setSupplyWay(DeliveryEnum.getNameByCode(Integer.valueOf(l.getSupplyWay()))));
		return R.data(list);
	}
	@GetMapping("/print/{id}")
	@Operation(summary = "供货单套打PDF", description = "生成备货单PDF")
	public void printReplenishmentOrder(@PathVariable Long id, HttpServletResponse response) {
		byte[] pdf = replenishmentOrderService.generateReplenishmentOrderPdf(id);
		response.setContentType("application/pdf");
		response.setHeader("Content-Disposition", "attachment; filename=replenishment_order_" + id + ".pdf");
		try (ServletOutputStream os = response.getOutputStream()) {
			os.write(pdf);
			os.flush();
		} catch (IOException e) {
			throw new RuntimeException("PDF输出失败", e);
		}
	}

	/**
	 * 供货单打印
	 */
	@GetMapping("/print")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "供货单打印", description = "供货单打印")
	public void print(HttpServletResponse response,ReplenishmentOrderPrintDTO dto ) throws IOException {
		//验证token值
		Claims claims = AuthUtil.getClaims(dto.getToken());
		if (Func.isEmpty(claims)){
			return;
		}
		ByteArrayOutputStream byteArrayOutputStream =replenishmentOrderService.print(dto);
		if (Func.isEmpty(dto.getFileName())){
			dto.setFileName("供货单打印");
		}
		// 对文件名进行 URL 编码
		String encodedFilename = URLEncoder.encode(dto.getFileName(), "UTF-8").replaceAll("\\+", "%20");
		// 设置响应头
		response.setContentType("application/pdf");
		response.setContentLength(byteArrayOutputStream.size());
		response.setHeader("Content-Disposition",
			"inline; filename=\"" + encodedFilename + ".pdf\"; filename*=UTF-8''" + encodedFilename + ".pdf");

		// 输出到响应流
		try (OutputStream outputStream = response.getOutputStream()) {
			outputStream.write(byteArrayOutputStream.toByteArray());
			outputStream.flush();
		}
	}

	/**
	 * 供货单打印
	 */
	@GetMapping("/printURL")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "供货单打印小程序", description = "供货单打印小程序")
	public R printURL(HttpServletResponse response,ReplenishmentOrderPrintDTO dto ) throws IOException {
		//验证token值
		Claims claims = AuthUtil.getClaims(dto.getToken());
		if (Func.isEmpty(claims)){
			return null;
		}

		ByteArrayOutputStream byteArrayOutputStream = replenishmentOrderService.print(dto);
		if (Func.isEmpty(dto.getFileName())) {
			dto.setFileName("供货单打印");
		}

		// 转换为 MultipartFile
		// 设置文件名和后缀
		String originalFilename = Func.isEmpty(dto.getFileName()) ? "供货单打印.pdf" : dto.getFileName() + ".pdf";
		MultipartFile multipartFile = new MockMultipartFile(
			"file",
			originalFilename,
			"application/pdf",
			new ByteArrayInputStream(byteArrayOutputStream.toByteArray())
		);
		BladeFile bladeFile = ossBuilder.template().putFile(originalFilename,  multipartFile);

		return R.data(bladeFile);
	}

	/**
	 * 获取时间段所有的供货单明细
	 */
	@GetMapping("/listByTime")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "获取时间段所有的供货单(供应商供货入库查询)", description = "获取时间段所有的供货单")
	public R<List<ReplenishmentOrderItemByTimeVO>> listByTime(
		@Parameter(description = "仓库id", required = true) @RequestParam Long warehouseId,
		@Parameter(description = "开始时间(yyyy-MM-dd)", required = true) @RequestParam String startTime,
		@Parameter(description = "结束时间(yyyy-MM-dd)", required = true) @RequestParam String endTime
	) {
		return R.data(replenishmentOrderService.listByTime(warehouseId, startTime, endTime));
	}

	/**
	 * 供应商供货入库单
	 */
	@GetMapping("/printReplenishmentOrder")
	@ApiOperationSupport(order = 111)
	@Operation(summary = "供应商供货入库单", description = "供应商供货入库单")
	public void printReplenishmentOrder(HttpServletResponse response, PrintReplenishmentOrderDTO dto ) throws IOException {
		//验证token值
		Claims claims = AuthUtil.getClaims(dto.getToken());
		if (Func.isEmpty(claims)){
			return;
		}
		//获取到PDF文件流

		ByteArrayOutputStream byteArrayOutputStream =replenishmentOrderService.printReplenishmentOrder(dto);
		if (Func.isEmpty(dto.getFileName())){
			dto.setFileName("供应商供货入库单");
		}
		// 对文件名进行 URL 编码
		String encodedFilename = URLEncoder.encode(dto.getFileName(), "UTF-8").replaceAll("\\+", "%20");
		// 设置响应头
		response.setContentType("application/pdf");
		response.setContentLength(byteArrayOutputStream.size());
		response.setHeader("Content-Disposition",
			"inline; filename=\"" + encodedFilename + ".pdf\"; filename*=UTF-8''" + encodedFilename + ".pdf");

		// 输出到响应流
		try (OutputStream outputStream = response.getOutputStream()) {
			outputStream.write(byteArrayOutputStream.toByteArray());
			outputStream.flush();
		}

	}
}
