/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.pojo.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 采购单主表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_purchase_order")
@Schema(description = "PurchaseOrderEntity对象")
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;
	/**
	 * 批次号
	 */
	@Schema(description = "批次号")
	private String batchNo;
	/**
	 * 采购单号
	 */
	@Schema(description = "采购单号")
	private String purchaseNo;
//	/**
//	 * 批次号
//	 */
//	@Schema(description = "批次号")
//	private String batchNo;
//	/**
//	 * 批次id
//	 */
//	@Schema(description = "批次id")
//	private Long batchId;
	/**
	 * 采购总金额
	 */
	@Schema(description = "采购总金额")
	private Integer totalAmount;
	/**
	 * 采购总数量
	 */
	@Schema(description = "采购总数量")
	private Integer totalCount;
	/**
	 * 采购总重量
	 */
	@Schema(description = "采购总重量")
	private Integer totalWeight;
	/**
	 * 状态（0=草稿 1=待审核 2=已通过 3=已完成）
	 */
	@Schema(description = "状态（0=草稿 1=待审核 2=已通过 3=已完成）")
	private Integer purchaseStatus;
	/**
	 * 创建时间戳
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "创建时间戳")
	private Long createTimeAt;
	/**
	 * 采购员id
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "采购员id")
	private Long purchaserId;

	/**
	 * 仓库ID （运输到的仓库）
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "仓库ID （运输到的仓库）")
	private Long warehouseId;
	/**
	 * 采购员仓库ID
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "采购员仓库ID")
	private Long purchaserWarehouseId;
	/**
	 * 订单仓库ID
	 */
	@JsonSerialize( using = ToStringSerializer.class )
	@Schema(description = "订单仓库ID")
	private Long orderWarehouseId;

	/**
	 * 分配状态 1未分配完，3已分配
	 */
	@Schema(description = "分配状态 1未分配完 3已分配")
	private Integer allocationStatus;

	/**
	 *  状态 0 销售采购 1 自采
	 */
	@Schema(description = " 状态 0 销售采购 1 自采")
	private Integer selfPurchaser;
	/**
	 *  截单时间
	 */
	@Schema(description = "截单时间")
	private LocalDateTime cutTime;
}
