<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustReconciliationMapper">

    <sql id="BaseCustomerInfo">
        SELECT
        c.id AS custId,
        c.cust_name AS custName,
        c.phone,
        IFNULL(c.use_credit, 0) AS useCredit,
        -IFNULL(t.total_used, 0) AS totalUsedAmount,
        IFNULL(w.balance, 0) AS walletBalance,
        CASE
        WHEN IFNULL(w.balance, 0) >= 0 THEN -IFNULL(t.total_used, 0)
        ELSE (-IFNULL(t.total_used, 0) - IFNULL(w.balance, 0))
        END AS totalDebt
        FROM chy_cust c
        LEFT JOIN (
        SELECT cust_id, SUM(used_amount) AS total_used
        FROM chy_cust_credit_usage_record
        WHERE is_deleted = 0
        GROUP BY cust_id
        ) t ON c.id = t.cust_id
        LEFT JOIN chy_cust_wallet w ON c.id = w.cust_id AND w.is_deleted = 0
    </sql>

    <select id="selectPage" resultType="org.springblade.modules.business.cust.pojo.vo.CustReconciliationPageVO">
        <include refid="BaseCustomerInfo"/>
        WHERE c.is_deleted = 0
        <if test="dto != null and dto.keyWard != null and dto.keyWard != ''">
            AND (c.cust_name LIKE CONCAT('%', #{dto.keyWard}, '%') OR c.phone LIKE CONCAT('%', #{dto.keyWard}, '%'))
        </if>
        <if test="dto != null and dto.hasBalance != null">
            <choose>
                <when test="dto.hasBalance == 1">
                    AND IFNULL(w.balance, 0) > 0
                </when>
                <otherwise>
                    AND (w.balance IS NULL OR w.balance &lt;= 0)
                </otherwise>
            </choose>
        </if>
        <if test="dto != null and dto.hasCredit != null">
            <choose>
                <when test="dto.hasCredit == 1">
                    AND IFNULL(c.use_credit, 0) > 0
                </when>
                <otherwise>
                    AND (c.use_credit IS NULL OR c.use_credit &lt;= 0)
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectDetail" resultType="org.springblade.modules.business.cust.pojo.vo.CustReconciliationDetailVO">
        <include refid="BaseCustomerInfo"/>
        WHERE c.id = #{custId}
    </select>

    <select id="selectDetailItems" resultType="org.springblade.modules.business.cust.pojo.vo.CustReconciliationDetailItemVO">
        SELECT
            'credit' AS type,
            '信用额度' as typeName,
            '付款方式' as column1,
            '关联订单' as column2,
            '信用额度余额' as column3,
            biz_type AS bizType,
            order_code as bizNo,
            case biz_type
                WHEN 4 THEN '第三方'
                else '信用额度'
            END AS payType,
            NULL AS bizTypeName,
            used_amount AS amount,
            balance AS balance,
            update_time AS time,
            description
        FROM chy_cust_credit_usage_record
        WHERE cust_id = #{custId} AND is_deleted = 0
        UNION ALL
        SELECT
            'wallet' AS type,
            '钱包' AS typeName,

            case biz_type
                WHEN 1 THEN '第三方'
                WHEN 2 THEN '余额支付'
                WHEN 3 THEN '退款账户'
                WHEN 4 THEN '退款账户'
                WHEN 5 THEN '余额支付'
                WHEN 6 THEN '退款账户'
                WHEN 7 THEN '退款账户'
                else '付款方式'
                END AS column1,
            case biz_type
                WHEN 1 THEN '第三方流水号'
                else '关联订单'
                END AS column2,
            '账户余额' as column3,
            biz_type AS bizType,
            case biz_type
                WHEN 1 THEN trade_no
                else biz_no
                END AS bizNo,
            case biz_type
                WHEN 1 THEN '第三方'
                WHEN 2 THEN '余额支付'
                WHEN 3 THEN '账户余额'
                WHEN 4 THEN '账户余额'
                WHEN 5 THEN '余额支付'
                WHEN 6 THEN '账户余额'
                WHEN 7 THEN '账户余额'
                else '余额支付'
                END AS payType,
            NULL AS bizTypeName,
            amount AS amount,
            balance AS balance,
            update_time AS time,
            NULL AS description
        FROM chy_cust_wallet_trade_record
        WHERE cust_id = #{custId}
        ORDER BY time DESC
    </select>

</mapper>
