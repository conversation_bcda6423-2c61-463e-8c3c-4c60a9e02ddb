package org.springblade.modules.business.order.pojo.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class TransferOrderPrintDTO {

	@Schema(description = "文件名称")
	private String fileName;

	//商品分类
	@Schema(description = "商品分类")
	private String productCategory;

	//转运日期
	@NotNull(message = "请选择日期")
	@Schema(description = "转运日期")
	private String transferDate;

	@Schema(description = "token值")
	private String token;

	//打印类型
	@NotNull(message = "请选择打印类型")
	@Schema(description = "打印类型")
	private Integer printType;

	//仓库ID
	@NotNull(message = "请选择仓库")
	@Schema(description = "仓库ID")
	@Parameter(description = "仓库ID")
	private List<Long> warehouseIds;

	//截单时间
	@NotNull(message = "请选择截单时间")
	@Schema(description = "截单时间")
	@Parameter(description = "截单时间")
	private String cutoffTime;


}
