/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户钱包主表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@TableName("chy_cust_wallet")
@Schema(description = "CustWalletEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustWalletEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户id
	 */
	@Schema(description = "用户id")
	private Long custId;
	/**
	 * 账户余额
	 */
	@Schema(description = "账户余额")
	private Integer balance;
	/**
	 * 冻结余额
	 */
	@Schema(description = "冻结余额")
	private Integer freezeBalance;
	/**
	 * 累计支出
	 */
	@Schema(description = "累计支出")
	private Integer totalExpense;
	/**
	 * 累计充值
	 */
	@Schema(description = "累计充值")
	private Integer totalRecharge;

}
