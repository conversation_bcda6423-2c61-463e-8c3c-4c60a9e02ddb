package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransportStatusEnum implements BaseEnum<Integer> {

	PENDING(0, "待送货"),
	PROCESS(1, "送货中"),
	END(2, "已完成");

	private final Integer code;
	private final String message;

	public static String getNameByCode(Integer code) {
		for (TransportStatusEnum statusEnum : TransportStatusEnum.values()) {
			if (statusEnum.getCode().equals(code)) {
				return statusEnum.getMessage();
			}
		}
		return null;
	}
}
