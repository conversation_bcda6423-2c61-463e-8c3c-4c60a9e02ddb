package org.springblade.modules.business.common.helper;

import lombok.AllArgsConstructor;
import org.springblade.common.enums.WarehouseTypeEnum;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 用户仓库上下文辅助类
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class UserWarehouseHelper {

    private final IWarehouseService warehouseService;

    /**
     * 判断当前登录用户是否属于分仓
     *
     * @return true 如果是分仓, false 如果是总仓或无法确定
     */
    public boolean isCurrentUserInDivideWarehouse() {
        Long userId = AuthUtil.getUserId();
        if (userId == null) {
            // 默认非分仓逻辑
            return false;
        }
        WarehouseEntity warehouse = warehouseService.getByUserId(userId);
        // 判断仓库不为空且仓库类型为分仓
        return warehouse != null && Objects.equals(warehouse.getWarehouseType(), WarehouseTypeEnum.DIVIDE.getCode());
    }
} 