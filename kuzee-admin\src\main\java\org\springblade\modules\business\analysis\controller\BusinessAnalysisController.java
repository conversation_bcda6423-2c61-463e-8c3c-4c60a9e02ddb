/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.analysis.controller;

import cn.hutool.core.lang.Assert;
import com.qiniu.util.Auth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.business.analysis.pojo.dto.AnalysisQueryDTO;
import org.springblade.modules.business.analysis.pojo.vo.AmountsDataVO;
import org.springblade.modules.business.analysis.pojo.vo.AreaSalesVO;
import org.springblade.modules.business.analysis.pojo.vo.BusinessAnalysisVO;
import org.springblade.modules.business.analysis.pojo.vo.CustomerRankingVO;
import org.springblade.modules.business.analysis.pojo.vo.HotCategoryVO;
import org.springblade.modules.business.analysis.pojo.vo.HotProductVO;
import org.springblade.modules.business.analysis.pojo.vo.ProfitAnalysisVO;
import org.springblade.modules.business.analysis.pojo.vo.TimeDistributionVO;
import org.springblade.modules.business.analysis.service.IBusinessAnalysisService;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 经营概况（老板）控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/analysisBusiness")
@Tag(name = "经营概况（老板）接口")
@Validated
public class BusinessAnalysisController {

    private final IBusinessAnalysisService businessAnalysisService;
	private final IWarehouseService warehouseService;
    /**
     * 获取业务分析数据
     *
     * @deprecated 此接口数据量大，建议使用拆分后的细粒度接口
     */
    @PostMapping("/data")
    @Operation(summary = "获取业务分析数据(不推荐)", description = "返回所有业务分析数据，数据量较大，不推荐使用")
    public R<BusinessAnalysisVO> getBusinessAnalysis(@Valid @RequestBody AnalysisQueryDTO queryDTO) {
        return R.data(businessAnalysisService.getBusinessAnalysis(queryDTO));
    }

    /**
     * 获取业务分析金额数据
     */
    @GetMapping("/amounts")
    @Operation(summary = "获取金额统计数据", description = "获取各类金额的汇总数据和环比数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
    })
    public R<AmountsDataVO> getAmountsData(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId
	) {
        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        if (Func.isNotEmpty(warehouseId)) {
			if("all".equals(warehouseId)){
				queryDTO.setWarehouseIds(null);
			}else {
				queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
			}
        }else {
			//查询当前用户的仓库ID
			WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
			Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
			queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
		}

        return R.data(businessAnalysisService.getAmountsData(queryDTO));
    }

    /**
     * 获取订单时间分布数据
     */
    @GetMapping("/order-distribution")
    @Operation(summary = "获取订单时间分布数据", description = "根据查询条件获取订单时间分布数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false)
    })
    public R<List<TimeDistributionVO>> getOrderTimeDistribution(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getOrderTimeDistribution(queryDTO));
    }

    /**
     * 获取热销商品数据
     */
    @GetMapping("/hot-products")
    @Operation(summary = "获取热销商品数据", description = "根据查询条件获取热销商品数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
        @Parameter(name = "keyword", description = "关键词(商品名称)", required = false)
    })
    public R<IPage<HotProductVO>> getHotProducts(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId,
            @RequestParam(value = "keyword", required = false) String keyword,
            Query query) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
		queryDTO.setKeyword(keyword);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getHotProducts(queryDTO, query));
    }

    /**
     * 获取热销品类数据
     */
    @GetMapping("/hot-categories")
    @Operation(summary = "获取热销品类数据", description = "根据查询条件获取热销品类数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
        @Parameter(name = "keyword", description = "关键词(品类名称)", required = false)
    })
    public R<IPage<HotCategoryVO>> getHotCategories(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId,
			@RequestParam(value = "keyword", required = false) String keyword,
			Query query) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
		queryDTO.setKeyword(keyword);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getHotCategories(queryDTO, query));
    }

    /**
     * 获取区域销售分析数据
     */
    @GetMapping("/area-sales")
    @Operation(summary = "获取区域销售分析数据", description = "根据查询条件获取区域销售分析数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false)
    })
    public R<List<AreaSalesVO>> getAreaSales(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getAreaSales(queryDTO));
    }

    /**
     * 获取客户订单数量排行
     */
    @GetMapping("/customer-order-ranking")
    @Operation(summary = "获取客户订单数量排行", description = "根据查询条件获取客户订单数量排行")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
        @Parameter(name = "keyword", description = "关键词(客户名称)", required = false)
    })
    public R<IPage<CustomerRankingVO>> getCustomerOrderRanking(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId,
            @RequestParam(value = "keyword", required = false) String keyword,
            Query query) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
		queryDTO.setKeyword(keyword);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getCustomerOrderRanking(queryDTO, query));
    }

    /**
     * 获取客户销售金额排行
     */
    @GetMapping("/customer-sales-ranking")
    @Operation(summary = "获取客户销售金额排行", description = "根据查询条件获取客户销售金额排行")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
        @Parameter(name = "keyword", description = "关键词(客户名称)", required = false)
    })
    public R<IPage<CustomerRankingVO>> getCustomerSalesRanking(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId,
            @RequestParam(value = "keyword", required = false) String keyword,
            Query query) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
		queryDTO.setKeyword(keyword);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getCustomerSalesRanking(queryDTO, query));
    }

    /**
     * 获取客户售后金额排行
     */
    @GetMapping("/customer-after-sale-ranking")
    @Operation(summary = "获取客户售后金额排行", description = "根据查询条件获取客户售后金额排行")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false),
        @Parameter(name = "keyword", description = "关键词(客户名称)", required = false)
    })
    public R<IPage<CustomerRankingVO>> getCustomerAfterSaleRanking(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId,
            @RequestParam(value = "keyword", required = false) String keyword,
            Query query) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
		queryDTO.setKeyword(keyword);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getCustomerAfterSaleRanking(queryDTO, query));
    }

    /**
     * 获取毛利相关数据
     */
    @GetMapping("/profit-analysis")
    @Operation(summary = "获取毛利分析数据", description = "获取毛利及相关的成本、销售数据")
    @Parameters({
        @Parameter(name = "startDate", description = "开始日期", required = true),
        @Parameter(name = "endDate", description = "结束日期", required = true),
        @Parameter(name = "warehouseId", description = "仓库ID", required = false)
    })
    public R<ProfitAnalysisVO> getProfitAnalysis(
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "warehouseId", required = false) String warehouseId) {

        AnalysisQueryDTO queryDTO = new AnalysisQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        if (Func.isNotEmpty(warehouseId)) {
            if("all".equals(warehouseId)){
                queryDTO.setWarehouseIds(null);
            }else {
                queryDTO.setWarehouseIds(Collections.singletonList(Long.parseLong(warehouseId)));
            }
        }else {
            //查询当前用户的仓库ID
            WarehouseEntity warehouse = warehouseService.getByUserId(AuthUtil.getUserId());
            Assert.notNull(warehouse, () -> new ServiceException("当前用户未关联仓库"));
            queryDTO.setWarehouseIds(Collections.singletonList(warehouse.getId()));
        }

        return R.data(businessAnalysisService.getProfitAnalysis(queryDTO));
    }
}
