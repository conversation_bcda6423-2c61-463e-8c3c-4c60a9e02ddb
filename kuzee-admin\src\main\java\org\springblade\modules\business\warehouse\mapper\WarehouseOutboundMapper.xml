<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.warehouse.mapper.WarehouseOutboundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="warehouseOutboundResultMap" type="org.springblade.modules.business.warehouse.pojo.entity.WarehouseOutboundEntity">
        <result column="outbound_no" property="outboundNo"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="biz_type" property="bizType"/>
        <result column="related_order_id" property="relatedOrderId"/>
        <result column="related_order_no" property="relatedOrderNo"/>
        <result column="related_source_id" property="relatedSourceId"/>
        <result column="note" property="note"/>
        <result column="reason_id" property="reasonId"/>
        <result column="attach_urls" property="attachUrls"/>
        <result column="create_time_at" property="createTimeAt"/>
    </resultMap>

<!--    <resultMap id="WarehouseOutboundPageMap" type="org.springblade.modules.business.warehouse.pojo.vo.WarehouseOutboundPageVO">-->
<!--        <id column="id" property="id"/>-->
<!--        <result column="warehouse_id" property="warehouseId"/>-->
<!--        <result column="outbound_no" property="outboundNo"/>-->
<!--        <result column="related_order_id" property="relatedOrderId"/>-->
<!--        <result column="related_order_no" property="relatedOrderNo"/>-->
<!--        <result column="biz_type" property="bizType"/>-->
<!--        <result column="related_source_id" property="relatedSourceId"/>-->
<!--        <result column="completion_time" property="completionTime"/>-->
<!--        <result column="note" property="note"/>-->
<!--        <result column="status" property="status"/>-->
<!--        <result column="reason_id" property="reasonId"/>-->
<!--        <result column="attach_urls" property="attachUrls"/>-->
<!--        <result column="update_user" property="updateUser"/>-->
<!--        <result column="outbound_item_id" property="outboundItemId"/>-->
<!--        <result column="product_id" property="productId"/>-->
<!--        <result column="product_sku_id" property="productSkuId"/>-->
<!--        <result column="sku_code" property="skuCode"/>-->
<!--        <result column="sp_data" property="spData"/>-->
<!--        <result column="store_batch_id" property="storeBatchId"/>-->
<!--        <result column="store_batch_no" property="storeBatchNo"/>-->
<!--        <result column="quantity" property="quantity"/>-->
<!--        <result column="weight" property="weight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>-->
<!--    </resultMap>-->

    <resultMap id="aggQuantityAndWeightMap" type="org.springblade.modules.business.warehouse.pojo.vo.AggQuantityWeightVO">
        <id column="id" property="id"/>
        <result column="quantity" property="quantity"/>
        <result column="weight" property="weight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
    </resultMap>

    <!-- 出库供应商详情查询结果映射 -->
    <resultMap id="outboundSupplierDetailMap" type="org.springblade.modules.business.warehouse.pojo.vo.WarehouseOutboundSupplierDetailVO">
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_full_name" property="supplierFullName"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="outbound_weight" property="outboundWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result column="outbound_quantity" property="outboundQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sp_data" property="spData"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
    </resultMap>

    <select id="selectWarehouseOutboundPage" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseOutboundPageVO">
        SELECT s.id,s.warehouse_id,s.outbound_no,
        s.related_order_id,
        s.related_order_no,
        s.biz_type,
        s.related_source_id,
        s.completion_time,
        s.note,
        s.status,
        s.reason_id,
        s.attach_urls,
        s.delivery_cost,
        s.cust_type,
        s.cust_id,
        s.cust_name,
        s.pay_type,
        s.pay_amount,
        s.update_user
        FROM chy_warehouse_outbound s
        WHERE s.is_deleted = 0 AND s.warehouse_id = #{ew.warehouseId}
        <if test="excludeBizTypes != null and excludeBizTypes.size() > 0">
            AND s.biz_type NOT IN
            <foreach collection="excludeBizTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ew.bizType != null">
            AND s.biz_type = #{ew.bizType}
        </if>
        <if test="ew.outboundNo != null">
            AND s.outbound_no = #{ew.outboundNo}
        </if>
        <if test="ew.completionTime != null">
            AND DATE_FORMAT(s.completion_time, '%Y-%m-%d') = #{ew.completionTime}
        </if>
        ORDER BY s.completion_time DESC
    </select>

    <select id="exportWarehouseOutbound" resultType="org.springblade.modules.business.warehouse.excel.WarehouseOutboundExcel">
        SELECT * FROM chy_warehouse_outbound ${ew.customSqlSegment}
    </select>

    <select id="aggQuantityAndWeight" resultMap="aggQuantityAndWeightMap">
        SELECT
        s.related_order_id AS id,
        SUM(i.quantity) AS quantity,
        SUM(i.weight) AS weight
        FROM chy_warehouse_outbound s
        LEFT JOIN chy_warehouse_outbound_item i ON i.warehouse_outbound_id = s.id AND i.is_deleted = 0
        WHERE s.is_deleted = 0 AND s.related_order_id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        GROUP BY s.related_order_id
    </select>

    <!-- 根据订单id和sku id查询出库供应商详情 -->
    <select id="selectOutboundSupplierDetail" resultMap="outboundSupplierDetailMap">
        SELECT
            woi.supplier_id AS supplier_id,
            s.full_name AS supplier_full_name,
            s.name AS supplier_name,
            woi.weight AS outbound_weight,
            woi.quantity AS outbound_quantity,
            woi.product_sku_id,
            ss.sku_code,
            woi.sp_data,
            ss.product_id,
            p.name AS product_name
        FROM chy_warehouse_outbound wo
        LEFT JOIN chy_warehouse_outbound_item woi ON wo.id = woi.warehouse_outbound_id
        INNER JOIN chy_supplier s ON woi.supplier_id = s.id
        LEFT JOIN chy_sku_stock ss ON woi.product_sku_id = ss.id
        LEFT JOIN chy_product p ON ss.product_id = p.id
        WHERE wo.is_deleted = 0
            AND wo.status = 1
            AND wo.related_order_id = #{orderId}
            AND woi.product_sku_id = #{productSkuId}

    </select>

    <!-- 客户配送方式分组查询结果映射 -->
    <resultMap id="customerDeliveryGroupMap" type="org.springblade.modules.business.warehouse.pojo.vo.CustomerDeliveryGroupVO">
        <result column="delivery_type" property="deliveryType"/>
        <result column="cust_name" property="custName"/>
        <result column="phone" property="phone"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_province" property="receiverProvince"/>
        <result column="receiver_city" property="receiverCity"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="order_ids" property="orderIds" typeHandler="org.springblade.common.handle.LongListTypeHandler"/>
    </resultMap>

    <!-- 查询客户配送方式分组信息 -->
    <select id="selectCustomerDeliveryGroup" resultMap="customerDeliveryGroupMap">
        SELECT
            so.delivery_type,
            c.cust_name,
            c.phone,
            IF(so.delivery_type = 1, so.receiver_name, NULL) AS receiver_name,
            IF(so.delivery_type = 1, so.receiver_phone, NULL) AS receiver_phone,
            IF(so.delivery_type = 1, so.receiver_province, NULL) AS receiver_province,
            IF(so.delivery_type = 1, so.receiver_city, NULL) AS receiver_city,
            IF(so.delivery_type = 1, so.receiver_region, NULL) AS receiver_region,
            IF(so.delivery_type = 1, so.receiver_detail_address, NULL) AS receiver_detail_address,
            w.warehouse_name as warehouseName,
            c.warehouse_id as warehouseId,
            GROUP_CONCAT(so.id) AS order_ids
        FROM chy_sale_order so
        LEFT JOIN chy_cust c ON so.cust_id = c.id
        LEFT JOIN chy_warehouse w ON c.warehouse_id = w.id
        WHERE so.is_deleted = 0
            AND c.is_deleted = 0
            AND so.warehouse_id = #{dto.warehouseId}
        <if test="dto.deliveryType != null">
            AND so.delivery_type = #{dto.deliveryType}
        </if>
        <if test="dto.custInfo != null and dto.custInfo != ''">
            AND (c.cust_name LIKE CONCAT('%', #{dto.custInfo}, '%')
                 OR c.phone LIKE CONCAT('%', #{dto.custInfo}, '%'))
        </if>
        AND so.is_cut_order = 1 AND so.order_type in(0,1) AND so.is_deleted = 0
        AND NOT EXISTS(
        SELECT 1 FROM chy_warehouse_outbound wo
        WHERE wo.related_order_id = so.id
        AND wo.is_deleted = 0
        AND wo.biz_type = 1
        )
        GROUP BY
            so.delivery_type,
            c.cust_name,
            c.phone,
        IF(so.delivery_type = 1, so.receiver_name, NULL),
        IF(so.delivery_type = 1, so.receiver_phone, NULL),
        IF(so.delivery_type = 1, so.receiver_province, NULL),
        IF(so.delivery_type = 1, so.receiver_city, NULL),
        IF(so.delivery_type = 1, so.receiver_region, NULL),
        IF(so.delivery_type = 1, so.receiver_detail_address, NULL)
        ORDER BY so.delivery_type, c.cust_name
    </select>

    <select id="selectOutboundItems" resultType="org.springblade.common.pojo.vo.SalesOutboundOrderListVO">
        SELECT d.product_sku_id as skuId,d.expect_quantity as orderNum,SUM(d.difference_amount) as difference FROM chy_warehouse_outbound t
        LEFT JOIN  chy_warehouse_outbound_item d on t.id =d.warehouse_outbound_id
        where t.related_order_id=#{id} and t.is_deleted=0 and d.is_deleted=0 and t.`status`=1
        GROUP BY d.product_sku_id
    </select>
    <select id="selectSList" resultType="org.springblade.common.pojo.vo.SalesOutboundOrderListVO">
        SELECT d.product_sku_id as  skuId,d.expect_support_trans_num as supportTransOrderNum FROM chy_warehouse_outbound t
        LEFT JOIN   chy_warehouse_support_trans d on t.id =d.related_record_id
        where t.related_order_id=#{id} and t.is_deleted=0 and d.is_deleted=0 and t.`status`=1 and
        d.product_sku_id in
        <foreach collection="skuIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY d.product_sku_id
    </select>

    <select id="selectOutboundRecordsForDiffDetail" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseOutboundRecordVO">
        WITH DiffOutboundAdjustments AS (
            SELECT
                wo.related_order_id as original_outbound_id,
                woi.product_sku_id,
                woi.supplier_id,
                SUM(woi.quantity) as diff_quantity,
                SUM(woi.weight) as diff_weight
            FROM chy_warehouse_outbound wo
                     JOIN chy_warehouse_outbound_item woi ON wo.id = woi.warehouse_outbound_id
            WHERE wo.biz_type = 7 -- 差异出库
              AND wo.is_deleted = 0 AND woi.is_deleted = 0
                <if test="warehouseId != null and warehouseId != ''">AND wo.warehouse_id = #{warehouseId}</if>
                <if test="supplierId != null and supplierId != ''">AND woi.supplier_id = #{supplierId}</if>
                <if test="productSkuId != null and productSkuId != ''">AND woi.product_sku_id = #{productSkuId}</if>
                <if test="startTime != null and startTime != ''">AND wo.completion_time &gt;= #{startTime}</if>
                <if test="endTime != null and endTime != ''">AND wo.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
            GROUP BY wo.related_order_id, woi.product_sku_id, woi.supplier_id
        ),
        DiffSupportOutboundAdjustments AS (
            SELECT
                wo.related_order_id as original_outbound_id,
                wst.product_sku_id,
                wst.supplier_id,
                SUM(wst.support_trans_num) as diff_support_num
            FROM chy_warehouse_outbound wo
                JOIN chy_warehouse_support_trans wst ON wo.id = wst.related_record_id AND wst.biz_type = 2
            WHERE wo.biz_type = 7 -- 差异出库
                AND wo.is_deleted = 0 AND wst.is_deleted = 0
                <if test="warehouseId != null and warehouseId != ''">AND wo.warehouse_id = #{warehouseId}</if>
                <if test="supplierId != null and supplierId != ''">AND wst.supplier_id = #{supplierId}</if>
                <if test="productSkuId != null and productSkuId != ''">AND wst.product_sku_id = #{productSkuId}</if>
                <if test="startTime != null and startTime != ''">AND wo.completion_time &gt;= #{startTime}</if>
                <if test="endTime != null and endTime != ''">AND wo.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
            GROUP BY wo.related_order_id, wst.product_sku_id, wst.supplier_id
        ),
        SupportTransAgg AS (
            SELECT
                wst.related_record_id,
                wst.product_sku_id,
                stu.transport_name as supportTransUnitName,
                SUM(wst.support_trans_num) as outboundSupportTransNum
            FROM chy_warehouse_support_trans wst
                     JOIN chy_transport_unit stu ON wst.support_trans_unit_id = stu.id
            WHERE wst.biz_type = 2 AND wst.is_deleted = 0
            GROUP BY wst.related_record_id, wst.product_sku_id
        ),
        OutboundScope AS (
            SELECT
                wo.id as record_id,
                woi.product_sku_id,
                woi.supplier_id
            FROM chy_warehouse_outbound wo
                JOIN chy_warehouse_outbound_item woi ON wo.id = woi.warehouse_outbound_id
            WHERE wo.is_deleted = 0 AND woi.is_deleted = 0 AND wo.status = 1 AND wo.biz_type != 7
                <if test="warehouseId != null and warehouseId != ''">AND wo.warehouse_id = #{warehouseId}</if>
                <if test="supplierId != null and supplierId != ''">AND woi.supplier_id = #{supplierId}</if>
                <if test="productSkuId != null and productSkuId != ''">AND woi.product_sku_id = #{productSkuId}</if>
                <if test="startTime != null and startTime != ''">AND wo.completion_time &gt;= #{startTime}</if>
                <if test="endTime != null and endTime != ''">AND wo.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
            UNION
            SELECT
                wo.id as record_id,
                wst.product_sku_id,
                wst.supplier_id
            FROM chy_warehouse_outbound wo
                JOIN chy_warehouse_support_trans wst ON wo.id = wst.related_record_id
            WHERE wo.is_deleted = 0 AND wst.is_deleted = 0 AND wst.biz_type = 2 AND wo.status = 1 AND wo.biz_type != 7
                <if test="warehouseId != null and warehouseId != ''">AND wo.warehouse_id = #{warehouseId}</if>
                <if test="supplierId != null and supplierId != ''">AND wst.supplier_id = #{supplierId}</if>
                <if test="productSkuId != null and productSkuId != ''">AND wst.product_sku_id = #{productSkuId}</if>
                <if test="startTime != null and startTime != ''">AND wo.completion_time &gt;= #{startTime}</if>
                <if test="endTime != null and endTime != ''">AND wo.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
        )
        SELECT
            wo.id,
            wo.outbound_no as outboundNo,
            wo.related_order_no as relatedOrderNo,
            wo.biz_type as bizType,
            wo.completion_time as completionTime,
            wo.update_user as updateUser,
            wo.warehouse_id as warehouseId,
            w.warehouse_name as warehouseName,

            ps.product_id as productId,
            scope.product_sku_id as productSkuId,
            (COALESCE(woi.quantity, 0) + COALESCE(doa.diff_quantity, 0)) as outboundQuantity,
            (COALESCE(woi.weight, 0) + COALESCE(doa.diff_weight, 0)) as outboundWeight,
            scope.supplier_id as supplierId,

            p.name as productName,
            ps.sku_code as skuCode,
            ps.sp_data as spData,

            sup.name as supplierName,

            (COALESCE(sta.outboundSupportTransNum, 0) + COALESCE(dsoa.diff_support_num, 0)) as outboundSupportTransNum,
            sta.supportTransUnitName
        FROM
            OutboundScope scope
                JOIN chy_warehouse_outbound wo ON scope.record_id = wo.id
                LEFT JOIN chy_warehouse_outbound_item woi ON scope.record_id = woi.warehouse_outbound_id AND scope.product_sku_id = woi.product_sku_id AND woi.is_deleted = 0
                LEFT JOIN DiffOutboundAdjustments doa ON wo.id = doa.original_outbound_id AND scope.product_sku_id = doa.product_sku_id AND scope.supplier_id = doa.supplier_id
                LEFT JOIN DiffSupportOutboundAdjustments dsoa ON wo.id = dsoa.original_outbound_id AND scope.product_sku_id = dsoa.product_sku_id AND scope.supplier_id = dsoa.supplier_id
                LEFT JOIN chy_supplier sup ON scope.supplier_id = sup.id
                LEFT JOIN SupportTransAgg sta ON sta.related_record_id = wo.id AND sta.product_sku_id = scope.product_sku_id
                LEFT JOIN chy_sku_stock ps ON scope.product_sku_id = ps.id
                LEFT JOIN chy_product p ON ps.product_id = p.id
                LEFT JOIN chy_warehouse w ON wo.warehouse_id = w.id
        WHERE
            wo.is_deleted = 0
          AND wo.status = 1
          AND wo.biz_type != 7
        ORDER BY wo.completion_time DESC
    </select>

    <resultMap id="resultSupplierReturn" type="org.springblade.modules.business.warehouse.pojo.vo.SupplierReturnOutboundVO">
        <result property="returnTime" column="returnTime"/>
        <result property="skuCode" column="skuCode"/>
        <result property="productName" column="productName"/>
        <result property="isStandard" column="isStandard"/>
        <result property="spData" column="spData"/>
        <result property="baseUnitName" column="baseUnitName"/>
        <result property="quantity" column="quantity"/>
        <result property="weight" column="weight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result property="productSalePrice" column="productSalePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="totalAmount" column="totalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
    </resultMap>

    <select id="getSupplierReturnDetailSummary" resultMap="resultSupplierReturn">
            SELECT
                o.create_time AS returnTime,
                o.outbound_no AS outboundNo,
                i.sku_code AS skuCode,
                p.`name` AS productName,
                CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
                i.sp_data AS  spData,
                bu.unit_name AS baseUnitName,
                i.quantity,
                i.weight,
                i.product_sale_price AS productSalePrice,
                COALESCE((i.weight / 500) * i.product_sale_price, 0) AS totalAmount
            FROM chy_warehouse_outbound o
            INNER JOIN chy_warehouse_outbound_item i ON i.warehouse_outbound_id = o.id
            LEFT JOIN chy_product p ON i.product_id = p.id AND p.is_deleted = 0
            LEFT JOIN chy_base_unit bu ON p.base_unit_id = bu.id AND bu.is_deleted = 0
            WHERE o.`status` = 1 AND o.is_deleted = 0
              AND i.supplier_id = #{supplierId}
              AND o.biz_type = 5
              AND o.create_time_at &gt;= #{startTime}
              AND o.create_time_at &lt;= #{endTime}
            <if test="productSkuId != null">
                AND i.product_sku_id = #{productSkuId}
            </if>
    </select>
</mapper>
