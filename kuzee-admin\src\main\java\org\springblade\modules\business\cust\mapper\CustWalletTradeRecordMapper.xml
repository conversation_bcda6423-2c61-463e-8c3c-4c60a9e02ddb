<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustWalletTradeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custWalletTradeRecordResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustWalletTradeRecordEntity">
        <result column="cust_id" property="custId"/>
        <result column="amount" property="amount"/>
        <result column="balance" property="balance"/>
        <result column="remaining_amount" property="remainingAmount"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="biz_type" property="bizType"/>
        <result column="biz_no" property="bizNo"/>
    </resultMap>

    <select id="selectCustWalletTradeRecordPage" resultMap="custWalletTradeRecordResultMap">
        select * from chy_cust_wallet_trade_record where is_deleted = 0
    </select>

    <select id="exportCustWalletTradeRecord" resultType="org.springblade.modules.business.cust.excel.CustWalletTradeRecordExcel">
        SELECT * FROM chy_cust_wallet_trade_record ${ew.customSqlSegment}
    </select>

</mapper>
