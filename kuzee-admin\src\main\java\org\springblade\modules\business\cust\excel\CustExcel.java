/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.excel;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serial;


/**
 * 客户表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 父级客户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("父级客户id")
	private Long parentId;
	/**
	 * 客户名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户名称")
	private String custName;
	/**
	 * 所属分区
	 */
	@ColumnWidth(20)
	@ExcelProperty("所属分区")
	private Long warehouseId;
	/**
	 * 授信额度
	 */
	@ColumnWidth(20)
	@ExcelProperty("授信额度")
	private Integer creditLimit;
	/**
	 * 区域id
	 */
	@ColumnWidth(20)
	@ExcelProperty("区域id")
	private LocalDateTime creditEndTime;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String phone;
	/**
	 * 密码
	 */
	@ColumnWidth(20)
	@ExcelProperty("密码")
	private String password;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String nickname;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String icon;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Byte gender;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private Byte sourceType;
	/**
	 * 客户角色id
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户角色id")
	private Long custRoleId;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String unionid;

}
