/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户钱包交易记录表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
@TableName("chy_cust_wallet_trade_record")
@Schema(description = "CustWalletTradeRecordEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustWalletTradeRecordEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long custId;
	/**
	 * 交易金额(正值表示余额增加，负值表示余额减少)
	 */
	@Schema(description = "交易金额(正值表示余额增加，负值表示余额减少)")
	private Integer amount;
	/**
	 * 交易后余额
	 */
	@Schema(description = "交易后余额")
	private Integer balance;
	/**
	 * 剩余可提现额度
	 */
	@Schema(description = "剩余可提现额度")
	private Integer remainingAmount;
	/**
	 * 交易流水号
	 */
	@Schema(description = "交易流水号")
	private String tradeNo;
	/**
	 * 业务类型（枚举WalletTradeBizTypeEnum）
	 */
	@Schema(description = "业务类型（枚举WalletTradeBizTypeEnum）")
	private Integer bizType;
	/**
	 * 关联业务编号
	 */
	@Schema(description = "关联业务编号")
	private String bizNo;

}
