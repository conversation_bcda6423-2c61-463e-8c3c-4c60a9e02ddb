<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.order.mapper.SaleOrderChangeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="saleOrderChangeRecordResultMap" type="org.springblade.modules.business.order.pojo.entity.SaleOrderChangeRecordEntity">
        <result column="sale_order_id" property="saleOrderId"/>
        <result column="sale_order_item_id" property="saleOrderItemId"/>
        <result column="old_order_price" property="oldOrderPrice"/>
        <result column="new_order_price" property="newOrderPrice"/>
    </resultMap>

    <select id="selectSaleOrderChangeRecordPage" resultMap="saleOrderChangeRecordResultMap">
        select * from chy_sale_order_change_record where is_deleted = 0
    </select>

    <select id="exportSaleOrderChangeRecord" resultType="org.springblade.modules.business.order.excel.SaleOrderChangeRecordExcel">
        SELECT * FROM chy_sale_order_change_record ${ew.customSqlSegment}
    </select>

</mapper>
