/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.business.order.pojo.entity.SaleOrderSourceEntity;
import org.springblade.modules.business.order.pojo.vo.SaleOrderSourceVO;
import java.util.Objects;

/**
 * 销售订单原始数据表-支付成功时候的数据 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public class SaleOrderSourceWrapper extends BaseEntityWrapper<SaleOrderSourceEntity, SaleOrderSourceVO>  {

	public static SaleOrderSourceWrapper build() {
		return new SaleOrderSourceWrapper();
 	}

	@Override
	public SaleOrderSourceVO entityVO(SaleOrderSourceEntity saleOrderSource) {
		SaleOrderSourceVO saleOrderSourceVO = Objects.requireNonNull(BeanUtil.copyProperties(saleOrderSource, SaleOrderSourceVO.class));

		//User createUser = UserCache.getUser(saleOrderSource.getCreateUser());
		//User updateUser = UserCache.getUser(saleOrderSource.getUpdateUser());
		//saleOrderSourceVO.setCreateUserName(createUser.getName());
		//saleOrderSourceVO.setUpdateUserName(updateUser.getName());

		return saleOrderSourceVO;
	}

}
