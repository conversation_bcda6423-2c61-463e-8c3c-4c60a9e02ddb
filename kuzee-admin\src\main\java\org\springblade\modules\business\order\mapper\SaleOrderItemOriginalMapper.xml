<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.order.mapper.SaleOrderItemOriginalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="saleOrderItemOriginalResultMap" type="org.springblade.modules.business.order.pojo.entity.SaleOrderItemOriginalEntity">
        <result column="order_id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_brand" property="productBrand"/>
        <result column="product_unit" property="productUnit"/>
        <result column="product_sale_price" property="productSalePrice"/>
        <result column="product_unit_price" property="productUnitPrice"/>
        <result column="product_price" property="productPrice"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="package_gross_conversion_rate" property="packageGrossConversionRate"/>
        <result column="product_id" property="productId"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="product_attr" property="productAttr"/>
        <result column="difference_type" property="differenceType"/>
        <result column="difference_status" property="differenceStatus"/>
        <result column="difference_amount" property="differenceAmount"/>
        <result column="support_trans_price" property="supportTransPrice"/>
        <result column="support_trans" property="supportTrans"/>
        <result column="support_trans_unit_id" property="supportTransUnitId"/>
        <result column="support_trans_num" property="supportTransNum"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="cancel_user_id" property="cancelUserId"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="sku_code" property="skuCode"/>
        <result column="note" property="note"/>
        <result column="delivery_expense" property="deliveryExpense"/>
        <result column="product_weight" property="productWeight"/>
        <result column="is_standard" property="isStandard"/>
        <result column="transport_conversion_rate" property="transportConversionRate"/>
        <result column="service_expense" property="serviceExpense"/>
    </resultMap>

    <select id="selectSaleOrderItemOriginalPage" resultMap="saleOrderItemOriginalResultMap">
        select * from chy_sale_order_item_original where is_deleted = 0
    </select>

    <select id="exportSaleOrderItemOriginal" resultType="org.springblade.modules.business.order.excel.SaleOrderItemOriginalExcel">
        SELECT * FROM chy_sale_order_item_original ${ew.customSqlSegment}
    </select>

</mapper>
