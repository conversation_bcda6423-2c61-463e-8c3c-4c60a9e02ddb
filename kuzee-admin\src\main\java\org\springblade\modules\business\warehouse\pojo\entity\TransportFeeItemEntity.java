/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.handle.WeightTypeHandle;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 转运费流水明细表(供应商配送产生) 实体类
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@TableName(value = "chy_transport_fee_item", autoResultMap = true)
@Schema(description = "TransportFeeItemEntity对象")
@EqualsAndHashCode(callSuper = true)
public class TransportFeeItemEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 转运费主id
	 */
	@Schema(description = "转运费主id")
	private Long transportFeeId;

	@Schema(description = "入库记录id")
	private Long storeItemId;
	/**
	 * 商品ID
	 */
	@Schema(description = "商品ID")
	private Long productId;
	/**
	 * 商品SKU
	 */
	@Schema(description = "商品SKU")
	private Long productSkuId;
	/**
	 * 商品销售属性
	 */
	@Schema(description = "商品销售属性")
	private String spData;

	@Schema(description = "商品数量")
	private Integer quantity;
	/**
	 * 商品重量(g)
	 */
	@Schema(description = "商品重量(g)")
	@TableField(typeHandler = WeightTypeHandle.class)
	private BigDecimal weight;
	/**
	 * 转运费(分)
	 */
	@Schema(description = "转运费(分)")
	private Integer amount;

	@Schema(description = "基础价格（要么是基础单位的单价，要么是包装单位的单价，根据price_type来确定）")
	private Integer price;

	@Schema(description = "基础价格类型（0-基本单位单价，1-包装单位单价）")
	private Integer priceType;
}
