/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.pojo.vo.SalesOutboundOrderListVO;
import org.springblade.modules.business.warehouse.excel.WarehouseOutboundExcel;
import org.springblade.modules.business.warehouse.pojo.dto.CustomerDeliveryQueryDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseOutboundQueryDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseOutboundEntity;
import org.springblade.modules.business.warehouse.pojo.vo.*;

import java.util.List;

/**
 * 出库记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface WarehouseOutboundMapper extends BaseMapper<WarehouseOutboundEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page            分页参数
	 * @param excludeBizTypes
	 * @return List<WarehouseOutboundVO>
	 */
	List<WarehouseOutboundPageVO> selectWarehouseOutboundPage(IPage<WarehouseOutboundPageVO> page, @Param("ew") WarehouseOutboundQueryDTO dto, @Param("excludeBizTypes") List<Integer> excludeBizTypes);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<WarehouseOutboundExcel>
	 */
	List<WarehouseOutboundExcel> exportWarehouseOutbound(@Param("ew") Wrapper<WarehouseOutboundEntity> queryWrapper);

	/**
	 * 根据关联ids统计出库数量及重量
	 *
	 * @param ids 关联ids
	 */
    List<AggQuantityWeightVO> aggQuantityAndWeight(@Param("ids") List<Long> ids);

	/**
	 * 根据订单id和sku id查询出库供应商详情
	 *
	 * @param orderId 订单id
	 * @param productSkuId sku的id
	 * @return 出库供应商详情
	 */
	List<WarehouseOutboundSupplierDetailVO> selectOutboundSupplierDetail(@Param("orderId") Long orderId, @Param("productSkuId") Long productSkuId);

	/**
	 * 查询客户配送方式分组信息
	 *
	 * @param page 分页参数
	 * @param dto 查询条件
	 * @return 客户配送方式分组信息列表
	 */
	List<CustomerDeliveryGroupVO> selectCustomerDeliveryGroup(IPage<CustomerDeliveryGroupVO> page, @Param("dto") CustomerDeliveryQueryDTO dto);

    List<SalesOutboundOrderListVO> selectOutboundItems(@Param("id") Long id);

	List<SalesOutboundOrderListVO> selectSList(@Param("skuIds") List<Long> skuIds, @Param("id") Long id);

	/**
	 * 查询客户配送方式分组信息分页列表
	 *
	 * @param dto 查询条件
	 * @param page 分页参数
	 * @return 客户配送方式分组信息分页列表
	 */
	IPage<CustomerDeliveryGroupVO> getCustomerDeliveryGroup(@Param("dto") CustomerDeliveryQueryDTO dto, IPage<CustomerDeliveryGroupVO> page);

	/**
	 * 为差异详情页查询出库记录
	 *
	 * @param warehouseId  仓库ID
	 * @param supplierId   供应商ID
	 * @param productSkuId 商品SKU ID
	 * @param startTime    开始时间
	 * @param endTime      结束时间
	 * @return 出库记录列表
	 */
	List<WarehouseOutboundRecordVO> selectOutboundRecordsForDiffDetail(@Param("warehouseId") Long warehouseId, @Param("supplierId") Long supplierId, @Param("productSkuId") Long productSkuId, @Param("startTime") String startTime, @Param("endTime") String endTime);

	/**
	 * 供应商退货详情
	 *
	 * @param supplierId   供应商id
	 * @param startTime    退货时间
	 * @param endTime      退货时间
	 * @param productSkuId 商品skuId
	 */
    List<SupplierReturnOutboundVO> getSupplierReturnDetailSummary(@Param("supplierId") Long supplierId, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("productSkuId") Long productSkuId);
}
