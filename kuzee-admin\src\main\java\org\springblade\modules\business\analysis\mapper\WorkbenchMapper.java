/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: Chill <PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.analysis.mapper;

import org.apache.ibatis.annotations.Param;

/**
 * 工作台数据访问接口
 *
 * <AUTHOR>
 */
public interface WorkbenchMapper {

    /**
     * 获取待转运数量(未完成的转入转出)
     *
     * @param warehouseId 仓库ID
     * @return 待转运数量
     */
    Integer getPendingTransportCount(@Param("warehouseId") Long warehouseId);

    /**
     * 获取待入库订单数量(已发送给供应商需要供应商供货的采购订单数量)
     *
     * @param warehouseId 仓库ID
     * @return 待入库订单数量
     */
    Integer getPendingInboundCount(@Param("warehouseId") Long warehouseId);

    /**
     * 获取待出库订单数量(还未完成出库的销售订单数量)
     *
     * @param warehouseId 仓库ID
     * @return 待出库订单数量
     */
    Integer getPendingOutboundCount(@Param("warehouseId") Long warehouseId);

    /**
     * 获取待处理售后订单数量(客户申请售后未处理完成的订单数量)
     *
     * @param warehouseId 仓库ID
     * @return 待处理售后订单数量
     */
    Integer getPendingAfterSalesCount(@Param("warehouseId") Long warehouseId);
} 