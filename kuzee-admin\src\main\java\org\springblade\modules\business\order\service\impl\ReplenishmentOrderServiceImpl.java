/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.RegionCache;
import org.springblade.common.cache.TransportUnitCache;
import org.springblade.common.cache.UserCache;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.ReplenishmentStatusEnum;
import org.springblade.common.enums.SupplyWayEnum;
import org.springblade.common.pdf.PrintingSupplyOrder;
import org.springblade.common.pdf.PrintingTransferOrder;
import org.springblade.common.pojo.vo.*;
import org.springblade.common.utills.CommonUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.common.pdf.PDFServerUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.*;
import org.springblade.modules.business.order.excel.ReplenishmentOrderExcel;
import org.springblade.modules.business.order.mapper.*;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.*;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.product.mapper.SkuStockMapper;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.pojo.entity.SkuSupplierRelationEntity;
import org.springblade.modules.business.product.pojo.entity.TransportUnitEntity;
import org.springblade.modules.business.product.pojo.vo.PStockVO;
import org.springblade.modules.business.product.pojo.vo.WarehouseStorePrice;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.product.service.ISkuSupplierRelationService;
import org.springblade.modules.business.product.service.ISkuWarehouseRelationService;
import org.springblade.modules.business.warehouse.mapper.WarehouseMapper;
import org.springblade.modules.business.warehouse.mapper.WarehouseStoreMapper;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreItemEntity;
import org.springblade.modules.business.warehouse.service.ITransportFeeService;
import org.springblade.modules.business.warehouse.utils.WarehouseHelper;
import org.springblade.modules.system.pojo.entity.Region;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商备货表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class ReplenishmentOrderServiceImpl extends BaseServiceImpl<ReplenishmentOrderMapper, ReplenishmentOrderEntity> implements IReplenishmentOrderService {

	private final IReplenishmentOrderItemService replenishmentOrderItemService;
	private final ISkuStockService skuStockService;
	private final IProductService productService;
	private final IPurchaseOrderService purchaseOrderService;
	private final ISupplierService supplierService;
	private final ITransportFeeService transportFeeService;
	private final SupplierMapper supplierMapper;
	private final WarehouseMapper warehouseMapper;
	private final PurchaseOrderMapper purchaseOrderMapper;
	private final ReplenishmentOrderItemMapper replenishmentOrderItemMapper;
	private final IUserService userService;
	private final WarehouseStoreMapper warehouseStoreMapper;
	private final ISkuWarehouseRelationService skuWarehouseRelationService;
	private final ISkuSupplierRelationService skuSupplierRelationService;
	private final IPurchaseOrderItemService purchaseOrderItemService;
	private final SkuStockMapper skuStockMapper;

	@Override
	public IPage<ReplenishmentOrderVO> selectReplenishmentOrderPage(IPage<ReplenishmentOrderVO> page, ReplenishmentOrderVO replenishmentOrder) {
		return page.setRecords(baseMapper.selectReplenishmentOrderPage(page, replenishmentOrder));
	}

	@Override
	public List<ReplenishmentOrderExcel> exportReplenishmentOrder(Wrapper<ReplenishmentOrderEntity> queryWrapper) {
		List<ReplenishmentOrderExcel> replenishmentOrderList = baseMapper.exportReplenishmentOrder(queryWrapper);
		//replenishmentOrderList.forEach(replenishmentOrder -> {
		//	replenishmentOrder.setTypeName(DictCache.getValue(DictEnum.YES_NO, ReplenishmentOrderEntity.getType()));
		//});
		return replenishmentOrderList;
	}

	@Override
	public IPage<ReplenishmentListVO> replenishmentList(ReplenishmentListDTO dto, Query query) {
		IPage<ReplenishmentListVO> page = Condition.getPage(query);
		IPage<ReplenishmentListVO> replenishmentList = baseMapper.replenishmentList(dto, page);
		for (ReplenishmentListVO replenishment : replenishmentList.getRecords()) {
			replenishment.setSupplyWayName(SupplyWayEnum.getNameByCode(replenishment.getSupplyWay()));
			replenishment.setNote(replenishment.getNoteDetail());
		}
//		//获取采购单ID集合
//		List<Long> IdList = replenishmentList.getRecords().stream().map(ReplenishmentListVO::getId).distinct().toList();
//		//通过ID集合查询采购单明细
//		List<ReplenishmentListProductVO> itemList = replenishmentOrderItemService.getItemList(IdList);

//		//通过sku获取所有的商品名称
//		List<Long> productIdList = itemList.stream().map(ReplenishmentListProductVO::getProductId).distinct().toList();
//		//获取sku对应的商品名称
//		List<ReplenishmentProductVO> productNameList = productService.getProductNameList(productIdList);
//
//		//明细关联商品名称
//		itemList.forEach(item -> {
//			productNameList.forEach(product -> {
//				if (item.getProductId().equals(product.getId())) {
//					item.setProductName(product.getProductName());
//				}
//			});
//		});
		//采购单关联明细
//		replenishmentList.getRecords().forEach(replenishment -> {
//			List<ReplenishmentListProductVO> itemList1 = itemList.stream().filter(item -> item.getReplenishmentOrderId().equals(replenishment.getId())).toList();
//			replenishment.setListProduct(itemList1);
//		});
		return replenishmentList;
	}

	@Override
	public List<ReplenishmentDetailVO> replenishmentDetail(ReplenishmentDetailDTO dto) {
		List<Long> idList = Arrays.stream(dto.getIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
		List<ReplenishmentDetailVO> replenishmentDetail = baseMapper.replenishmentDetail(idList);

		//通过ID集合查询采购单明细
		List<ReplenishmentDetailProductVO> itemList = replenishmentOrderItemService.getItemDetailList(idList);

		//通过sku获取所有的商品名称
		List<Long> productIdList = itemList.stream().map(ReplenishmentDetailProductVO::getProductId).distinct().toList();
		//获取sku对应的商品名称
		List<ReplenishmentProductVO> productNameList = productService.getProductNameList(productIdList);

		//明细关联商品名称
		itemList.forEach(item -> {
			productNameList.forEach(product -> {
				if (item.getProductId().equals(product.getId())) {
					item.setProductName(product.getProductName());
				}
			});
		});
		//采购单关联明细
		replenishmentDetail.forEach(replenishment -> {
			List<ReplenishmentDetailProductVO> itemList1 = itemList.stream().filter(item -> item.getReplenishmentOrderId().equals(replenishment.getId())).toList();
			replenishment.setProductList(itemList1);
		});

		return replenishmentDetail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String save(ReplenishmentOrderDTO dto) {
		String tip = checkOrderItem(dto);
		if (Func.isEmpty(dto.getItemList())) {
			return tip;
		}

		// 1. 获取或创建备货主表
		ReplenishmentOrderEntity entity = this.getOrCreateReplenishmentOrder(dto);

		// 2. 处理备货明细
		ItemProcessResult processResult = this.processReplenishmentItems(dto, entity);

		// 3. 保存明细数据
		this.saveOrUpdateItems(processResult);

		// 4. 更新主表总数量（如果是复用的主表）
		this.updateMainOrderTotalCount(entity, processResult);

		// 5. 更新对应采购单数量及状态
		purchaseOrderService.updateStatusAndQuantity(dto.getPurchaseOrderId(), dto.getItemList(), false);

		return tip;
	}

	/**
	 * 获取或创建备货主表
	 */
	private ReplenishmentOrderEntity getOrCreateReplenishmentOrder(ReplenishmentOrderDTO dto) {
		ReplenishmentOrderEntity existingOrder = this.findReusableOrder(dto);

		if (existingOrder != null) {
			log.info("复用已存在的备货主表，id: {}", existingOrder.getId());
			return existingOrder;
		}

		// 创建新的备货主表
		ReplenishmentOrderEntity entity = BeanUtil.copyProperties(dto, ReplenishmentOrderEntity.class);
		if (Func.isNull(entity)) {
			throw new ServiceException("数据异常");
		}

		entity.setCreateTimeAt(System.currentTimeMillis());
		entity.setTotalCount(dto.totalCount());
		entity.setReplenishmentNo(GenerateNumberUtil.generate(BusinessConstant.ORDER_SUPPLIER_NO, ""));
		entity.setStatus(ReplenishmentStatusEnum.PENDING.getCode());

		Assert.isTrue(save(entity), "生成备货单失败");
		log.info("创建新的备货主表，id: {}", entity.getId());

		return entity;
	}

	/**
	 * 处理备货明细
	 */
	private ItemProcessResult processReplenishmentItems(ReplenishmentOrderDTO dto, ReplenishmentOrderEntity entity) {
		ItemProcessResult result = new ItemProcessResult();

		// 构建已存在明细的映射
		Map<String, ReplenishmentOrderItemEntity> existingItemsMap = this.buildExistingItemsMap(entity.getId());

		// 处理每个新的明细项
		for (ReplenishmentOrderItemDTO itemDto : dto.getItemList()) {
			this.processNewItemDetail(dto, entity, itemDto, existingItemsMap, result);
		}

		return result;
	}

	/**
	 * 构建已存在明细的映射
	 */
	private Map<String, ReplenishmentOrderItemEntity> buildExistingItemsMap(Long replenishmentOrderId) {
		List<ReplenishmentOrderItemEntity> existingItems = replenishmentOrderItemService.list(
			Wrappers.<ReplenishmentOrderItemEntity>lambdaQuery()
				.eq(ReplenishmentOrderItemEntity::getReplenishmentOrderId, replenishmentOrderId)
				.eq(ReplenishmentOrderItemEntity::getIsDeleted, 0)
		);

		Map<String, ReplenishmentOrderItemEntity> existingItemsMap = new HashMap<>();
		for (ReplenishmentOrderItemEntity item : existingItems) {
			String key = this.buildItemKey(item);
			existingItemsMap.put(key, item);
		}

		return existingItemsMap;
	}

	/**
	 * 处理单个新明细项
	 */
	private void processNewItemDetail(ReplenishmentOrderDTO dto, ReplenishmentOrderEntity entity,
									  ReplenishmentOrderItemDTO itemDto, Map<String, ReplenishmentOrderItemEntity> existingItemsMap,
									  ItemProcessResult result) {

		String itemKey = this.buildItemKey(itemDto);
		ReplenishmentOrderItemEntity existingItem = existingItemsMap.get(itemKey);

		if (existingItem != null) {
			// 更新已存在的明细
			this.updateExistingItem(existingItem, itemDto);
			result.getUpdateItems().add(existingItem);
			log.info("更新已存在的备货明细，skuId: {}, 新增数量: {}", itemDto.getProductSkuId(), itemDto.getQuantity());
		} else {
			// 新增明细
			ReplenishmentOrderItemEntity newItem = this.createNewItem(dto, entity, itemDto);
			result.getNewItems().add(newItem);
			log.info("新增备货明细，skuId: {}, 数量: {}", itemDto.getProductSkuId(), itemDto.getQuantity());
		}

		result.addTotalQuantity(itemDto.getQuantity());
	}

	/**
	 * 更新已存在的明细
	 */
	private void updateExistingItem(ReplenishmentOrderItemEntity existingItem, ReplenishmentOrderItemDTO itemDto) {
		existingItem.setQuantity(existingItem.getQuantity() + itemDto.getQuantity());
		existingItem.setNote(itemDto.getNote());

		int existingTransNum = existingItem.getSupportTransNum() != null ? existingItem.getSupportTransNum() : 0;
		int newTransNum = itemDto.getSupportTransNum() != null ? itemDto.getSupportTransNum() : 0;
		existingItem.setSupportTransNum(existingTransNum + newTransNum);
	}

	/**
	 * 创建新的明细项
	 */
	private ReplenishmentOrderItemEntity createNewItem(ReplenishmentOrderDTO dto, ReplenishmentOrderEntity entity,
													   ReplenishmentOrderItemDTO itemDto) {

		ReplenishmentOrderItemEntity itemEntity = new ReplenishmentOrderItemEntity();
		itemEntity.setNote(itemDto.getNote());
		itemEntity.setPurchaseOrderId(dto.getPurchaseOrderId());
		itemEntity.setPurchaseOrderItemId(itemDto.getPurchaseOrderItemId());
		itemEntity.setReplenishmentOrderId(entity.getId());
		itemEntity.setProductId(itemDto.getProductId());
		itemEntity.setProductSkuId(itemDto.getProductSkuId());
		itemEntity.setSkuCode(itemDto.getSkuCode());
		itemEntity.setSpData(itemDto.getSpData());
		itemEntity.setQuantity(itemDto.getQuantity());
		itemEntity.setUnitPrice(itemDto.getUnitPrice());
		itemEntity.setWholePrice(itemDto.getWholePrice());
		itemEntity.setSupportTransUnitId(itemDto.getSupportTransUnitId());
		itemEntity.setSupportTransNum(itemDto.getSupportTransNum());
		itemEntity.setCreateTimeAt(System.currentTimeMillis());

		// 设置商品名称
		this.setProductName(itemEntity, itemDto.getProductId());

		return itemEntity;
	}

	/**
	 * 设置商品名称
	 */
	private void setProductName(ReplenishmentOrderItemEntity itemEntity, Long productId) {
		if (Func.isNotEmpty(productId)) {
			List<ReplenishmentProductVO> productList = productService.getProductNameList(Collections.singletonList(productId));
			if (Func.isNotEmpty(productList)) {
				itemEntity.setProductName(productList.get(0).getProductName());
			}
		}
	}

	/**
	 * 保存或更新明细
	 */
	private void saveOrUpdateItems(ItemProcessResult result) {
		if (!result.getNewItems().isEmpty()) {
			// 设置重量和配套运输单价
			// 获取result.getNewItems()中sku的packageGrossConversionRate并以productSkuId为key保存
			Map<Long, BigDecimal> packageGrossConversionRateMap = this.getPackageGrossConversionRateMap(result.getNewItems());
			result.getNewItems().forEach(
				itemEntity -> this.setSupportTransPriceAndWeight(packageGrossConversionRateMap, itemEntity)
			);
			Assert.isTrue(replenishmentOrderItemService.saveBatch(result.getNewItems()), "生成备货单商品明细失败");
		}
		if (!result.getUpdateItems().isEmpty()) {
			// 设置重量和配套运输单价
			// 获取result.getUpdateItems()中sku的packageGrossConversionRate并以productSkuId为key保存
			Map<Long, BigDecimal> packageGrossConversionRateMap = this.getPackageGrossConversionRateMap(result.getUpdateItems());
			result.getUpdateItems().forEach(
				itemEntity -> this.setSupportTransPriceAndWeight(packageGrossConversionRateMap, itemEntity)
			);
			Assert.isTrue(replenishmentOrderItemService.updateBatchById(result.getUpdateItems()), "更新备货单商品明细失败");
		}
	}

	private void setSupportTransPriceAndWeight(Map<Long, BigDecimal> packageGrossConversionRateMap, ReplenishmentOrderItemEntity itemEntity) {
		itemEntity.setWeight(
			(
				packageGrossConversionRateMap.get(itemEntity.getProductSkuId()) == null ? BigDecimal.ZERO : packageGrossConversionRateMap.get(itemEntity.getProductSkuId())
			).multiply(BigDecimal.valueOf(itemEntity.getQuantity()))
		);
		if (org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(itemEntity.getSupportTransUnitId())
			&& org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(TransportUnitCache.getById(itemEntity.getSupportTransUnitId()))
			&& org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(TransportUnitCache.getById(itemEntity.getSupportTransUnitId()).getPrice())) {
			itemEntity.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(TransportUnitCache.getById(itemEntity.getSupportTransUnitId()).getPrice()));
		}
	}

	/**
	 * 获取ReplenishmentOrderItemEntity中sku的packageGrossConversionRate并以productSkuId为key保存
	 */
	private Map<Long, BigDecimal> getPackageGrossConversionRateMap(List<ReplenishmentOrderItemEntity> newItems) {
		if (CollectionUtil.isEmpty(newItems)) {
			return Collections.emptyMap();
		}
		Set<Long> productSkuIds = newItems.stream().map(ReplenishmentOrderItemEntity::getProductSkuId).collect(Collectors.toSet());
		if (CollectionUtil.isEmpty(productSkuIds)) {
			return Collections.emptyMap();
		}
		List<SkuStockEntity> skuStockEntities = skuStockService.list(new QueryWrapper<SkuStockEntity>().lambda().in(SkuStockEntity::getId, productSkuIds));
		if (CollectionUtil.isEmpty(skuStockEntities)) {
			return Collections.emptyMap();
		}
		Map<Long, BigDecimal> packageGrossConversionRateMap = skuStockEntities.stream().collect(Collectors.toMap(SkuStockEntity::getId, SkuStockEntity::getPackageGrossConversionRate));
		if (CollectionUtil.isEmpty(packageGrossConversionRateMap)) {
			return Collections.emptyMap();
		} else {
			return packageGrossConversionRateMap;
		}
	}

	/**
	 * 更新主表总数量
	 */
	private void updateMainOrderTotalCount(ReplenishmentOrderEntity entity, ItemProcessResult result) {
		// 只有复用主表时才需要更新总数量（新主表在创建时已设置）
		if (result.getTotalQuantityAdded() > 0 && entity.getTotalCount() != null) {
			// 判断是否为复用主表：如果当前总数量不等于新增数量，说明是复用的
			if (!result.getNewItems().isEmpty() || !result.getUpdateItems().isEmpty()) {
				entity.setTotalCount(entity.getTotalCount() + result.getTotalQuantityAdded());
				Assert.isTrue(updateById(entity), "更新备货单总数量失败");
			}
		}
	}

	/**
	 * 明细处理结果类
	 */
	@Getter
	private static class ItemProcessResult {
		private final List<ReplenishmentOrderItemEntity> newItems = new ArrayList<>();
		private final List<ReplenishmentOrderItemEntity> updateItems = new ArrayList<>();
		private int totalQuantityAdded = 0;

		public void addTotalQuantity(int quantity) {
			this.totalQuantityAdded += quantity;
		}
	}

	/**
	 * 查找可复用的备货主表
	 */
	private ReplenishmentOrderEntity findReusableOrder(ReplenishmentOrderDTO dto) {
		// 获取今天的开始和结束时间
		LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
		LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

		// 查询满足条件的备货主表
		List<ReplenishmentOrderEntity> candidateOrders = list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.eq(ReplenishmentOrderEntity::getSupplierId, dto.getSupplierId())
				.eq(ReplenishmentOrderEntity::getWarehouseId, dto.getWarehouseId())
				.eq(ReplenishmentOrderEntity::getSupplyWay, dto.getSupplyWay())
				.between(ReplenishmentOrderEntity::getCreateTime, todayStart, todayEnd)
				.eq(ReplenishmentOrderEntity::getIsDeleted, 0)
				.orderByDesc(ReplenishmentOrderEntity::getCreateTime)
		);

		if (candidateOrders.isEmpty()) {
			return null;
		}

		// 检查是否已入库
		List<Long> orderIds = candidateOrders.stream().map(ReplenishmentOrderEntity::getId).collect(Collectors.toList());

		// 查询已入库的备货单ID
		List<WarehouseStoreEntity> warehouseStores = warehouseStoreMapper.selectList(
			Wrappers.<WarehouseStoreEntity>lambdaQuery()
				.in(WarehouseStoreEntity::getRelatedOrderId, orderIds)
				.eq(WarehouseStoreEntity::getIsDeleted, 0)
		);

		Set<Long> storedOrderIds = warehouseStores.stream()
			.map(WarehouseStoreEntity::getRelatedOrderId)
			.collect(Collectors.toSet());

		// 返回第一个未入库的备货单
		for (ReplenishmentOrderEntity order : candidateOrders) {
			if (!storedOrderIds.contains(order.getId())) {
				return order;
			}
		}

		return null;
	}

	/**
	 * 构建明细的唯一标识
	 */
	private String buildItemKey(ReplenishmentOrderItemEntity item) {
		// SKU相同 + 同为整件采购价或同为每斤采购价 + 价格相同 + 运输品id相同 + 关联采购单商品详情ID
		String priceType = (item.getUnitPrice() != null) ? "unit" : "whole";
		String price = (item.getUnitPrice() != null) ? item.getUnitPrice().toString() :
			(item.getWholePrice() != null ? item.getWholePrice().toString() : "0");
		String transUnitId = item.getSupportTransUnitId() != null ? item.getSupportTransUnitId().toString() : "null";
		String purchaseOrderItemId = item.getPurchaseOrderItemId() != null ? item.getPurchaseOrderItemId().toString() : "null";

		return item.getProductSkuId() + "_" + priceType + "_" + price + "_" + transUnitId + "_" + purchaseOrderItemId;
	}

	/**
	 * 构建明细的唯一标识（DTO版本）
	 */
	private String buildItemKey(ReplenishmentOrderItemDTO item) {
		// SKU相同 + 同为整件采购价或同为每斤采购价 + 价格相同 + 运输品id相同 + 关联采购单商品详情ID
		String priceType = (item.getUnitPrice() != null) ? "unit" : "whole";
		String price = (item.getUnitPrice() != null) ? item.getUnitPrice().toString() :
			(item.getWholePrice() != null ? item.getWholePrice().toString() : "0");
		String transUnitId = item.getSupportTransUnitId() != null ? item.getSupportTransUnitId().toString() : "null";
		String purchaseOrderItemId = item.getPurchaseOrderItemId() != null ? item.getPurchaseOrderItemId().toString() : "null";

		return item.getProductSkuId() + "_" + priceType + "_" + price + "_" + transUnitId + "_" + purchaseOrderItemId;
	}

//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public void update(ReplenishmentOrderUpdateDTO dto) {
//		checkOrderItem(dto.getItemList());
//		ReplenishmentOrderEntity existsOrder = getById(dto.getId());
//		Assert.notNull(existsOrder, "备货单不存在");
//		Assert.isFalse(existsOrder.getStatus().equals(BusinessConstant.ENABLE_STATUS), "备货单已完成，无法继续操作");
//		// 1.更新备货单
//		ReplenishmentOrderEntity entity = new ReplenishmentOrderEntity();
//		entity.setId(dto.getId());
//		entity.setTotalCount(dto.totalCount());
//		entity.setStatus(dto.getStatus());
//		Assert.isTrue(updateById(entity), "更新备货单失败");
//
//		// 2.更新备货单商品明细
//		Assert.isTrue(replenishmentOrderItemService.updateBatchById(dto.convertItemList()), "更新备货单商品明细失败");
//
//		// 3.更新对应采购单数量及状态
//		if (entity.getStatus().equals(BusinessConstant.ENABLE_STATUS)) {
//			purchaseOrderService.updateStatusAndQuantity(existsOrder.getPurchaseOrderId(), dto.getItemList());
//		}
//	}

	/**
	 * 商品价格参数校验
	 */
	private String checkOrderItem(ReplenishmentOrderDTO dto) {
		List<ReplenishmentOrderItemDTO> itemList = dto.getItemList();
		boolean match = itemList.stream().anyMatch(i -> Func.isNull(i.getUnitPrice()) && Func.isNull(i.getWholePrice()));
		Assert.isFalse(match, () -> new ServiceException("整体和每斤价格不能同时为空"));

		// 判断sku与该供应商是否有关系
		List<Long> skuIds = itemList.stream().map(ReplenishmentOrderItemDTO::getProductSkuId).toList();
		List<Long> existsIds = skuSupplierRelationService.list(Wrappers.<SkuSupplierRelationEntity>lambdaQuery()
			.in(SkuSupplierRelationEntity::getSkuId, skuIds)
			.eq(SkuSupplierRelationEntity::getSupplierId, dto.getSupplierId())
		).stream().map(SkuSupplierRelationEntity::getSkuId).toList();

		if (itemList.stream().anyMatch(i -> !existsIds.contains(i.getProductSkuId()))) {
			StringBuilder sb = new StringBuilder();
			// 排除没有关系的sku
			List<ReplenishmentOrderItemDTO> filterList = new ArrayList<>();
			for (ReplenishmentOrderItemDTO itemDTO : itemList) {
				if (existsIds.contains(itemDTO.getProductSkuId())) {
					filterList.add(itemDTO);
					continue;
				}
				sb.append(" ").append(itemDTO.getSkuCode());
			}
			dto.setItemList(filterList);

			if (Func.isBlank(sb)) {
				return null;
			}

			return "选中供应商无法采购SKU:" + sb;
		}

		return null;
	}

	@Override
	public List<IdVO> replenishmentListBySupplierId(Long warehouseId, Long supplierId) {
		List<ReplenishmentOrderEntity> list = list(
			Wrappers.<ReplenishmentOrderEntity>lambdaQuery()
				.select(ReplenishmentOrderEntity::getId, ReplenishmentOrderEntity::getReplenishmentNo)
				.eq(ReplenishmentOrderEntity::getWarehouseId, warehouseId)
				.eq(ReplenishmentOrderEntity::getSupplierId, supplierId)
				.in(ReplenishmentOrderEntity::getStatus, ReplenishmentStatusEnum.PENDING.getCode(), ReplenishmentStatusEnum.PORTION.getCode())
		);
		return list.stream().map(e -> new IdVO(e.getId(), e.getReplenishmentNo())).toList();
	}

	@Override
	public List<ReplenishmentOrderAllocationVO> listAllocationDetail(Long purchaseOrderId, Long skuId) {
		List<ReplenishmentOrderAllocationVO> list = baseMapper.listByPurchaseOrderAndSku(purchaseOrderId, skuId);
		for (ReplenishmentOrderAllocationVO vo : list) {
			TransportUnitEntity unit = TransportUnitCache.getById(vo.getSupportTransUnitId());
			if (Func.notNull(unit)) {
				vo.setSupportTransUnitName(unit.getTransportName());
			}
		}
		return list;
	}

	@Override
	public List<SupplierReplenishmentOrderVO> listSupplierReplenishment(Long purchaseOrderId) {
		return baseMapper.listSupplierReplenishment(purchaseOrderId);
	}


	@Override
	public IPage<ReplenishmentListSupplierPageVO> listBySupplier(ReplenishmentListSupplierPageQueryDTO dto, IPage<ReplenishmentListSupplierPageDTO> page) {
		SupplierEntity supplier = supplierService.getSupplier();
		page.setRecords(baseMapper.listBySupplier(page, dto, supplier.getId()));

		IPage<ReplenishmentListSupplierPageVO> resPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
		if (page.getTotal() < 1) {
			return resPage;
		}

		resPage.setRecords(page.getRecords().stream().map(ReplenishmentListSupplierPageVO::build).toList());
		return resPage;
	}

	@Override
	public void complete(Long orderId) {
		ReplenishmentOrderEntity existsOrder = getById(orderId);
		Assert.notNull(existsOrder, "备货单不存在");
		Assert.isTrue(existsOrder.getStatus().equals(ReplenishmentStatusEnum.PENDING.getCode()), "备货单已完成，无法继续操作");
		Assert.isTrue(update(Wrappers.<ReplenishmentOrderEntity>lambdaUpdate()
			.set(ReplenishmentOrderEntity::getStatus, ReplenishmentStatusEnum.PORTION.getCode())
			.eq(ReplenishmentOrderEntity::getId, orderId)), "更新备货单失败");
	}

	@Override
	public void updateAndGenerateAmount(Long replenishmentId, List<WarehouseStoreItemEntity> itemEntityList) {
		Assert.notNull(replenishmentId, () -> new ServiceException("关联备货单标识不能为空"));
		ReplenishmentOrderEntity order = getById(replenishmentId);
		Assert.notNull(order, () -> new ServiceException("关联备货单不存在"));
		if (order.getStatus().equals(ReplenishmentStatusEnum.END.getCode())) {
			throw new ServiceException("备货单已完成，无法继续操作");
		}

		// 更新备货单明细入库状态
		replenishmentOrderItemService.updateStoreStatus(replenishmentId, itemEntityList.stream().map(WarehouseStoreItemEntity::getProductSkuId).toList());
		// 该备货单下全部入库完成则更新备货单状态
		if (!replenishmentOrderItemService.isAllStoreComplete(replenishmentId)) {
			Assert.isTrue(updateById(WarehouseHelper.replenishmentOrderEndStatus(replenishmentId)), () -> new ServiceException("关联备货单更新失败"));
		}
		if (SupplyWayEnum.DELIVERY.getCode().equals(order.getSupplyWay())) {
			return;
		}

		// 生成转运费流水
		transportFeeService.generateAmount(order.getReplenishmentNo(), itemEntityList);
	}

	@Override
	public byte[] generateReplenishmentOrderPdf(Long id) {
		// 1. 查询主单
		ReplenishmentOrderEntity order = this.getById(id);
		if (order == null) throw new ServiceException("备货单不存在");

		// 2. 查询供应商
		SupplierEntity supplier = supplierMapper.selectById(order.getSupplierId());
		// 3. 查询仓库
		WarehouseEntity warehouse = warehouseMapper.selectById(order.getWarehouseId());
		// 4. 查询采购单
		// 6. 查询明细
		List<ReplenishmentOrderItemEntity> itemEntities = replenishmentOrderItemMapper.selectList(
			new QueryWrapper<ReplenishmentOrderItemEntity>()
				.eq("replenishment_order_id", id)
				.eq("is_deleted", 0)
		);
		List<Long> orderIds = itemEntities.stream().map(i -> i.getReplenishmentOrderId()).distinct().toList();
		List<PurchaseOrderEntity> purchaseOrder = purchaseOrderMapper.selectByIds(orderIds);

		// 5. 查询采购员
		String purchaserName = "";
		for (PurchaseOrderEntity item : purchaseOrder) {
			if (purchaseOrder != null && item.getPurchaserId() != null) {
				User purchaser = userService.getById(item.getPurchaserId());
				if (Func.isEmpty(purchaserName)) {
					purchaserName = purchaser != null ? purchaser.getName() : "";
				} else {
					purchaserName = purchaserName + "," + purchaser != null ? purchaser.getName() : "";
				}
			}
		}
		// 7. 组装明细VO
		List<ReplenishmentOrderPrintItemVO> itemList = new ArrayList<>();
		BigDecimal totalAmount = BigDecimal.ZERO;
		int index = 1;
		for (ReplenishmentOrderItemEntity item : itemEntities) {
			ReplenishmentOrderPrintItemVO vo = new ReplenishmentOrderPrintItemVO();
			vo.setIndex(index++);
			vo.setProductName(item.getProductName());
			vo.setSpData(item.getSpData());
			vo.setQuantityDesc(item.getQuantity() != null ? String.valueOf(item.getQuantity()) : "");
			// 假设weight字段在sp_data或其他字段中，如有weight字段请替换
			vo.setWeightDesc(""); // 可扩展
			// 小计
			BigDecimal subtotal = BigDecimal.ZERO;
			if (item.getUnitPrice() != null && item.getQuantity() != null) {
				subtotal = item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity()));
			}
			vo.setSubtotalDesc(subtotal.toPlainString());
			totalAmount = totalAmount.add(subtotal);

			// 运输品
			vo.setTransportDesc(item.getSupportTransUnitId() != null ? getTransUnitName(item.getSupportTransUnitId()) : "");
			vo.setTransportNumDesc(item.getSupportTransNum() != null ? String.valueOf(item.getSupportTransNum()) : "");
			// 运输小计（如有相关价格字段可补充）
			vo.setTransportSubtotalDesc(""); // 可扩展
			vo.setItemNote(""); // 备注扩展
			itemList.add(vo);
		}

		// 8. 组装主VO
		ReplenishmentOrderPrintVO printVO = new ReplenishmentOrderPrintVO();
		printVO.setReplenishmentNo(order.getReplenishmentNo());
		printVO.setOrderDate(order.getCreateTime() != null ? new SimpleDateFormat("yyyy年MM月dd日").format(order.getCreateTime()) : "");
		printVO.setSupplierName(supplier != null ? supplier.getFullName() : "");
		printVO.setSupplierPhone(supplier != null ? supplier.getPhone() : "");
		printVO.setSupplierAddress(supplier != null ? supplier.getAddress() : "");
		printVO.setWarehouseName(warehouse != null ? warehouse.getWarehouseName() : "");
		printVO.setPurchaserName(purchaserName);
		printVO.setTotalAmount(totalAmount);
		printVO.setNote(order.getNote());
		printVO.setItemList(itemList);
		// 签字区
		printVO.setTransportSign("");
		printVO.setSupplierSign("");
		printVO.setWarehouseSign("");

		// 9. 渲染Word并转PDF
		String templatePath = "config/template/replenishment_order.docx"; // 模板路径
		Map<String, Object> dataMap = new HashMap<>(BeanUtil.toMap(printVO));
		// itemList为明细循环
		dataMap.put("itemList", printVO.getItemList());
		//return WordPdfUtil.renderAndExportPdf(templatePath, dataMap);
		return null;
	}

	/**
	 * 获取运输品名称（如有运输品字典/表可实现）
	 */
	private String getTransUnitName(Long supportTransUnitId) {
		// TODO: 查询运输品名称
		return "";
	}

	@Override
	public ByteArrayOutputStream print(ReplenishmentOrderPrintDTO dto) throws IOException {

		List<PrintSupplyOrderVO> voList = new ArrayList<>();

		List<Long> ids = Arrays.stream(dto.getReplenishmentOrderId().split(",")).map(i -> Long.valueOf(i)).distinct().toList();
		for (Long sid : ids) {
			// 备货单信息
			ReplenishmentOrderEntity order = getById(sid);
//			dto.setWarehouseId(order.getWarehouseId());
//			dto.setSupplierId(order.getSupplierId());

			//查询所以的采购单
			QueryWrapper<ReplenishmentOrderItemEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("replenishment_order_id", sid);
			queryWrapper.eq("is_deleted", 0);
			List<ReplenishmentOrderItemEntity> replenishmentOrderItemEntities = replenishmentOrderItemMapper.selectList(queryWrapper).stream().toList();
			List<Long> orderIds = replenishmentOrderItemEntities.stream().map(i -> i.getReplenishmentOrderId()).distinct().toList();
			if (order == null) {
				PrintSupplyOrderVO supplyOrderVO = new PrintSupplyOrderVO();
				return PDFServerUtil.PrintingSupplyOrder("供应商打印.pdf", new ArrayList<>());
			}
			// 供应商信息
			SupplierEntity supplier = supplierService.getById(order.getSupplierId());
			if (supplier == null) {
				throw new ServiceException("供应商不存在");
			}
			// 采购员信息
			List<PurchaseOrderEntity> purchaseOrder = purchaseOrderMapper.selectByIds(orderIds);
			String purchaserName = "";
			for (PurchaseOrderEntity item : purchaseOrder) {
				if (Func.isEmpty(purchaserName)) {
					purchaserName = Optional.ofNullable(item)
						.map(PurchaseOrderEntity::getPurchaserId)
						.map(UserCache::getUserName)
						.orElse("");
				} else {
					purchaserName = purchaserName + "," + Optional.ofNullable(item)
						.map(PurchaseOrderEntity::getPurchaserId)
						.map(UserCache::getUserName)
						.orElse("");
				}
			}

			//获取所有的Sku
			List<Long> longs = replenishmentOrderItemEntities.stream().map(i -> i.getProductSkuId()).toList();
			List<PStockVO> skuStocks = skuStockMapper.getPStock(longs);

			// 备货单明细
			List<ReplenishmentOrderItemVO> detailList = replenishmentOrderItemService.getSupplierItemDetailList(sid);
			PrintSupplyOrderVO vo = new PrintSupplyOrderVO();
			vo.setReplenishmentNo(order.getReplenishmentNo());
			vo.setSupplierName(supplier.getFullName());
			vo.setSupplierAddress(buildAddressD(supplier));
			vo.setSupplierPhone(supplier.getPhone());
			vo.setPurchaser(purchaserName);
			vo.setCreateTime(order.getCreateTime());
			vo.setOrderDate(order.getUpdateTime() != null ? new SimpleDateFormat("yyyy年MM月dd日").format(order.getUpdateTime()) : "");
			//重新复制给PrintSupplyOrderListVO
			List<PrintSupplyOrderListVO> printList = new ArrayList<>();
			for (ReplenishmentOrderItemVO detail : detailList) {
				PStockVO skuStock = skuStocks.stream().filter(sku -> sku.getId().equals(detail.getProductSkuId())).findFirst().orElse(null);
				if (skuStock == null) {
					return null;
				}
				String transportUnitName = detail.getSupportTransUnitName();
				Integer transportNum = detail.getSupportTransNum();
				PrintSupplyOrderListVO printVO = new PrintSupplyOrderListVO();
				if (Func.isNotEmpty(skuStock)) {
					printVO.setMetering(skuStock.getIsStandard() == 1 ? "非标品" : "标品");
				}
				printVO.setProductName(detail.getProductName());
				printVO.setPurchaseNumber(detail.getQuantity().toString());
				printVO.setPurchaseWeight(new BigDecimal(detail.getQuantity()).multiply(skuStock.getPackageGrossConversionRate()).toString());
				printVO.setTransport(transportUnitName != null ? transportUnitName : "");
				printVO.setOrderQuantity(transportNum != null ? "" + transportNum : "");
				printList.add(printVO);
			}
			//合计
			PrintSupplyOrderListVO newPrintVO = new PrintSupplyOrderListVO();
			newPrintVO.setProductName("合计");
			newPrintVO.setPurchaseNumber(printList.stream().filter(i -> Func.isNotEmpty(i.getPurchaseNumber())).mapToInt(i -> Integer.parseInt(i.getPurchaseNumber())).sum() + "");
			List<BigDecimal> pto = printList.stream().filter(i -> Func.isNotEmpty(i.getPurchaseWeight())).map(i -> new BigDecimal(i.getPurchaseWeight())).toList();
			for (BigDecimal i : pto) {
				newPrintVO.setPurchaseWeight((new BigDecimal(Func.isEmpty(newPrintVO.getPurchaseWeight()) ? "0" : newPrintVO.getPurchaseWeight()).add(i)).toString());
			}
			newPrintVO.setOrderQuantity(printList.stream().filter(i -> Func.isNotEmpty(i.getOrderQuantity())).mapToInt(i -> Integer.parseInt(i.getOrderQuantity())).sum() + "");
			printList.add(newPrintVO);
			vo.setData(printList);
			voList.add(vo);
		}

		try {
			return PrintingSupplyOrder.PrintingSupplyOrder("供应商打印.pdf", voList, ids);
		} catch (Exception e) {
			log.error("打印备货单失败", e);
			return null;
		}
	}

	/**
	 * 构建供应商地址
	 */
	private String buildAddress(SupplierEntity supplier) {
		Region region = RegionCache.getByCode(supplier.getRegion());
		if (ObjectUtil.isNotNull(region)) {
			if (StringUtil.equals(region.getProvinceName(), region.getCityName())) {
				return region.getCityName() + region.getDistrictName();
			} else {
				return region.getProvinceName() + region.getCityName() + region.getDistrictName();
			}
		}
		return "";
	}

	private String buildAddressD(SupplierEntity supplier) {
		Region region = RegionCache.getByCode(supplier.getRegion());
		if (ObjectUtil.isNotNull(region)) {
			if (StringUtil.equals(region.getProvinceName(), region.getCityName())) {
				return region.getCityName() + region.getDistrictName() + supplier.getAddress();
			} else {
				return region.getProvinceName() + region.getCityName() + region.getDistrictName() + supplier.getAddress();
			}
		} else {
			return supplier.getAddress();
		}
	}

	@Override
	public List<ReplenishmentOrderItemByTimeVO> listByTime(Long warehouseId, List<Long> orderWarehouseIds,String startTime, String endTime) {
		LocalDateTime startDateTime = DateTimeUtil.parseDate(startTime).atTime(LocalTime.MIN);
		LocalDateTime endDateTime = DateTimeUtil.parseDate(endTime).atTime(LocalTime.MAX);

		List<ReplenishmentOrderItemByTimeVO> list = baseMapper.listByTime(warehouseId, orderWarehouseIds, DateUtil.toMilliseconds(startDateTime), DateUtil.toMilliseconds(endDateTime));
		if (Func.isEmpty(list)) {
			return list;
		}

		// 设置商品信息
		productService.assembleProductVO(list);

		Set<Long> supplierIds = new HashSet<>();
		Set<Long> purchaseItemIds = new HashSet<>();
		for (ReplenishmentOrderItemByTimeVO vo : list) {
			supplierIds.add(vo.getSupplierId());
			purchaseItemIds.add(vo.getPurchaseOrderItemId());
		}

		// 查询供应商
		Map<Long, SupplierEntity> supplierMap = null;
		if (Func.isNotEmpty(supplierIds)) {
			supplierMap = supplierMapper.selectByIds(supplierIds)
				.stream()
				.collect(Collectors.toMap(SupplierEntity::getId, e -> e));
		}

		// 查询报缺数量
		Map<Long, Integer> missingMap = purchaseOrderItemService.listMissingByIds(purchaseItemIds);

		Set<Long> skuIds = new HashSet<>();
		for (ReplenishmentOrderItemByTimeVO vo : list) {
			skuIds.add(vo.getProductSkuId());
			vo.setMissingQuantity(missingMap.getOrDefault(vo.getPurchaseOrderItemId(), 0));
			TransportUnitEntity transportUnit = TransportUnitCache.getById(vo.getSupportTransUnitId());
			if (Func.notNull(transportUnit)) {
				vo.setSupportTransUnitName(transportUnit.getTransportName());
				vo.setSupportTransPrice(CommonUtil.ConvertIntBigDecimal(transportUnit.getPrice()));
			}
			if (Func.isNotEmpty(vo.getUnitPrice())) {
				vo.setUnitPrice(CommonUtil.ConvertToBigDecimal(vo.getUnitPrice()));
			}
			if (Func.isNotEmpty(vo.getWholePrice())) {
				vo.setWholePrice(CommonUtil.ConvertToBigDecimal(vo.getWholePrice()));
			}
			if (Func.isNotEmpty(supplierMap)) {
				SupplierEntity supplier = supplierMap.get(vo.getSupplierId());
				if (Func.notNull(supplier)) {
					vo.setSupplierName(supplier.getFullName());
					vo.setAddress(supplier.getAddress());
				}
			}
		}

		// 查询当前询价价格
		Map<Long, WarehouseStorePrice> skuMap = skuWarehouseRelationService.getManagerWarehouseStorePrices(warehouseId, new ArrayList<>(skuIds));
		if (Func.isEmpty(skuMap)) {
			return list;
		}

		// 设置当前询价价格
		for (ReplenishmentOrderItemByTimeVO vo : list) {
			WarehouseStorePrice sku = skuMap.get(vo.getProductSkuId());
			if (Func.isNull(sku)) {
				continue;
			}
			vo.setInquiry(sku.getPriceType(), sku.getPrice(), vo.getPackageGrossConversionRate());
		}
		return list;
	}

	@Override
	public ByteArrayOutputStream printReplenishmentOrder(PrintReplenishmentOrderDTO dto) throws IOException {
		List<ReplenishmentOrderItemByTimeVO> vos = listByTime(dto.getWarehouseId(), null, dto.getScreeningTime(), dto.getScreeningTime());
		List<PrintSupplierSupplyReceiptVO> list = new ArrayList<>();
		for (ReplenishmentOrderItemByTimeVO item : vos) {
			PrintSupplierSupplyReceiptVO vo = new PrintSupplierSupplyReceiptVO();
			vo.setSupplierName(item.getSupplierName());
			vo.setWarehouseName(item.getAddress());
			vo.setProductName(item.getProductName());
			vo.setSupplyOrderNo(item.getReplenishmentNo());
			vo.setOutboundNum(item.getQuantity());
//			vo.setInboundNum(item.getQuantity());
//			vo.setTotalWt(item.getPackageGrossConversionRate().multiply(BigDecimal.valueOf(item.getQuantity())));
			vo.setSupportTrans(item.getSupportTransUnitName());
			vo.setSupportTransNum(item.getSupportTransNum());
//			vo.setSupportTransInboundNum(item.getSupportTransNum());
			list.add(vo);
		}
		//获取仓库
		WarehouseEntity warehouse = warehouseMapper.selectById(dto.getWarehouseId());
		PrintSupplierSupplyReceiptDVO dvo = new PrintSupplierSupplyReceiptDVO();
		dvo.setWarehouseName(warehouse.getWarehouseName());
		dvo.setDate(dto.getScreeningTime());
		dvo.setAddress(warehouse.getRegionName() + warehouse.getAddress());

		List<String> strings = list.stream().map(i -> i.getSupplierName()).distinct().toList();

		return PDFServerUtil.SupplierSupplyReceipt("供应商供货入库单.pdf", dvo, list, strings);
	}

	@Override
	public ByteArrayOutputStream printTransferOrder(TransferOrderPrintDTO dto) throws IOException {

		switch (dto.getPrintType()) {
			case 1: //仓库出库-档口

				List<PrintTransferOrderWarehouseVO> listWarehouse = getPrintTransferOrderWarehouse(dto);
				PrintTransferOrderWarehouseVO vow = new PrintTransferOrderWarehouseVO();
				vow.setProductName("合计");
				vow.setQuantity(listWarehouse.stream().mapToInt(PrintTransferOrderWarehouseVO::getQuantity).sum());
				listWarehouse.add(vow);
				//总仓的名称
				try {
					Long parentId = warehouseMapper.selectById(dto.getWarehouseIds().get(0)).getParentId();
					String zc = warehouseMapper.selectById(parentId).getWarehouseName();
					List<String> warehouseNames = warehouseMapper.selectByIds(dto.getWarehouseIds()).stream().map(WarehouseEntity::getWarehouseName).toList();
					String dates = dto.getTransferDate() + " " + dto.getCutoffTime();
					// 将字符串解析为 LocalDateTime 对象（假设格式为 yyyy-MM-dd HH:mm）
					DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
					LocalDateTime localDateTime = LocalDateTime.parse(dates, inputFormatter);
					DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");
					String formattedDate = localDateTime.format(outputFormatter);
					for (PrintTransferOrderWarehouseVO tie:listWarehouse) {
						tie.setOutboundWeight(CommonUtil.WeightToBigDecimal(tie.getOutboundWeight()));
					}
					return PrintingTransferOrder.PrintTransferOrderWarehouse(listWarehouse, warehouseNames,zc, formattedDate);

				} catch (Exception e) {
					throw new ServiceException(e.getMessage());
				}

			case 2://仓库分拣-商品
				List<PrintTransferOrderProductVO> listProducts = getPrintTransferOrderProduct(dto);
				List<String> stringProduct = listProducts.stream().map(PrintTransferOrderProductVO::getProductName).distinct().toList();
				List<PrintTransferOrderProductVO> listProduct = new ArrayList<>();
				if (stringProduct.size() > 1) {
					for (String name : stringProduct) {
						PrintTransferOrderProductVO vo = new PrintTransferOrderProductVO();
						List<PrintTransferOrderProductVO> voList = listProducts.stream().filter(i -> i.getProductName().equals(name)).toList();
						listProduct.addAll(voList);
						vo.setProductName("小计");
						vo.setQuantity(voList.stream().mapToInt(PrintTransferOrderProductVO::getQuantity).sum());
						listProduct.add(vo);
					}
					PrintTransferOrderProductVO vo = new PrintTransferOrderProductVO();
					vo.setProductName("合计");
					vo.setQuantity(listProducts.stream().mapToInt(PrintTransferOrderProductVO::getQuantity).sum());
					listProduct.add(vo);
				} else {
					PrintTransferOrderProductVO vo = new PrintTransferOrderProductVO();
					vo.setProductName("合计");
					vo.setQuantity(listProducts.stream().mapToInt(PrintTransferOrderProductVO::getQuantity).sum());
					listProducts.add(vo);
				}
				if (listProduct.size() > 0) {
					listProducts = listProduct;
				}
				//总仓的名称
				try {
					Long parentId = warehouseMapper.selectById(dto.getWarehouseIds().get(0)).getParentId();
					String warehouseNames = warehouseMapper.selectById(parentId).getWarehouseName();
					String dates = dto.getTransferDate() + " " + dto.getCutoffTime();
					// 将字符串解析为 LocalDateTime 对象（假设格式为 yyyy-MM-dd HH:mm）
					DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
					LocalDateTime localDateTime = LocalDateTime.parse(dates, inputFormatter);
					DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");
					String formattedDate = localDateTime.format(outputFormatter);
					return PrintingTransferOrder.PrintTransferOrderProduct(listProducts, warehouseNames, formattedDate);

				} catch (Exception e) {
					throw new ServiceException(e.getMessage());
				}
			case 3://分配搬运
				List<PrintTransferOrderTransportVO> vos = getPrintTransferOrderTransport(dto);
				if (vos.size() == 0) {
					return null;
				}
				List<String> strings = vos.stream().map(PrintTransferOrderTransportVO::getSupplierName).distinct().toList();
				List<PrintTransferOrderTransportVO> list = new ArrayList<>();
				if (strings.size() > 1) {
					for (String name : strings) {
						PrintTransferOrderTransportVO vo = new PrintTransferOrderTransportVO();
						List<PrintTransferOrderTransportVO> voList = vos.stream().filter(i -> i.getSupplierName().equals(name)).toList();
						list.addAll(voList);
						vo.setSupplierName("小计");
						vo.setTotal(voList.stream().mapToInt(PrintTransferOrderTransportVO::getTotal).sum());
						list.add(vo);
					}
					PrintTransferOrderTransportVO vo = new PrintTransferOrderTransportVO();
					vo.setSupplierName("合计");
					vo.setTotal(vos.stream().mapToInt(PrintTransferOrderTransportVO::getTotal).sum());
					list.add(vo);
				} else {
					PrintTransferOrderTransportVO vo = new PrintTransferOrderTransportVO();
					vo.setSupplierName("合计");
					vo.setTotal(vos.stream().mapToInt(PrintTransferOrderTransportVO::getTotal).sum());
					vos.add(vo);
				}
				if (list.size() > 0) {
					vos = list;
				}

				//总仓的名称
				try {
					Long parentId = warehouseMapper.selectById(dto.getWarehouseIds().get(0)).getParentId();
					String warehouseNames = warehouseMapper.selectById(parentId).getWarehouseName();
					String dates = dto.getTransferDate() + " " + dto.getCutoffTime();
					// 将字符串解析为 LocalDateTime 对象（假设格式为 yyyy-MM-dd HH:mm）
					DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
					LocalDateTime localDateTime = LocalDateTime.parse(dates, inputFormatter);
					DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");
					String formattedDate = localDateTime.format(outputFormatter);
					return PrintingTransferOrder.PrintTransferOrderTransport(vos, warehouseNames, formattedDate);

				} catch (Exception e) {
					throw new ServiceException(e.getMessage());
				}
			default:
				break;
		}

		return null;
	}

	/**
	 * 仓库出库-商品
	 *
	 * @param dto
	 * @return
	 */
	private List<PrintTransferOrderProductVO> getPrintTransferOrderProduct(TransferOrderPrintDTO dto) {

		if (Func.isEmpty(dto.getWarehouseIds())) {
			return new ArrayList<>();
		}
		List<PrintTransferOrderProductVO> vos = baseMapper.getPrintTransferOrderProduct(dto);

		return vos;
	}

	/**
	 * 仓库出库-运输
	 *
	 * @param dto
	 * @return
	 */
	private List<PrintTransferOrderTransportVO> getPrintTransferOrderTransport(TransferOrderPrintDTO dto) {
		if (Func.isEmpty(dto.getWarehouseIds())) {
			return new ArrayList<>();
		}
		List<PrintTransferOrderTransportVO> vos = baseMapper.getPrintTransferOrderTransport(dto);
		return vos;
	}

	/**
	 * 仓库出库-仓库
	 *
	 * @param dto
	 * @return
	 */
	private List<PrintTransferOrderWarehouseVO> getPrintTransferOrderWarehouse(TransferOrderPrintDTO dto) {

		if (Func.isEmpty(dto.getWarehouseIds())) {
			return new ArrayList<>();
		}
		List<PrintTransferOrderWarehouseVO> vos = baseMapper.getPrintTransferOrderWarehouse(dto);

		return vos;
	}
}
