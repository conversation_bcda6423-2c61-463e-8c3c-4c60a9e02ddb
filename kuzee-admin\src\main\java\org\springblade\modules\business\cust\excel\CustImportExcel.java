package org.springblade.modules.business.cust.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客户导入Excel实体类
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustImportExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户编号")
	private String code;

	/**
	 * 客户名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户名称")
	private String custName;

	/**
	 * 联系人
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户联系人")
	private String contacts;

	/**
	 * 手机号
	 */
	@ColumnWidth(20)
	@ExcelProperty("电话")
	private String phone;

	/**
	 * 收货地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货地址")
	private String regionName;

	/**
	 * 详细地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("详细地址")
	private String address;

	/**
	 * 分配区域（档口）
	 */
	@ColumnWidth(20)
	@ExcelProperty("分配区域（档口）")
	private String warehouseName;
}
