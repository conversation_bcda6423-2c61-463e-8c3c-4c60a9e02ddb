package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderAfterSalesStatusEnum {
	//	业务类型（充值，支付，退差，退单）
	PROCUREMENT_PENDING(0, "待采购确认"),
	FINANCE_PENDING(1, "待财务确认"),
	PENDING_REFUND(2, "待退差"),
	COMPLETED(3, "已完成"),
	WITHDRAW(4, "取消申请"),
	REJECT_APPLICATION(5, "驳回申请");

	private final Integer code;
	private final String message;
}
