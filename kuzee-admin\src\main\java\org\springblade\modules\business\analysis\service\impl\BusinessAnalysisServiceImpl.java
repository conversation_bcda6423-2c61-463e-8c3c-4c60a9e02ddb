/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.analysis.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.business.analysis.mapper.BusinessAnalysisMapper;
import org.springblade.modules.business.analysis.pojo.dto.AnalysisQueryDTO;
import org.springblade.modules.business.analysis.pojo.vo.*;
import org.springblade.modules.business.analysis.service.IBusinessAnalysisService;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.Cacheable;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDate;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 业务分析服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class BusinessAnalysisServiceImpl implements IBusinessAnalysisService {

    private static final int DECIMAL_SCALE = 2;

    private final BusinessAnalysisMapper businessAnalysisMapper;

    @Override
    public BusinessAnalysisVO getBusinessAnalysis(AnalysisQueryDTO queryDTO) {
        // 创建返回结果对象
        BusinessAnalysisVO result = new BusinessAnalysisVO();

        try {
            // 计算实际销售金额(含框)
            // 计算公式：询价+公式加价+产品加价+服务费+配送费加价+订单中框的价格
            BigDecimal actualSaleAmountWithTax = businessAnalysisMapper.calculateActualSaleAmountWithTax(queryDTO);
            result.setActualSaleAmountWithTax(actualSaleAmountWithTax);

            // 计算实际销售金额(不含框)
            // 计算公式：询价+公式加价+产品加价+服务费+配送费加价
            BigDecimal actualSaleAmountWithoutTax = businessAnalysisMapper.calculateActualSaleAmountWithoutTax(queryDTO);
            result.setActualSaleAmountWithoutTax(actualSaleAmountWithoutTax);

            // 计算基础产品金额(含税)
            BigDecimal baseProductAmountWithTax = businessAnalysisMapper.calculateBaseProductAmountWithTax(queryDTO);
            result.setBaseProductAmountWithTax(baseProductAmountWithTax);

            // 计算基础产品金额(不含税)
            BigDecimal baseProductAmountWithoutTax = businessAnalysisMapper.calculateBaseProductAmountWithoutTax(queryDTO);
            result.setBaseProductAmountWithoutTax(baseProductAmountWithoutTax);

            // 计算采购金额
            BigDecimal purchaseAmount = businessAnalysisMapper.calculatePurchaseAmount(queryDTO);
            result.setPurchaseAmount(purchaseAmount);

            // 计算配送成本
            // 计算公式：出库单上的配送成本总和
            BigDecimal deliveryCost = businessAnalysisMapper.calculateDeliveryCost(queryDTO);
            result.setDeliveryCost(deliveryCost);

            // 计算转运费
            // 计算公式：入库单对应的转运费
            BigDecimal transferFee = businessAnalysisMapper.calculateTransferFee(queryDTO);
            result.setTransferFee(transferFee);

            // 计算售后金额
            // 计算公式：实际给客户退款金额+文员单独给客户补偿金额
            BigDecimal afterSaleAmount = businessAnalysisMapper.calculateAfterSaleAmount(queryDTO);
            result.setAfterSaleAmount(afterSaleAmount);

            // 计算成本金额
            // 计算公式：采购金额+配送成本
            BigDecimal costAmount = purchaseAmount.add(deliveryCost);
            result.setCostAmount(costAmount);

            // 计算订单总额（已截单的订单总额）
            BigDecimal orderTotalAmount = businessAnalysisMapper.calculateOrderTotalAmount(queryDTO);

            // 计算售后找回金额
            // 计算公式：供应商罚款+供应商退货金额
            BigDecimal afterSaleReturnAmount = businessAnalysisMapper.calculateAfterSaleReturnAmount(queryDTO);
           	result.setAfterSaleReturnAmount(afterSaleReturnAmount);

            // 计算卖框金额
            BigDecimal priceDifferenceAmount = businessAnalysisMapper.calculatePriceDifferenceAmount(queryDTO);

            // 计算毛利
            // 计算公式：[截单的订单总额]-[成本金额]-[售后金额]+[售后找回金额]+[卖框金额]
            BigDecimal grossProfit = orderTotalAmount
                .subtract(costAmount)
                .subtract(afterSaleAmount)
                .add(afterSaleReturnAmount)
                .add(priceDifferenceAmount);
            result.setGrossProfit(grossProfit);

            // 获取订单时间分布数据
            List<TimeDistributionVO> orderDistribution = getOrderTimeDistribution(queryDTO);
            result.setOrderDistribution(orderDistribution);

			Query defaultQuery = new Query();
			defaultQuery.setCurrent(1);
			defaultQuery.setSize(5);

            // 获取热销商品数据
			IPage<HotProductVO> hotProducts = getHotProducts(queryDTO, defaultQuery);
            result.setHotProducts(hotProducts.getRecords());

            // 获取热销品类数据
			IPage<HotCategoryVO> hotCategories = getHotCategories(queryDTO, defaultQuery);
            result.setHotCategories(hotCategories.getRecords());

            // 获取区域销售数据
            List<AreaSalesVO> areaSales = getAreaSales(queryDTO);
            result.setAreaSales(areaSales);

            // 获取客户订单数量排行
			IPage<CustomerRankingVO> customerOrderRanking = getCustomerOrderRanking(queryDTO, defaultQuery);
            result.setCustomerOrderRanking(customerOrderRanking.getRecords());

            // 获取客户销售金额排行
			IPage<CustomerRankingVO> customerSalesRanking = getCustomerSalesRanking(queryDTO, defaultQuery);
            result.setCustomerSalesRanking(customerSalesRanking.getRecords());

            // 获取客户售后金额排行
			IPage<CustomerRankingVO> customerAfterSaleRanking = getCustomerAfterSaleRanking(queryDTO, defaultQuery);
            result.setCustomerAfterSaleRanking(customerAfterSaleRanking.getRecords());

        } catch (Exception e) {
            log.error("计算业务分析数据异常", e);
            // 发生异常时返回默认值
            setDefaultValues(result);
        }

        return result;
    }

    @Override
    public List<TimeDistributionVO> getOrderTimeDistribution(AnalysisQueryDTO queryDTO) {
        try {
            return businessAnalysisMapper.getOrderTimeDistribution(queryDTO);
        } catch (Exception e) {
            log.error("获取订单时间分布数据异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public IPage<HotProductVO> getHotProducts(AnalysisQueryDTO queryDTO, Query query) {
        try {
			IPage<HotProductVO> page = new Page<>(query.getCurrent(), query.getSize());
            return businessAnalysisMapper.getHotProducts(page, queryDTO);
        } catch (Exception e) {
            log.error("获取热销商品数据异常", e);
            return new Page<>();
        }
    }

    @Override
    public IPage<HotCategoryVO> getHotCategories(AnalysisQueryDTO queryDTO, Query query) {
        try {
			IPage<HotCategoryVO> page = new Page<>(query.getCurrent(), query.getSize());
            return businessAnalysisMapper.getHotCategories(page, queryDTO);
        } catch (Exception e) {
            log.error("获取热销品类数据异常", e);
            return new Page<>();
        }
    }

    @Override
    public List<AreaSalesVO> getAreaSales(AnalysisQueryDTO queryDTO) {
        try {
            return businessAnalysisMapper.getAreaSales(queryDTO);
        } catch (Exception e) {
            log.error("获取区域销售分析数据异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public IPage<CustomerRankingVO> getCustomerOrderRanking(AnalysisQueryDTO queryDTO, Query query) {
        try {
			IPage<CustomerRankingVO> page = new Page<>(query.getCurrent(), query.getSize());
            return businessAnalysisMapper.getCustomerOrderRanking(page, queryDTO);
        } catch (Exception e) {
            log.error("获取客户订单数量排行异常", e);
            return new Page<>();
        }
    }

    @Override
    public IPage<CustomerRankingVO> getCustomerSalesRanking(AnalysisQueryDTO queryDTO, Query query) {
        try {
			IPage<CustomerRankingVO> page = new Page<>(query.getCurrent(), query.getSize());
            return businessAnalysisMapper.getCustomerSalesRanking(page, queryDTO);
        } catch (Exception e) {
            log.error("获取客户销售金额排行异常", e);
            return new Page<>();
        }
    }

    @Override
    public IPage<CustomerRankingVO> getCustomerAfterSaleRanking(AnalysisQueryDTO queryDTO, Query query) {
        try {
			IPage<CustomerRankingVO> page = new Page<>(query.getCurrent(), query.getSize());
            return businessAnalysisMapper.getCustomerAfterSaleRanking(page, queryDTO);
        } catch (Exception e) {
            log.error("获取客户售后金额排行异常", e);
            return new Page<>();
        }
    }

    /**
     * 设置默认值
     *
     * @param result 业务分析结果对象
     */
    private void setDefaultValues(BusinessAnalysisVO result) {
        result.setActualSaleAmountWithTax(BigDecimal.ZERO);
        result.setActualSaleAmountWithoutTax(BigDecimal.ZERO);
        result.setBaseProductAmountWithTax(BigDecimal.ZERO);
        result.setBaseProductAmountWithoutTax(BigDecimal.ZERO);
        result.setPurchaseAmount(BigDecimal.ZERO);
        result.setDeliveryCost(BigDecimal.ZERO);
        result.setTransferFee(BigDecimal.ZERO);
        result.setAfterSaleAmount(BigDecimal.ZERO);
        result.setAfterSaleReturnAmount(BigDecimal.ZERO);
        result.setCostAmount(BigDecimal.ZERO);
        result.setGrossProfit(BigDecimal.ZERO);
        result.setOrderDistribution(new ArrayList<>());
        result.setHotProducts(new ArrayList<>());
        result.setHotCategories(new ArrayList<>());
        result.setAreaSales(new ArrayList<>());
        result.setCustomerOrderRanking(new ArrayList<>());
        result.setCustomerSalesRanking(new ArrayList<>());
        result.setCustomerAfterSaleRanking(new ArrayList<>());
    }

    /**
     * 获取所有金额汇总数据
     * 直接从Mapper层获取数据，避免构建完整BusinessAnalysisVO对象的开销
     *
     * @param queryDTO 查询条件
     * @return 金额汇总数据
     */
    @Override
   // @Cacheable(value = "business_analysis_amounts", key = "#queryDTO.startDate + '_' + #queryDTO.endDate + '_' + (#queryDTO.warehouseIds != null ? #queryDTO.warehouseIds : 'all')")
    public AmountsDataVO getAmountsData(AnalysisQueryDTO queryDTO) {
        AmountsDataVO vo = new AmountsDataVO();

        try {
            // 获取当前周期数据
            Map<String, Object> currentData = businessAnalysisMapper.getAmountsData(queryDTO);

            // 计算前一周期的时间范围
            LocalDate startDate = queryDTO.getStartDate();
            LocalDate endDate = queryDTO.getEndDate();
            if (startDate == null || endDate == null) {
                setDefaultAmountsValues(vo);
                return vo;
            }
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
            LocalDate previousStartDate = startDate.minusDays(daysBetween);
            LocalDate previousEndDate = startDate.minusDays(1);
            queryDTO.setStartDate(previousStartDate);
            queryDTO.setEndDate(previousEndDate);

            // 获取前一周期数据
            Map<String, Object> previousData = businessAnalysisMapper.getAmountsData(queryDTO);

            // 恢复原始查询时间
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);

            // 填充VO
            populateAmountsData(vo, currentData, previousData);

        } catch (Exception e) {
            log.error("获取金额数据异常", e);
            setDefaultAmountsValues(vo);
        }

        return vo;
    }

    private void populateAmountsData(AmountsDataVO vo, Map<String, Object> currentData, Map<String, Object> previousData) {
        // 设置各项金额、上期值、环比和差值
        setBigDecimalValueWithAllFields(currentData, previousData, "actual_sale_amount_with_tax",
            vo::setActualSaleAmountWithTax, vo::setPreviousActualSaleAmountWithTax,
            vo::setActualSaleAmountWithTaxRatio, vo::setActualSaleAmountWithTaxDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "actual_sale_amount_without_tax",
            vo::setActualSaleAmountWithoutTax, vo::setPreviousActualSaleAmountWithoutTax,
            vo::setActualSaleAmountWithoutTaxRatio, vo::setActualSaleAmountWithoutTaxDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "base_product_amount_with_tax",
            vo::setBaseProductAmountWithTax, vo::setPreviousBaseProductAmountWithTax,
            vo::setBaseProductAmountWithTaxRatio, vo::setBaseProductAmountWithTaxDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "base_product_amount_without_tax",
            vo::setBaseProductAmountWithoutTax, vo::setPreviousBaseProductAmountWithoutTax,
            vo::setBaseProductAmountWithoutTaxRatio, vo::setBaseProductAmountWithoutTaxDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "purchase_amount",
            vo::setPurchaseAmount, vo::setPreviousPurchaseAmount,
            vo::setPurchaseAmountRatio, vo::setPurchaseAmountDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "delivery_cost",
            vo::setDeliveryCost, vo::setPreviousDeliveryCost,
            vo::setDeliveryCostRatio, vo::setDeliveryCostDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "transfer_fee",
            vo::setTransferFee, vo::setPreviousTransferFee,
            vo::setTransferFeeRatio, vo::setTransferFeeDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "after_sale_amount",
            vo::setAfterSaleAmount, vo::setPreviousAfterSaleAmount,
            vo::setAfterSaleAmountRatio, vo::setAfterSaleAmountDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "supplier_fine_amount",
            vo::setSupplierFineAmount, vo::setPreviousSupplierFineAmount,
            vo::setSupplierFineAmountRatio, vo::setSupplierFineAmountDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "supplier_return_amount",
            vo::setSupplierReturnAmount, vo::setPreviousSupplierReturnAmount,
            vo::setSupplierReturnAmountRatio, vo::setSupplierReturnAmountDifference);

        setBigDecimalValueWithAllFields(currentData, previousData, "selling_box_amount",
            vo::setSellingBoxAmount, vo::setPreviousSellingBoxAmount,
            vo::setSellingBoxAmountRatio, vo::setSellingBoxAmountDifference);

        // 计算并设置成本金额
        BigDecimal currentCostAmount = vo.getPurchaseAmount().add(vo.getDeliveryCost());
        BigDecimal previousCostAmount = vo.getPreviousPurchaseAmount().add(vo.getPreviousDeliveryCost());
        BigDecimal costAmountDifference = currentCostAmount.subtract(previousCostAmount);

        vo.setCostAmount(currentCostAmount);
        vo.setPreviousCostAmount(previousCostAmount);
        vo.setCostAmountRatio(calculateRatio(currentCostAmount, previousCostAmount));
        vo.setCostAmountDifference(costAmountDifference);

        // 计算并设置毛利
        BigDecimal afterSaleRecoveryAmount = vo.getSupplierFineAmount().add(vo.getSupplierReturnAmount());
        BigDecimal currentProfit = vo.getActualSaleAmountWithTax()
            .subtract(currentCostAmount)
            .subtract(vo.getAfterSaleAmount())
            .add(afterSaleRecoveryAmount)
            .add(vo.getSellingBoxAmount());

        BigDecimal previousAfterSaleRecoveryAmount = vo.getPreviousSupplierFineAmount().add(vo.getPreviousSupplierReturnAmount());
        BigDecimal previousProfit = vo.getPreviousActualSaleAmountWithTax()
            .subtract(previousCostAmount)
            .subtract(vo.getPreviousAfterSaleAmount())
            .add(previousAfterSaleRecoveryAmount)
            .add(vo.getPreviousSellingBoxAmount());

        BigDecimal profitDifference = currentProfit.subtract(previousProfit);

        vo.setProfit(currentProfit);
        vo.setPreviousProfit(previousProfit);
        vo.setProfitRatio(calculateRatio(currentProfit, previousProfit));
        vo.setProfitDifference(profitDifference);
    }


    /**
     * 设置BigDecimal值及其环比 (金额单位: 分 -> 元)
     *
     * @param currentData 当前周期数据
     * @param previousData 上一周期数据
     * @param key 数据键名
     * @param valueSetter 设置值的函数
     * @param ratioSetter 设置环比的函数
     */
    private void setBigDecimalValueWithRatio(Map<String, Object> currentData, Map<String, Object> previousData, String key,
                                           Consumer<BigDecimal> valueSetter,
                                           Consumer<BigDecimal> ratioSetter) {

        BigDecimal currentValue = getBigDecimalFromMap(currentData, key);
        BigDecimal previousValue = getBigDecimalFromMap(previousData, key);

        if (valueSetter != null) {
            valueSetter.accept(currentValue);
        }
        if (ratioSetter != null) {
            ratioSetter.accept(calculateRatio(currentValue, previousValue));
        }
    }

    /**
     * 设置BigDecimal值、上期值、环比和差值 (金额单位: 分 -> 元)
     *
     * @param currentData 当前周期数据
     * @param previousData 上一周期数据
     * @param key 数据键名
     * @param valueSetter 设置当前值的函数
     * @param previousValueSetter 设置上期值的函数
     * @param ratioSetter 设置环比的函数
     * @param differenceSetter 设置差值的函数
     */
    private void setBigDecimalValueWithAllFields(Map<String, Object> currentData, Map<String, Object> previousData, String key,
                                               Consumer<BigDecimal> valueSetter,
                                               Consumer<BigDecimal> previousValueSetter,
                                               Consumer<BigDecimal> ratioSetter,
                                               Consumer<BigDecimal> differenceSetter) {

        BigDecimal currentValue = getBigDecimalFromMap(currentData, key);
        BigDecimal previousValue = getBigDecimalFromMap(previousData, key);
        BigDecimal difference = currentValue.subtract(previousValue);

        if (valueSetter != null) {
            valueSetter.accept(currentValue);
        }
        if (previousValueSetter != null) {
            previousValueSetter.accept(previousValue);
        }
        if (ratioSetter != null) {
            ratioSetter.accept(calculateRatio(currentValue, previousValue));
        }
        if (differenceSetter != null) {
            differenceSetter.accept(difference);
        }
    }

	private BigDecimal getBigDecimalFromMap(Map<String, Object> dataMap, String key) {
		if (dataMap == null) {
			dataMap = new java.util.HashMap<>();
		}
		BigDecimal valueInCents = BigDecimal.ZERO;
		if (dataMap.containsKey(key) && dataMap.get(key) != null) {
			try {
				valueInCents = new BigDecimal(String.valueOf(dataMap.get(key)));
			} catch (NumberFormatException e) {
				log.warn("无法将 {} 转换为BigDecimal: {}", key, dataMap.get(key));
			}
		}
		// 从分转换为元
		return valueInCents.divide(new BigDecimal("100"), DECIMAL_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 设置Integer值及其环比
     *
     * @param currentData 当前周期数据
     * @param previousData 上一周期数据
     * @param key 数据键名
     * @param valueSetter 设置值的函数
     * @param ratioSetter 设置环比的函数
     */
    private void setIntegerValueWithRatio(Map<String, Object> currentData, Map<String, Object> previousData, String key,
                                        java.util.function.Consumer<Integer> valueSetter,
                                        java.util.function.Consumer<BigDecimal> ratioSetter) {
        if (currentData == null) {
            currentData = new java.util.HashMap<>();
        }
        if (previousData == null) {
            previousData = new java.util.HashMap<>();
        }

        // 获取当前值，如果不存在则默认为0
        Integer currentValue = 0;
        if (currentData.containsKey(key) && currentData.get(key) != null) {
            try {
                currentValue = Integer.parseInt(String.valueOf(currentData.get(key)));
            } catch (NumberFormatException e) {
                log.warn("无法将 {} 转换为Integer: {}", key, currentData.get(key));
            }
        }

        // 获取前一周期值，如果不存在则默认为0
        Integer previousValue = 0;
        if (previousData.containsKey(key) && previousData.get(key) != null) {
            try {
                previousValue = Integer.parseInt(String.valueOf(previousData.get(key)));
            } catch (NumberFormatException e) {
                log.warn("无法将 {} 转换为Integer: {}", key, previousData.get(key));
            }
        }

        // 设置当前值
        if (valueSetter != null) {
            valueSetter.accept(currentValue);
        }

        // 计算并设置环比
        if (ratioSetter != null) {
            ratioSetter.accept(calculateRatio(new BigDecimal(currentValue), new BigDecimal(previousValue)));
        }
    }

    /**
     * 计算环比值
     * 公式：(当期值-上期值)/上期值*100%
     * 如果上期值为0，当期值大于0则返回100%，当期值为0则返回0%
     *
     * @param current 当期值
     * @param previous 上期值
     * @return 环比百分比
     */
    private BigDecimal calculateRatio(BigDecimal current, BigDecimal previous) {
        if (current == null) {
            current = BigDecimal.ZERO;
        }

        if (previous == null) {
            previous = BigDecimal.ZERO;
        }

        if (previous.compareTo(BigDecimal.ZERO) == 0) {
            return current.compareTo(BigDecimal.ZERO) > 0 ? new BigDecimal("100") : BigDecimal.ZERO;
        }

        try {
            return current.subtract(previous)
                .divide(previous, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("计算环比异常：current={}, previous={}", current, previous, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 设置默认值
     * 当发生异常时，所有数值设为0
     *
     * @param vo 金额汇总数据对象
     */
    private void setDefaultAmountsValues(AmountsDataVO vo) {
        // 实际销售金额(含框)
        vo.setActualSaleAmountWithTax(BigDecimal.ZERO);
        vo.setActualSaleAmountWithTaxRatio(BigDecimal.ZERO);
        vo.setPreviousActualSaleAmountWithTax(BigDecimal.ZERO);
        vo.setActualSaleAmountWithTaxDifference(BigDecimal.ZERO);

        // 实际销售金额(不含框)
        vo.setActualSaleAmountWithoutTax(BigDecimal.ZERO);
        vo.setActualSaleAmountWithoutTaxRatio(BigDecimal.ZERO);
        vo.setPreviousActualSaleAmountWithoutTax(BigDecimal.ZERO);
        vo.setActualSaleAmountWithoutTaxDifference(BigDecimal.ZERO);

        // 基础产品金额(含框)
        vo.setBaseProductAmountWithTax(BigDecimal.ZERO);
        vo.setBaseProductAmountWithTaxRatio(BigDecimal.ZERO);
        vo.setPreviousBaseProductAmountWithTax(BigDecimal.ZERO);
        vo.setBaseProductAmountWithTaxDifference(BigDecimal.ZERO);

        // 基础产品金额(不含框)
        vo.setBaseProductAmountWithoutTax(BigDecimal.ZERO);
        vo.setBaseProductAmountWithoutTaxRatio(BigDecimal.ZERO);
        vo.setPreviousBaseProductAmountWithoutTax(BigDecimal.ZERO);
        vo.setBaseProductAmountWithoutTaxDifference(BigDecimal.ZERO);

        // 采购金额
        vo.setPurchaseAmount(BigDecimal.ZERO);
        vo.setPurchaseAmountRatio(BigDecimal.ZERO);
        vo.setPreviousPurchaseAmount(BigDecimal.ZERO);
        vo.setPurchaseAmountDifference(BigDecimal.ZERO);

        // 配送成本
        vo.setDeliveryCost(BigDecimal.ZERO);
        vo.setDeliveryCostRatio(BigDecimal.ZERO);
        vo.setPreviousDeliveryCost(BigDecimal.ZERO);
        vo.setDeliveryCostDifference(BigDecimal.ZERO);

        // 转运费
        vo.setTransferFee(BigDecimal.ZERO);
        vo.setTransferFeeRatio(BigDecimal.ZERO);
        vo.setPreviousTransferFee(BigDecimal.ZERO);
        vo.setTransferFeeDifference(BigDecimal.ZERO);

        // 售后金额
        vo.setAfterSaleAmount(BigDecimal.ZERO);
        vo.setAfterSaleAmountRatio(BigDecimal.ZERO);
        vo.setPreviousAfterSaleAmount(BigDecimal.ZERO);
        vo.setAfterSaleAmountDifference(BigDecimal.ZERO);

        // 供应商罚款金额
        vo.setSupplierFineAmount(BigDecimal.ZERO);
        vo.setSupplierFineAmountRatio(BigDecimal.ZERO);
        vo.setPreviousSupplierFineAmount(BigDecimal.ZERO);
        vo.setSupplierFineAmountDifference(BigDecimal.ZERO);

        // 供应商退货金额
        vo.setSupplierReturnAmount(BigDecimal.ZERO);
        vo.setSupplierReturnAmountRatio(BigDecimal.ZERO);
        vo.setPreviousSupplierReturnAmount(BigDecimal.ZERO);
        vo.setSupplierReturnAmountDifference(BigDecimal.ZERO);

        // 卖框金额
        vo.setSellingBoxAmount(BigDecimal.ZERO);
        vo.setSellingBoxAmountRatio(BigDecimal.ZERO);
        vo.setPreviousSellingBoxAmount(BigDecimal.ZERO);
        vo.setSellingBoxAmountDifference(BigDecimal.ZERO);

        // 成本金额
        vo.setCostAmount(BigDecimal.ZERO);
        vo.setCostAmountRatio(BigDecimal.ZERO);
        vo.setPreviousCostAmount(BigDecimal.ZERO);
        vo.setCostAmountDifference(BigDecimal.ZERO);

        // 毛利
        vo.setProfit(BigDecimal.ZERO);
        vo.setProfitRatio(BigDecimal.ZERO);
        vo.setPreviousProfit(BigDecimal.ZERO);
        vo.setProfitDifference(BigDecimal.ZERO);
    }

    /**
     * 获取毛利分析数据
     * 只计算与毛利相关的数据，减少不必要的计算
     *
     * @param queryDTO 查询条件
     * @return 毛利相关数据
     */
    @Override
    @Cacheable(value = "business_analysis_profit", key = "#queryDTO.startDate + '_' + #queryDTO.endDate + '_' + (#queryDTO.warehouseIds != null ? #queryDTO.warehouseIds : 'all')")
    public ProfitAnalysisVO getProfitAnalysis(AnalysisQueryDTO queryDTO) {
        ProfitAnalysisVO result = new ProfitAnalysisVO();

        try {
            // 计算实际销售金额（含框）
            BigDecimal actualSaleAmountWithTax = businessAnalysisMapper.calculateActualSaleAmountWithTax(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);
            result.setActualSaleAmountWithTax(actualSaleAmountWithTax);

            // 计算售后金额
            BigDecimal afterSaleAmount = businessAnalysisMapper.calculateAfterSaleAmount(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);
            result.setAfterSaleAmount(afterSaleAmount);

            // 计算成本金额
            BigDecimal purchaseAmount = businessAnalysisMapper.calculatePurchaseAmount(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);
            BigDecimal deliveryCost = businessAnalysisMapper.calculateDeliveryCost(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);

            BigDecimal costAmount = purchaseAmount.add(deliveryCost);
            result.setCostAmount(costAmount.setScale(DECIMAL_SCALE, RoundingMode.HALF_UP));

            // 计算售后找回金额
            BigDecimal afterSaleReturnAmount = businessAnalysisMapper.calculateAfterSaleReturnAmount(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);

            // 计算卖框金额
            BigDecimal priceDifferenceAmount = businessAnalysisMapper.calculatePriceDifferenceAmount(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);

            // 计算订单总额（已截单的订单总额）
            BigDecimal orderTotalAmount = businessAnalysisMapper.calculateOrderTotalAmount(queryDTO)
                .setScale(DECIMAL_SCALE, RoundingMode.HALF_UP);

            // 计算毛利
            // 计算公式：[截单的订单总额]-[成本金额]-[售后金额]+[售后找回金额]+[卖框金额]
            BigDecimal grossProfit = orderTotalAmount
                .subtract(costAmount)
                .subtract(afterSaleAmount)
                .add(afterSaleReturnAmount)
                .add(priceDifferenceAmount);
            result.setGrossProfit(grossProfit.setScale(DECIMAL_SCALE, RoundingMode.HALF_UP));
        } catch (Exception e) {
            log.error("获取毛利分析数据异常", e);
            // 发生异常时设置默认值
            result.setGrossProfit(BigDecimal.ZERO);
            result.setCostAmount(BigDecimal.ZERO);
            result.setActualSaleAmountWithTax(BigDecimal.ZERO);
            result.setAfterSaleAmount(BigDecimal.ZERO);
        }

        return result;
    }
}

