package org.springblade.common.enums;

import cn.hutool.core.lang.Assert;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WarehouseTypeEnum implements BaseEnum<Integer> {

	CHINA(1, "全国仓库"),
	CITY(2, "城市总仓"),
	DIVIDE(3, "分仓");

	private final Integer code;
	private final String message;

	/**
	 * 是否总仓
	 */
	public static boolean isTotalWarehouse(Integer warehouseType) {
		return CITY.getCode().equals(warehouseType);
	}

	public static void checkIsTotalWarehouse(Integer warehouseType) {
		Assert.isTrue(isTotalWarehouse(warehouseType), "非总仓管理员，不能操作");
	}
}
