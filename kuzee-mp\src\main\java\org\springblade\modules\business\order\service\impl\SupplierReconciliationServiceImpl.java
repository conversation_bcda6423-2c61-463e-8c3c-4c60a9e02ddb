/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.SupplierReconciliationRecordEnum;
import org.springblade.common.enums.SupplierReconciliationStatusEnum;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.modules.business.order.mapper.SupplierReconciliationMapper;
import org.springblade.modules.business.order.pojo.dto.SupplierReconciliationPageDTO;
import org.springblade.modules.business.order.pojo.entity.SupplierEntity;
import org.springblade.modules.business.order.pojo.entity.SupplierReconciliationEntity;
import org.springblade.modules.business.order.pojo.entity.SupplierReconciliationItemEntity;
import org.springblade.modules.business.order.pojo.entity.SupplierReconciliationRecordEntity;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
@AllArgsConstructor
@Slf4j
public class SupplierReconciliationServiceImpl extends BaseServiceImpl<SupplierReconciliationMapper, SupplierReconciliationEntity> implements ISupplierReconciliationService {
	private final ISupplierService supplierService;
	private final ISupplierReconciliationRecordService supplierReconciliationRecordService;
	private final ISupplierReconciliationPaymentService supplierReconciliationPaymentService;
	private final ISupplierReconciliationItemService supplierReconciliationItemService;
	private final IWarehouseService warehouseService;

	/**
	 * 获取详情
	 *
	 * @param id z主键id
	 * @return SupplierReconciliationDetailsVO
	 */
	public SupplierReconciliationDetailsVO getDetails(Long id) {
		// 获取对账单主表信息
		SupplierReconciliationEntity reconciliationEntity = getById(id);
		if (reconciliationEntity == null) {
			return null;
		}

		// 获取供应商信息
		SupplierEntity supplier = supplierService.getById(reconciliationEntity.getSupplierId());

		// 获取对账单明细信息
		LambdaQueryWrapper<SupplierReconciliationItemEntity> itemWrapper = new LambdaQueryWrapper<>();
		itemWrapper.eq(SupplierReconciliationItemEntity::getReconciliationId, id);
		List<SupplierReconciliationItemEntity> itemList = supplierReconciliationItemService.list(itemWrapper);

		// 分类处理明细信息
		List<SupplierReconciliationTradeVO> tradeList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_TRADE.equals(item.getType()))
			.map(item -> {
				SupplierReconciliationTradeVO tradeVO = new SupplierReconciliationTradeVO();
				tradeVO.setId(item.getId());
				tradeVO.setStockOrderNumber(item.getStockOrderNumber());
				tradeVO.setTotalAmount(item.getTotalAmount());
				tradeVO.setWarehousingTime(item.getWarehousingTime());
				tradeVO.setWarehouseId(item.getWarehouseId());
				tradeVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
				return tradeVO;
			})
			.collect(Collectors.toList());

		List<SupplierReconciliationFeeVO> feeList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_TRANSFER.equals(item.getType()))
			.map(item -> {
				SupplierReconciliationFeeVO feeVO = new SupplierReconciliationFeeVO();
				feeVO.setId(item.getId());
				feeVO.setStockOrderNumber(item.getStockOrderNumber());
				feeVO.setNumber(item.getNumber());
				feeVO.setWeight(item.getWeight());
				feeVO.setForwardingCharges(item.getForwardingCharges());
				feeVO.setWarehousingTime(item.getWarehousingTime());
				feeVO.setWarehouseId(item.getWarehouseId());
				feeVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
				return feeVO;
			})
			.collect(Collectors.toList());

		List<SupplierReconciliationAfterVO> afterList = itemList.stream()
			.filter(item -> BusinessConstant.ORDER_TYPE_RETURN.equals(item.getType()))
			.map(item -> {
				SupplierReconciliationAfterVO afterVO = new SupplierReconciliationAfterVO();
				afterVO.setId(item.getId());
				afterVO.setWeight(item.getWeight());
				afterVO.setReturnAmount(item.getReturnAmount());
				afterVO.setFinesTotal(item.getFinesTotal());
				afterVO.setTotalAmount(item.getTotalAmount());
				afterVO.setWarehousingTime(item.getWarehousingTime());
				afterVO.setWarehouseId(item.getWarehouseId());
				afterVO.setWarehouseName(warehouseService.getWarehouseName(item.getWarehouseId()));
				return afterVO;
			})
			.collect(Collectors.toList());

		// 构建详情对象
		SupplierReconciliationDetailsVO detailsVO = new SupplierReconciliationDetailsVO();
		detailsVO.setId(reconciliationEntity.getId());
		detailsVO.setSupplierId(reconciliationEntity.getSupplierId());
		detailsVO.setStatus(reconciliationEntity.getStatus());
		detailsVO.setStatusStr(SupplierReconciliationStatusEnum.getNameByCode(reconciliationEntity.getStatus()));
		detailsVO.setSupplierName(supplier.getName());
		detailsVO.setPhone(supplier.getPhone());
		detailsVO.setBankAddress(supplier.getAddress());
		detailsVO.setBankNumber(supplier.getBankNumber());
		detailsVO.setTradeList(tradeList);
		detailsVO.setFeeList(feeList);
		detailsVO.setAfterList(afterList);
		detailsVO.setChangeNote(reconciliationEntity.getChangeNote());
		return detailsVO;

	}


	/**
	 * 根据类型获取对账记录
	 *
	 * @param id        对账单ID
	 * @param type      记录类型
	 * @param isInvoice 是否为发票记录
	 * @return 对账记录列表
	 */
	private List<SupplierReconciliationRecordVO> getRecordsByType(Long id, int type, boolean isInvoice) {
		LambdaQueryWrapper<SupplierReconciliationRecordEntity> wrapper = Wrappers.lambdaQuery(SupplierReconciliationRecordEntity.class)
			.eq(SupplierReconciliationRecordEntity::getReconciliationId, id)
			.eq(SupplierReconciliationRecordEntity::getStatus, BusinessConstant.ENABLE_STATUS);

		if (isInvoice) {
			wrapper.eq(SupplierReconciliationRecordEntity::getType, type);
		} else {
			wrapper.ne(SupplierReconciliationRecordEntity::getType, type);
		}
		List<SupplierReconciliationRecordEntity> entities = supplierReconciliationRecordService.list(wrapper);
		return entities.stream()
			.map(this::convertToRecordVO)
			.collect(Collectors.toList());
	}

	/**
	 * 将 SupplierReconciliationRecordEntity 转换为 SupplierReconciliationRecordVO
	 *
	 * @param entity 对账记录实体
	 * @return 对账记录视图对象
	 */
	private SupplierReconciliationRecordVO convertToRecordVO(SupplierReconciliationRecordEntity entity) {
		SupplierReconciliationRecordVO vo = new SupplierReconciliationRecordVO();
		vo.setId(entity.getId());
		vo.setReconciliationId(entity.getReconciliationId());
		vo.setType(entity.getType());
		vo.setPaymentAmount(entity.getPaymentAmount());
		vo.setCreateTime(entity.getCreateTime());
		vo.setStatus(entity.getStatus());
		vo.setTypeName(SupplierReconciliationRecordEnum.getNameByCode(entity.getType()));
		return vo;
	}

	@Override
	public IPage<SupplierReconciliationPageVO> selectSupplierReconciliationPage(IPage<SupplierReconciliationPageVO> page, SupplierReconciliationPageDTO supplierReconciliation) {
		IPage<SupplierReconciliationPageVO> pages = page.setRecords(baseMapper.selectSupplierReconciliationPage(page, supplierReconciliation));
		if (!pages.getRecords().isEmpty()) {
			pages.getRecords()
				.forEach(vo -> vo.setStatusStr(SupplierReconciliationStatusEnum.getNameByCode(vo.getStatus())));
		}
		return pages;
	}

}
