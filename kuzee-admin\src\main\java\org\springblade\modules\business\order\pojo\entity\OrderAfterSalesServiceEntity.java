/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import scala.Int;

import java.io.Serial;

/**
 * 订单售后申请 实体类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("chy_order_after_sales_service")
@Schema(description = "OrderAfterSalesServiceEntity对象")
@EqualsAndHashCode(callSuper = true)
public class OrderAfterSalesServiceEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@Schema(description = "订单id")
	private Long orderId;
	/**
	 * 订单编号
	 */
	@Schema(description = "订单编号")
	private String afterSalesNo;
	/**
	 * 商品类型 1、商品 2 、运输品
	 */
	@Schema(description = "商品类型 1、商品 2 、运输品")
	private Integer type;
	/**
	 * 申请时间
	 */
	@Schema(description = "申请时间")
	private LocalDateTime applyTime;
	/**
	 * 客户ID
	 */
	@Schema(description = "客户ID")
	private Long custId;
	/**
	 * 仓库ID
	 */
	@Schema(description = "仓库ID")
	private Long warehouseId;
	/**
	 * 退款金额
	 */
	@Schema(description = "退款金额")
	private Integer amount;
	/**
	 * 退款金额（财务审核退款总额）
	 */
	@Schema(description = "退款金额（财务审核退款总额）")
	private Integer financeAmount;
	/**
	 * 处理时间
	 */
	@Schema(description = "处理时间")
	private LocalDateTime handleTime;
	/**
	 * 原因
	 */
	@Schema(description = "原因")
	private String reason;
	/**
	 * 描述
	 */
	@Schema(description = "描述")
	private String description;
	/**
	 * 凭证附件，以逗号隔开
	 */
	@Schema(description = "凭证附件，以逗号隔开")
	private String proofAttachs;
	/**
	 * 处理备注
	 */
	@Schema(description = "处理备注")
	private String handleNote;
	/**
	 * 处理人员
	 */
	@Schema(description = "处理人员")
	private Long handleId;
	/**
	 * 退货数量
	 */
	@Schema(description = "退货数量")
	private Integer quantity;
	/**
	 * 重量（g）
	 */
	@Schema(description = "重量（g）")
	private Integer weight;
	/**
	 *配送/运载
	 */
	@Schema(description = "配送/运载")
	private Integer deliveryFee;
	/**
	 * 财务处理时间
	 */
	@Schema(description = "财务处理时间")
	private LocalDateTime financeTime;

	/**
	 * 授信额度支付金额
	 */
	@Schema(description = "授信额度支付金额")
	private Integer creditaPayAmount;
	/**
	 * 钱包余额支付金额
	 */
	@Schema(description = "钱包余额支付金额")
	private Integer walletPayAmount;
	/**
	 * 三方渠道支付金额
	 */
	@Schema(description = "三方渠道支付金额")
	private Integer channelPayAmount;

	/**
	 *服务费
	 */
	@Schema(description = "服务费")
	private Integer serviceFee;

	/**
	 *拒绝原因
	 */
	@Schema(description = "拒绝原因")
	private String rejectReason;
}
