package org.springblade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.core.log.exception.ServiceException;

import java.util.Arrays;
import java.util.List;

/**
 * 仓库入库业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum StoreBizTypeEnum implements BaseEnum<Integer> {

	PURCHASE_IN(1, "供应商供货入库", BusinessConstant.ORDER_SUPPLIER_NO, false),
	TEMP_PURCHASE_IN(2, "临时采购入库", BusinessConstant.ORDER_TEMP_PURCHASE_NO, false),
	ALLOT(3, "调拨入库", BusinessConstant.ORDER_ALLOCATION_NO, false),
	TRANSPORT(4, "中心仓入库", BusinessConstant.ORDER_TRANS_NO, false),
	RETURN_IN(5, "退货入库", BusinessConstant.ORDER_RETURN_IN_NO, false),
	DIFF(6, "差异入库", "DIFF", true),
	CHECK(7, "盘点入库", "DIFF", true);

	private final Integer code;
	private final String message;
	private final String prefix;
	// 不做主要业务，业务中排除
	private final boolean exclude;

	public static StoreBizTypeEnum getByCode(Integer bizType) {
		for (StoreBizTypeEnum value : StoreBizTypeEnum.values()) {
			if (value.getCode().equals(bizType)) {
				return value;
			}
		}
		throw new ServiceException("不存在的入库类型");
	}

	public static String getNameByCode(Integer bizType) {
		for (StoreBizTypeEnum value : StoreBizTypeEnum.values()) {
			if (value.getCode().equals(bizType)) {
				return value.getMessage();
			}
		}
		return null;
	}

	public static String getPrefixByCode(Integer bizType) {
		for (StoreBizTypeEnum value : StoreBizTypeEnum.values()) {
			if (value.getCode().equals(bizType)) {
				return value.getPrefix();
			}
		}
		return "";
	}

	/**
	 * 是否退货入库
	 */
	public static boolean isReturnIn(Integer bizType) {
		return RETURN_IN.getCode().equals(bizType);
	}

	@Override
	public Integer getCode() {
		return code;
	}

	/**
	 * 是否关联供应商
	 */
	public static boolean isSupplierIn(Integer code) {
		return PURCHASE_IN.getCode().equals(code) || TEMP_PURCHASE_IN.getCode().equals(code);
	}

	/**
	 * 是否供应商供货入库(由采购方式)
	 */
	public static boolean isPurchaseIn(Integer code) {
		return PURCHASE_IN.getCode().equals(code);
	}

	/**
	 * 是否关联其他仓库
	 */
	public static boolean isOtherWarehouseIn(Integer code) {
		return ALLOT.getCode().equals(code) || TRANSPORT.getCode().equals(code);
	}

	/**
	 * 是否转运入库
	 */
	public static boolean isTransport(Integer bizType) {
		return TRANSPORT.getCode().equals(bizType);
	}

	/**
	 * 获取排除的入库类型
	 */
	public static List<Integer> getExcludeBizTypes() {
		return Arrays.stream(StoreBizTypeEnum.values())
			.filter(StoreBizTypeEnum::isExclude)
			.map(StoreBizTypeEnum::getCode)
			.toList();
	}
	public static boolean isExcluded(Integer bizType) {
		StoreBizTypeEnum type = getByCode(bizType);
		return type.isExclude();
	}
}
