/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.pojo.vo.IdNumVO;
import org.springblade.common.pojo.vo.PrintOutboundVO;
import org.springblade.modules.business.order.excel.SaleOrderExcel;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.SaleOrderEntity;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.warehouse.pojo.dto.PrintSingleItemOrderDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseOutboundPrintDTO;

import java.util.List;
import java.util.Map;

/**
 * 销售订单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface SaleOrderMapper extends BaseMapper<SaleOrderEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param saleOrder 查询参数
	 * @return List<SaleOrderVO>
	 */
	List<SaleOrderPageVO> selectSaleOrderPage(IPage page,@Param(value = "dto")  SaleOrderPageDTO saleOrder);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<SaleOrderExcel>
	 */
	List<SaleOrderExcel> exportSaleOrder(@Param("ew") Wrapper<SaleOrderEntity> queryWrapper);


    void UpdateCutOrder(@Param("ids") List<Long> orderIds,@Param("cutNo") String cutNo);
	/**
	 * 获取订单详情
	 *
	 * @param orderId 订单ID
	 * @return SalaOrderDetailVO
	 */
	SalaOrderDetailVO selectOrderDetail(@Param("orderId") Long orderId);

    List<AfterSalesRecordListVO> getAfterSalesRecordList(@Param("dto") AfterSalesRecordListDTO dto);

	List<CustCancelsProductListVO> getcustCancelsProductList(@Param("dto") CustCancelsProductListDTO dto);

    IPage<CustomerOrderListVO> getCustomerOrderList(@Param("dto") CustomerOrderListDTO dto, IPage<CustomerOrderListVO> page,@Param("userId") Long userId);

	TotalAmountVO getCustomerOrderTotalAmount(@Param("dto") CustomerOrderListDTO dto,@Param("userId")  Long userId);

	/***
	 * 查询待出库订单
	 */
    List<SaleOrderEntity> outboundPage(IPage<SaleOrderEntity> page, @Param("dto") SaleOrderOutboundDTO dto,
									   @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<IdNumVO> selectSkuIds(@Param("orderId") Long orderId);

	/**
	 * 打印出库单
	 */
	List<PrintOutboundVO> printAggQuantityAndWeight(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<PrintOutboundVO> getOutboundList(@Param("dto") PrintSingleItemOrderDTO dto);

    List<PrintOutboundVO> printOutWarechouse(@Param("dto") WarehouseOutboundPrintDTO dto);

	List<SaleOrderEntity> OrderExpire();

	/**
	 * 查询客户基本信息
	 *
	 * @param orderIds 查询参数
	 * @return 客户基本信息
	 */
	CustomerInfo queryCustomerInfo(@Param("orderIds") List<Long> orderIds);

	/**
	 * 查询订单商品信息
	 *
	 * @param orderIds 查询参数
	 * @return 订单商品信息列表
	 */
	List<OrderProductInfo> queryOrderProductInfo(@Param("orderIds") List<Long> orderIds);

	/**
	 * 查询库存信息
	 *
	 * @param warehouseId 仓库ID
	 * @param skuId SKU ID
	 * @return 库存信息列表
	 */
	List<OrderProductInfo> queryInventoryInfo(@Param("warehouseId") Long warehouseId,
											  @Param("skuId") Long skuId);

	/**
	 * 查询订单运输品库存信息
	 *
	 * @param warehouseId 仓库id
	 * @param skuId sku_id
	 * @param transUnitId 配套运输品id
	 * @return 订单运输品库存信息列表
	 */
	List<OrderTransportInfo> queryInventoryTransportInfo(@Param("warehouseId") Long warehouseId,
														 @Param("skuId") Long skuId,
														 @Param("transUnitId") Long transUnitId
														);

	/**
	 * 查询订单运输品信息
	 *
	 * @param saleOrderItemId 销售订单详情id
	 * @return 订单运输品信息列表
	 */
	OrderTransportInfo queryOrderTransportBaseInfo(@Param("saleOrderItemId") Long saleOrderItemId);

    IPage<OrderListVO> getOrderList(@Param("dto") CustOrderListDTO dto, IPage<OrderListVO> page);

	IPage<CustOrderListVO> getCustOrderList(@Param("dto") CustOrderListDTO dto, IPage<CustOrderListVO> page);

	OrderStatisticsVO getOrderStatistics(@Param("dto") CustOrderListDTO dto);

	IPage<OrderListDetailsVO> getOrderListDetails(@Param("dto") CustOrderListDTO dto, IPage<OrderListDetailsVO> page);

	IPage<CustOrderListDetailsVO> getCustOrderListDetails(@Param("dto") CustOrderListDTO dto, IPage<CustOrderListDetailsVO> page);

	/**
	 * 订单信息拉到列表上展示、按商品统计订单
	 *
	 * @param page      分页参数
	 * @param saleOrder 查询参数
	 * @return List<OrderListProductVO>
	 */
	IPage<OrderListProductVO> getOrderListProduct(IPage<OrderListProductVO> page, @Param("dto") SaleOrderPageDTO saleOrder);

	List<Map<String, Object>> getPurchaseSummaryByItems(@Param("itemIds") List<Long> itemIds, @Param("skuIds") List<Long> skuIds);

	/**
	 * 按分区汇总
	 *
	 * @param page 分页参数
	 * @param dto  查询参数
	 * @return IPage<SalesSummaryByRegionVO>
	 */
	IPage<SalesSummaryByRegionVO> selectSalesSummaryByRegion(IPage<?> page, @Param("dto") SalesStatisticsQueryDTO dto);

	/**
	 * 按商品汇总
	 *
	 * @param page 分页参数
	 * @param dto  查询参数
	 * @return IPage<SalesSummaryByProductVO>
	 */
	IPage<SalesSummaryByProductVO> selectSalesSummaryByProduct(IPage<?> page, @Param("dto") SalesStatisticsQueryDTO dto);

	/**
	 * 销售明细汇总
	 *
	 * @param page 分页参数
	 * @param dto  查询参数
	 * @return IPage<SalesSummaryDetailVO>
	 */
	IPage<SalesSummaryDetailVO> selectSalesSummaryDetail(IPage<?> page, @Param("dto") SalesStatisticsQueryDTO dto);

	/**
	 * 按业务员汇总
	 *
	 * @param page 分页参数
	 * @param dto  查询参数
	 * @return IPage<SalesSummaryBySalespersonVO>
	 */
	IPage<SalesSummaryBySalespersonVO> selectSalesSummaryBySalesperson(IPage<?> page, @Param("dto") SalesStatisticsQueryDTO dto);

	SalaOrderDetailVO selectOrderDetailOld(Long orderId);

    void sukRestrictedRecharge();
}
