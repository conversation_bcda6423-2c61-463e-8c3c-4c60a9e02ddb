<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustCreditRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custCreditRecordResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustCreditRecordEntity">
        <result column="cust_id" property="custId"/>
        <result column="limit_change_type" property="limitChangeType"/>
        <result column="old_credit_limit" property="oldCreditLimit"/>
        <result column="new_credit_limit" property="newCreditLimit"/>
    </resultMap>

    <select id="selectCustCreditRecordPage" resultMap="custCreditRecordResultMap">
        select * from chy_cust_credit_record where is_deleted = 0
    </select>

    <select id="exportCustCreditRecord" resultType="org.springblade.modules.business.cust.excel.CustCreditRecordExcel">
        SELECT * FROM chy_cust_credit_record ${ew.customSqlSegment}
    </select>

    <select id="listpage" resultType="org.springblade.modules.business.cust.pojo.vo.CustCreditRecordListVO">
        select * from chy_cust_credit_record where is_deleted = 0 and cust_id = #{dto.custId}
        order by create_time desc
    </select>
</mapper>
