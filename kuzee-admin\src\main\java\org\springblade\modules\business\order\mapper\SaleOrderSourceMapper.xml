<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.order.mapper.SaleOrderSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="saleOrderSourceResultMap" type="org.springblade.modules.business.order.pojo.entity.SaleOrderSourceEntity">
        <result column="cust_id" property="custId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_time" property="orderTime"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="delivery_expense" property="deliveryExpense"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="credita_pay_amount" property="creditaPayAmount"/>
        <result column="wallet_pay_amount" property="walletPayAmount"/>
        <result column="channel_pay_amount" property="channelPayAmount"/>
        <result column="wallet_amount_diff" property="walletAmountDiff"/>
        <result column="pay_type" property="payType"/>
        <result column="source_type" property="sourceType"/>
        <result column="order_type" property="orderType"/>
        <result column="sessions_id" property="sessionsId"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="add_order" property="addOrder"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_post_code" property="receiverPostCode"/>
        <result column="receiver_province" property="receiverProvince"/>
        <result column="receiver_city" property="receiverCity"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="note" property="note"/>
        <result column="confirm_status" property="confirmStatus"/>
        <result column="is_add_box" property="isAddBox"/>
        <result column="create_time_at" property="createTimeAt"/>
        <result column="pay_status" property="payStatus"/>
        <result column="is_cut_order" property="isCutOrder"/>
        <result column="batch_no" property="batchNo"/>
        <result column="return_reason" property="returnReason"/>
        <result column="return_handle_man" property="returnHandleMan"/>
        <result column="return_time" property="returnTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="cancel_user_id" property="cancelUserId"/>
        <result column="pay_time" property="payTime"/>
        <result column="receiver_time" property="receiverTime"/>
        <result column="delivery_fee" property="deliveryFee"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="take_delivery_path" property="takeDeliveryPath"/>
        <result column="is_credita_repayment" property="isCreditaRepayment"/>
        <result column="pay_order_no" property="payOrderNo"/>
        <result column="credita_repay_time" property="creditaRepayTime"/>
        <result column="pay_time_at" property="payTimeAt"/>
        <result column="order_cust_id" property="orderCustId"/>
        <result column="receive_method" property="receiveMethod"/>
    </resultMap>

    <select id="selectSaleOrderSourcePage" resultMap="saleOrderSourceResultMap">
        select * from chy_sale_order_source where is_deleted = 0
    </select>

    <select id="exportSaleOrderSource" resultType="org.springblade.modules.business.order.excel.SaleOrderSourceExcel">
        SELECT * FROM chy_sale_order_source ${ew.customSqlSegment}
    </select>

</mapper>
