<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.order.mapper.OrderAfterSalesServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderAfterSalesServiceResultMap"
               type="org.springblade.modules.business.order.pojo.entity.OrderAfterSalesServiceEntity">
        <result column="order_id" property="orderId"/>
        <result column="after_sales_no" property="afterSalesNo"/>
        <result column="type" property="type"/>
        <result column="apply_time" property="applyTime"/>
        <result column="cust_id" property="custId"/>
        <result column="amount" property="amount"/>
        <result column="handle_time" property="handleTime"/>
        <result column="reason" property="reason"/>
        <result column="description" property="description"/>
        <result column="proof_attachs" property="proofAttachs"/>
        <result column="handle_note" property="handleNote"/>
        <result column="handle_Id" property="handleId"/>
    </resultMap>

    <resultMap id="orderAfterSalesStorePageMap"
               type="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesStorePageVO">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="after_sales_no" property="afterSalesNo"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="total_amount" property="totalAmount"
                typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result column="apply_time" property="applyTime"/>
    </resultMap>

    <resultMap id="orderAfterSalesStoreDetailMap"
               type="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesStoreDetailBaseVO">
        <result column="type" property="type"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="note" property="note"/>
        <result column="receiver_region" property="receiverRegion"/>
        <result column="receiver_detail_address" property="receiverDetailAddress"/>
        <result column="total_amount" property="totalAmount"
                typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result column="apply_time" property="applyTime"/>
        <result column="order_id" property="orderId"/>
    </resultMap>

    <select id="selectOrderAfterSalesServicePage" resultMap="orderAfterSalesServiceResultMap">
        select * from chy_order_after_sales_service where is_deleted = 0
    </select>

    <select id="exportOrderAfterSalesService"
            resultType="org.springblade.modules.business.order.excel.OrderAfterSalesServiceExcel">
        SELECT * FROM chy_order_after_sales_service ${ew.customSqlSegment}
    </select>
    <select id="getOrderAfterSalesList"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesListVO">

        select a.after_sales_no,a.id,a.quantity,a.weight,a.apply_time,c.cust_name,c.phone,a.status from
        chy_order_after_sales_service a
        LEFT JOIN chy_cust c on a.cust_id=c.id
        where a.is_deleted=0 and a.type=1
        <if test="warehouseId!=null and warehouseId!=''">
            and a.warehouse_id = #{warehouseId}
        </if>
        <if test="ids!=null and ids.size()>0">
            and a.id in(
            select after_sales_id from chy_order_after_sales_service_item
            where a.is_deleted=0 and sku_id in
            <foreach item="item" index="index" collection="ids" separator="," open="(" close=")" >
                #{item}
            </foreach>
            )
        </if>
        <if test="dto.keywords!=null and dto.keywords!=''">
            and ( c.cust_name like CONCAT('%',#{dto.keywords},'%') or c.phone like CONCAT('%',#{dto.keywords},'%'))
        </if>
        <if test="dto.orderNo!=null and dto.orderNo!=''">
            and ( a.after_sales_no like CONCAT('%',#{dto.orderNo},'%'))
        </if>
        <if test="dto.beginDate!=null and dto.beginDate!=''">
            and Date( a.apply_time) &gt;= Date(#{dto.beginDate})
        </if>
        <if test="dto.endDate!=null and dto.endDate!=''">
            and Date(a.apply_time) &lt;= Date(#{dto.endDate})
        </if>
        <if test="dto.status!=null and dto.status!=''">
            and a.status = #{dto.status}
        </if>
    </select>

    <select id="selectOrderAfterSalesStorePage" resultMap="orderAfterSalesStorePageMap">
        SELECT s.id,s.order_id,o.order_no,s.after_sales_no,o.receiver_name,o.receiver_phone,o.total_amount,s.apply_time
        FROM chy_order_after_sales_service s
        INNER JOIN chy_sale_order o ON o.id = s.order_id AND o.is_deleted = 0
        where s.is_deleted = 0 AND
              ((s.type = 1 AND s.STATUS = 3 AND NOT EXISTS (SELECT 1 FROM chy_warehouse_store ws WHERE ws.related_order_id = s.id AND ws.is_deleted = 0 AND ws.biz_type = 5))
                   OR (s.type = 2 AND s.STATUS = 2))
        <if test="dto.warehouseId!=null and dto.warehouseId!=''">
            and o.warehouse_id = #{dto.warehouseId}
        </if>
        <if test="dto.custInfo!=null and dto.custInfo!=''">
            and ( o.receiver_name like CONCAT('%',#{dto.custInfo},'%') or o.receiver_phone like
            CONCAT('%',#{dto.custInfo},'%'))
        </if>
        <if test="dto.orderNo!=null and dto.orderNo!=''">
            and o.order_no = #{dto.orderNo}
        </if>
    </select>

    <select id="selectOrderAfterSalesStoreDetail" resultMap="orderAfterSalesStoreDetailMap">
        SELECT
        s.type,o.receiver_name,o.receiver_phone,o.warehouse_id,w.warehouse_name,o.delivery_type,o.note,o.receiver_region,o.receiver_detail_address,o.total_amount,s.apply_time,s.order_id
        FROM chy_order_after_sales_service s
        INNER JOIN chy_sale_order o ON o.id = s.order_id AND o.is_deleted = 0
        INNER JOIN chy_warehouse w ON w.id = o.warehouse_id
        where s.id = #{orderId} AND s.is_deleted = 0
    </select>

    <select id="getOrderAfterSalesDetail"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesDetailVO">

        select a.after_sales_no,a.id,a.apply_time,c.cust_name,c.phone,a.status,w.warehouse_name,a.handle_note from
        chy_order_after_sales_service a
        LEFT JOIN chy_cust c on a.cust_id=c.id
        Left JOIN chy_warehouse w on c.warehouse_id=w.id
        where a.id=#{dto.id} and a.is_deleted=0

    </select>
    <select id="getOrderAfterSalesServicePage"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesServicePageVO">
        <!--//and s.status=3-->
        select s.id, o.order_no,o.order_time,s.create_time,c.cust_name,c.phone,w.warehouse_name,s.`status`
        from chy_order_after_sales_service s
        left JOIN chy_sale_order o on s.order_id=o.id
        LEFT JOIN chy_warehouse w on o.warehouse_id=w.id
        LEFT JOIN chy_cust c on o.cust_id= c.id where s.is_deleted=0 and s.type = 1

        <if test="dto.warehouseId!=null and dto.warehouseId!=''">
            and o.warehouse_id = #{dto.warehouseId}
        </if>
        <if test="dto.orderNo!=null and dto.orderNo!=''">
            and o.order_no = #{dto.orderNo}
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and Date(s.create_time) &gt;= Date(#{dto.startTime})
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and Date(s.create_time) &lt;= Date(#{dto.endTime})
        </if>

        <if test="dto.custName!=null and dto.custName!=''">
            and ( c.cust_name like CONCAT('%',#{dto.custName},'%') or c.phone like CONCAT('%',#{dto.custName},'%'))
        </if>

        <if test="dto.status!=null and dto.status!=''">
            and s.`status` = #{dto.status}
        </if>

        order by s.create_time desc

    </select>
    <select id="orderAfterSalesServiceDetail"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesServiceDetailVO">
        select s.id,s.apply_time,c.cust_name as operator,o.order_no,c.parent_id as custPrentId,c.cust_name as custName,
        c.phone,w.warehouse_name,o.total_amount,o.credita_pay_amount,o.wallet_pay_amount,o.channel_pay_amount,s.handle_Id,
        s.handle_time,s.handle_note,s.`status`,u.`name` as handleName
        from chy_order_after_sales_service s
        LEFT JOIN chy_sale_order o on s.order_id=o.id
        LEFT JOIN chy_warehouse w on o.warehouse_id=w.id
        LEFT JOIN chy_cust c on c.id=o.cust_id
        LEFT JOIN blade_user u on s.handle_id =u.id

        where s.is_deleted=0 and s.id=#{id}

    </select>
    <select id="getOrderAfterSalesProductDetailList"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesProductDetailListVO">
        <!--        select o.id,o.product_name,o.sp_data,o.examine_quantity,o.examine_weight,o.examine_amount, s.quantity,s.weight,-->
        <!--        m.amount,o.fines_weight,o.fines_type,o.examine_fines_amount, o.product_unit_price,o.product_sale_price,-->
        <!--        s.store_no as batchNo,su.`name`as supplierMoney,sus.`name`as supplier,o.handle_attachs,o.reason,s.amount as examineSupplierAmount,m.supplier_id as supplierIdMoney,-->
        <!--        s.supplier_id as supplierId-->
        <!--        from  chy_order_after_sales_service_item o-->
        <!--        LEFT JOIN chy_order_after_sales_service_item_money m on o.id =m.after_sales_item_id-->
        <!--        LEFT JOIN chy_order_after_sales_service_item_supplier s on o.id=s.after_sales_item_id-->
        <!--        LEFT JOIN chy_supplier su on m.supplier_id=su.id-->
        <!--        LEFT JOIN chy_supplier sus on s.supplier_id=sus.id-->
        <!--        where o.is_deleted=0 and s.is_deleted=0 and m.is_deleted=0 and o.after_sales_id=#{id}-->
        <!--        order by s.after_sales_item_id desc,s.supplier_id desc,s.store_no desc-->


        select o.id,o.product_name,o.sp_data,o.examine_quantity,o.examine_weight,o.examine_amount,
        o.fines_weight,o.fines_type,o.examine_fines_amount, o.product_unit_price,o.product_sale_price,
        o.handle_attachs,o.reason,o.description as note,o.finance_description as financeDescription
        from chy_order_after_sales_service_item o
        where o.is_deleted=0 and o.after_sales_id=#{id}
        order by o.id
    </select>
    <select id="getOrderAfterSalesProductDetailFinList"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesProductDetailListVO">
        select o.id,o.product_name,o.sp_data,o.finance_quantity as examineQuantity,o.finance_weight as
        examineWeight,o.finance_amount as examineAmount,
        o.finance_fines_weight as finesWeight,o.fines_type,o.finance_fines_amount as examineFinesAmount,
        o.product_unit_price,o.product_sale_price,
        o.handle_attachs,o.reason,o.description as note,o.finance_description as financeDescription
        from chy_order_after_sales_service_item o
        where o.is_deleted=0 and o.after_sales_id=#{id}
        order by o.id
    </select>
    <select id="getSupplierAfterAlesOrderList"
            resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterAlesOrderListVO">
        <include refid="orderAfterSalesServiceCondition"></include>
    </select>
    <select id="getSupplierAfterAlesOrderDetail"
            resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterAlesOrderListVO">
        <include refid="orderAfterSalesServiceCondition"></include>
    </select>
    <select id="getSupplierAfterAlesOrderDetailList"
            resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterAlesOrderDetailListVO">
        select s.product_name,s.sp_data,s.finance_quantity as quantity, s.finance_weight as weight,s.finance_amount as
        amount,s.proof_attachs,s.damage_attachs,s.weight_attachs,s.id as afterSalesItemId
        from chy_order_after_sales_service_item_supplier t
        LEFT JOIN chy_order_after_sales_service_item s on s.id=t.after_sales_item_id
        where t.is_deleted=0 and s.after_sales_id=#{dto.afterId} and t.supplier_id=#{dto.id}
    </select>

    <select id="getSupplierAfterAlesOrderDetailMList"
            resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterAlesOrderDetailListVO">
        select s.product_name,s.sp_data, t.finance_amount as fineAmount,s.proof_attachs,s.damage_attachs,s.weight_attachs,s.id as afterSalesItemId
        from chy_order_after_sales_service_item_money t
        LEFT JOIN chy_order_after_sales_service_item s on s.id=t.after_sales_item_id
        where t.is_deleted=0 and s.after_sales_id=#{dto.afterId} and t.supplier_id=#{dto.id}
    </select>
    <sql id="orderAfterSalesServiceCondition">
        select t.id, t.finance_time,a.fineAmount,b.refundAmount from chy_order_after_sales_service t
        left join chy_order_after_sales_service_item i on t.id=i.after_sales_id
        LEFT JOIN (
        select supplier_id,after_sales_item_id,SUM(finance_amount) as fineAmount from
        chy_order_after_sales_service_item_money
        where is_deleted=0 and supplier_id=#{dto.id} GROUP BY supplier_id,after_sales_item_id) a on
        i.id=a.after_sales_item_id
        LEFT JOIN (
        select supplier_id,after_sales_item_id,SUM(finance_amount) as refundAmount from
        chy_order_after_sales_service_item_supplier
        where is_deleted=0 and supplier_id=#{dto.id} GROUP BY supplier_id,after_sales_item_id
        )b on b.after_sales_item_id=i.id
        where t.is_deleted=0 and i.is_deleted=0
        <if test="dto.afterId!=null and dto.afterId!=''">
            and t.id = #{dto.afterId}
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and Date(t.finance_time) &gt;= Date(#{dto.startTime})
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and Date(t.finance_time) &lt;= Date(#{dto.endTime})
        </if>
<!--        and (a.fineAmount &gt; 0 or b.refundAmount&gt;0)-->
    </sql>
    <select id="getOrderAfterSalesByWarehouseList"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesByWarehouseListVO">
        select
        ss.id,
        ss.warehouse_id,
        wh.warehouse_name,
        SUM(sis.finance_quantity) as finance_quantity,
        SUM(sis.finance_weight) as finance_weight,
        SUM(sis.finance_amount) as finance_amount,
        SUM(im.finance_amount) as fine_amount
        from
        chy_order_after_sales_service ss
        left join
        chy_warehouse wh on ss.warehouse_id = wh.id
        left join
        chy_order_after_sales_service_item si on ss.id = si.after_sales_id
        left join
        chy_order_after_sales_service_item_money im on si.id = im.after_sales_item_id
        left join
        chy_order_after_sales_service_item_supplier sis on sis.after_sales_item_id = si.id
        where 1=1 and (
        im.supplier_id = #{supplierId}
        or sis.supplier_id = #{supplierId})
        and ss.status = 3
        <if test="startDate!=null">
            and Date(ss.finance_time) &gt;= Date(#{startDate})
        </if>
        <if test="endDate!=null">
            and Date(ss.finance_time) &lt;= Date(#{endDate})
        </if>
        group by
        ss.warehouse_id, wh.warehouse_name
    </select>
    <select id="getOrderAfterSalesByWarehouseListForSuppliers"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesByWarehouseListVO">
        select
            MIN(ss.id) as id,
            agg.supplier_id as supplierId,
            ss.warehouse_id,
            wh.warehouse_name,
            SUM(agg.finance_quantity) as finance_quantity,
            SUM(agg.finance_weight) as finance_weight,
            SUM(agg.finance_amount) as finance_amount,
            SUM(agg.fine_amount) as fine_amount
        from
            chy_order_after_sales_service ss
        left join
            chy_warehouse wh on ss.warehouse_id = wh.id
        left join
            chy_order_after_sales_service_item si on ss.id = si.after_sales_id
        join
            (
                select
                    after_sales_item_id,
                    supplier_id,
                    finance_quantity,
                    finance_weight,
                    finance_amount,
                    0 as fine_amount
                from
                    chy_order_after_sales_service_item_supplier
                where
                    is_deleted = 0 and supplier_id IN
                    <foreach item="item" index="index" collection="supplierIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>

                union all

                select
                    after_sales_item_id,
                    supplier_id,
                    0 as finance_quantity,
                    0 as finance_weight,
                    0 as finance_amount,
                    finance_amount as fine_amount
                from
                    chy_order_after_sales_service_item_money
                where
                    is_deleted = 0 and supplier_id IN
                    <foreach item="item" index="index" collection="supplierIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            ) agg on si.id = agg.after_sales_item_id
        where
            ss.status = 3 and si.is_deleted = 0
        <if test="startDate!=null">
            and Date(ss.finance_time) &gt;= Date(#{startDate})
        </if>
        <if test="endDate!=null">
            and Date(ss.finance_time) &lt;= Date(#{endDate})
        </if>
        group by
            agg.supplier_id,
            ss.warehouse_id,
            wh.warehouse_name
    </select>
    <select id="selectSupplierPage"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderAfterSalesByProductListVO">
        select
        ss.id,
        ss.warehouse_id,
        wh.warehouse_name,
        si.sku_id as productSkuId,
        si.product_name,
        si.sp_data,
        ss.finance_time,
        SUM(sis.finance_quantity) as finance_quantity,
        SUM(sis.finance_weight) as finance_weight,
        SUM(sis.finance_amount) as finance_amount,
        SUM(im.finance_amount) as fine_amount
        from
        chy_order_after_sales_service ss
        left join
        chy_warehouse wh on ss.warehouse_id = wh.id
        left join
        chy_order_after_sales_service_item si on ss.id = si.after_sales_id
        left join
        chy_order_after_sales_service_item_money im on si.id = im.after_sales_item_id
        left join
        chy_order_after_sales_service_item_supplier sis on sis.after_sales_item_id = si.id
        where 1=1 and (
        im.supplier_id = #{dto.supplierId}
        or sis.supplier_id = #{dto.supplierId})
        and ss.status = 3
        <if test="dto.cycleStartTime!=null">
            and Date(ss.finance_time) &gt;= Date(#{dto.cycleStartTime})
        </if>
        <if test="dto.cycleEndTime!=null">
            and Date(ss.finance_time) &lt;= Date(#{dto.cycleEndTime})
        </if>
        <if test="dto.thirdId!=null">
            and ss.id=#{dto.thirdId}
        </if>
        group by
        ss.id,
        ss.warehouse_id,
        wh.warehouse_name,
        si.sku_id,
        si.product_name,
        si.sp_data,
        ss.finance_time
    </select>
    <select id="getOrderAfterSalesListItemList"
            resultType="org.springblade.modules.business.order.pojo.vo.OrderDetailsAfterSalesItemVO">
        select id,after_sales_id,
        product_pic,product_name,quantity,weight,amount,finance_quantity,finance_weight,finance_amount,support_trans,
        proof_attachs,damage_attachs,weight_attachs,product_unit_price,support_trans_unit_id from
        chy_order_after_sales_service_item where is_deleted=0 and after_sales_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

    </select>

    <select id="orderAfterSalesCount" resultType="org.springblade.modules.business.order.pojo.vo.BusinessToDoVO">
        select COUNT(1) as returnOrderNum from chy_order_after_sales_service  where  is_deleted=0   and  status=0
        and id in(
            select after_sales_id from chy_order_after_sales_service_item where is_deleted=0 and sku_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

    <select id="purchaseOrderCount" resultType="org.springblade.modules.business.order.pojo.vo.BusinessToDoVO">
        select count(1) as purchasedNum from chy_purchase_order where is_deleted=0 and ( allocation_status=1) and
        purchaser_id = #{userId} and self_purchaser=0
    </select>
    <select id="getSupplierAfterSalesDetailsS"  resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterSalesDetailsVO">
        select t.finance_time as afterSaleTime,i.product_name as productName,i.sp_data as spData,u.unit_name as unitName,
        s.finance_weight as afterSaleWeight,s.finance_amount as afterSaleAmount,p.is_standard as isStandard,i.id as afterSaleItemId
        ,c.cust_name as customerName,i.product_sale_price from chy_order_after_sales_service t
        LEFT JOIN chy_order_after_sales_service_item i on t.id=i.after_sales_id
        LEFT JOIN chy_order_after_sales_service_item_supplier s on s.after_sales_item_id=i.id
        LEFT JOIN chy_sku_stock st on st.id=i.sku_id
        LEFT JOIN chy_base_unit u on st.base_unit_id = u.id
        LEFT JOIN chy_product p on p.id = st.product_id
        LEFT JOIN chy_cust c on c.id =t.cust_id
        where t.is_deleted=0 and i.is_deleted=0 and t.type=1 and t.`status`=3 and DATE(t.finance_time)=DATE(#{dto.afterSaleTime})
        and s.supplier_id=#{dto.supplierId}
        <if test="dto.warehouseId!=null">
            and t.warehouse_id = #{dto.warehouseId}
        </if>
        <if test="dto.productSkuId!=null">
            and i.sku_id = #{dto.productSkuId}
        </if>
        ORDER BY i.id
    </select>


    <select id="getSupplierAfterSalesDetailsM"  resultType="org.springblade.modules.business.order.pojo.vo.SupplierAfterSalesDetailsVO">
        select t.finance_time as afterSaleTime,i.product_name as productName,i.sp_data as spData,u.unit_name as unitName,
        m.finance_amount as afterSaleFine,p.is_standard as isStandard,i.id as afterSaleItemId ,c.cust_name as customerName,i.product_sale_price
        from chy_order_after_sales_service t
        LEFT JOIN chy_order_after_sales_service_item i on t.id=i.after_sales_id
        LEFT JOIN chy_order_after_sales_service_item_money m on m.after_sales_item_id=i.id
        LEFT JOIN chy_sku_stock st on st.id=i.sku_id
        LEFT JOIN chy_base_unit u on st.base_unit_id = u.id
        LEFT JOIN chy_product p on p.id = st.product_id
        LEFT JOIN chy_cust c on c.id =t.cust_id
        where t.is_deleted=0 and i.is_deleted=0 and t.type=1 and t.`status`=3 and DATE(t.finance_time)=DATE(#{dto.afterSaleTime})
        and m.supplier_id=#{dto.supplierId}
        <if test="dto.warehouseId!=null">
            and t.warehouse_id = #{dto.warehouseId}
        </if>
        <if test="dto.productSkuId!=null">
            and i.sku_id = #{dto.productSkuId}
        </if>
        ORDER BY i.id
    </select>
</mapper>
