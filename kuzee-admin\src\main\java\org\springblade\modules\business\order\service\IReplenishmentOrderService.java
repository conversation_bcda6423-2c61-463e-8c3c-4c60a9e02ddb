/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.common.pojo.vo.IdVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.modules.business.order.excel.ReplenishmentOrderExcel;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.ReplenishmentOrderEntity;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreItemEntity;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * 供应商备货表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface IReplenishmentOrderService extends BaseService<ReplenishmentOrderEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param replenishmentOrder 查询参数
	 * @return IPage<ReplenishmentOrderVO>
	 */
	IPage<ReplenishmentOrderVO> selectReplenishmentOrderPage(IPage<ReplenishmentOrderVO> page, ReplenishmentOrderVO replenishmentOrder);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<ReplenishmentOrderExcel>
	 */
	List<ReplenishmentOrderExcel> exportReplenishmentOrder(Wrapper<ReplenishmentOrderEntity> queryWrapper);

    IPage<ReplenishmentListVO> replenishmentList(ReplenishmentListDTO dto, Query query);

	List<ReplenishmentDetailVO> replenishmentDetail(ReplenishmentDetailDTO dto);

	/**
	 * 根据供应商id查询供应商备货列表
	 *
	 * @param warehouseId 	仓库id
	 * @param supplierId 	供应商id
	 */
	List<IdVO> replenishmentListBySupplierId(Long warehouseId, Long supplierId);

	/**
	 * 新增备货单及商品明细
	 */
	String save(ReplenishmentOrderDTO dto);

//	/**
//	 * 修改备货单及商品明细
//	 */
//	void update(ReplenishmentOrderUpdateDTO dto);

	/**
	 * 查询固定商品的已分配详情
	 *
	 * @param purchaseOrderId 采购单id
	 * @param skuId			商品sku
	 */
	List<ReplenishmentOrderAllocationVO> listAllocationDetail(Long purchaseOrderId, Long skuId);

	/**
	 * 根据采购单id查询所有供应商备货单信息
	 */
	List<SupplierReplenishmentOrderVO> listSupplierReplenishment(Long purchaseOrderId);

	/**
	 * 备货单标记完成
	 */
	void complete(Long orderId);

	/**
	 * 根据供应商id查询供应商备货列表
	 */
	IPage<ReplenishmentListSupplierPageVO> listBySupplier(ReplenishmentListSupplierPageQueryDTO dto, IPage<ReplenishmentListSupplierPageDTO> page);

	/**
	 * 更新备货单状态并生成转运费
	 *
	 * @param replenishmentId 备货单id
	 * @param itemEntityList  入库商品明细
	 */
	void updateAndGenerateAmount(Long replenishmentId, List<WarehouseStoreItemEntity> itemEntityList);

	/**
	 * 生成备货单PDF
	 * @param id 备货单ID
	 * @return PDF字节流
	 */
	byte[] generateReplenishmentOrderPdf(Long id);

	/**
	 * 打印
	 *
	 */
	ByteArrayOutputStream print(ReplenishmentOrderPrintDTO dto) throws IOException;

	/**
	 * 获取时间段所有的供货单明细
	 *
	 * @param warehouseId	仓库id
	 * @param startTime		备货单开始日期
	 * @param endTime		备货单结束日期
	 */
	List<ReplenishmentOrderItemByTimeVO> listByTime(Long warehouseId, List<Long> orderWarehouseIds, String startTime, String endTime);

	ByteArrayOutputStream printReplenishmentOrder(PrintReplenishmentOrderDTO dto) throws IOException;

	ByteArrayOutputStream printTransferOrder(TransferOrderPrintDTO dto) throws IOException;
}
