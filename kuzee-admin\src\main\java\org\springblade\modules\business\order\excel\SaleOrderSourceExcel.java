/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.excel;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serial;


/**
 * 销售订单原始数据表-支付成功时候的数据 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SaleOrderSourceExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户id")
	private Long custId;
	/**
	 * 仓库id
	 */
	@ColumnWidth(20)
	@ExcelProperty("仓库id")
	private Long warehouseId;
	/**
	 * 订单编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单编号")
	private String orderNo;
	/**
	 * 订单提交时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单提交时间")
	private LocalDateTime orderTime;
	/**
	 * 订单应付金额（包含订单总金额、配套、送货费、促销等）
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单应付金额（包含订单总金额、配套、送货费、促销等）")
	private Integer totalAmount;
	/**
	 * 送货费用
	 */
	@ColumnWidth(20)
	@ExcelProperty("送货费用")
	private Integer deliveryExpense;
	/**
	 * 订单实际支付金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单实际支付金额")
	private Integer payAmount;
	/**
	 * 授信额度支付金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("授信额度支付金额")
	private Integer creditaPayAmount;
	/**
	 * 钱包余额支付金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("钱包余额支付金额")
	private Integer walletPayAmount;
	/**
	 * 三方渠道支付金额
	 */
	@ColumnWidth(20)
	@ExcelProperty("三方渠道支付金额")
	private Integer channelPayAmount;
	/**
	 * 钱包余额补差
	 */
	@ColumnWidth(20)
	@ExcelProperty("钱包余额补差")
	private Integer walletAmountDiff;
	/**
	 * 支付方式：0->未支付；1->平台额度；2->钱包；3-混合支付；4-第三方支付
	 */
	@ColumnWidth(20)
	@ExcelProperty("支付方式：0->未支付；1->平台额度；2->钱包；3-混合支付；4-第三方支付")
	private Integer payType;
	/**
	 * 订单来源：0->PC订单；1->app订单
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单来源：0->PC订单；1->app订单")
	private Integer sourceType;
	/**
	 * 订单类型：0->正常订单；1->秒杀订单；2->临时订单
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单类型：0->正常订单；1->秒杀订单；2->临时订单")
	private Integer orderType;
	/**
	 * 活动场次ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("活动场次ID")
	private Long sessionsId;
	/**
	 * 配送方式  1 配送 2 自提
	 */
	@ColumnWidth(20)
	@ExcelProperty("配送方式  1 配送 2 自提")
	private Integer deliveryType;
	/**
	 * 是否加单1 加单 0 正常单
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否加单1 加单 0 正常单")
	private Integer addOrder;
	/**
	 * 收货人名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货人名称")
	private String receiverName;
	/**
	 * 收货人手机号
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货人手机号")
	private String receiverPhone;
	/**
	 * 邮政编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("邮政编码")
	private String receiverPostCode;
	/**
	 * 省份/直辖市
	 */
	@ColumnWidth(20)
	@ExcelProperty("省份/直辖市")
	private String receiverProvince;
	/**
	 * 城市
	 */
	@ColumnWidth(20)
	@ExcelProperty("城市")
	private String receiverCity;
	/**
	 * 区
	 */
	@ColumnWidth(20)
	@ExcelProperty("区")
	private String receiverRegion;
	/**
	 * 详细地址(街道)
	 */
	@ColumnWidth(20)
	@ExcelProperty("详细地址(街道)")
	private String receiverDetailAddress;
	/**
	 * 订单备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单备注")
	private String note;
	/**
	 * 确认收货状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("确认收货状态")
	private Integer confirmStatus;
	/**
	 * 是否加框
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否加框")
	private Integer isAddBox;
	/**
	 * 订单提交时间戳
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单提交时间戳")
	private Long createTimeAt;
	/**
	 * 支付状态：0-待支付 1-支付中 2-支付成功 3-支付失败
	 */
	@ColumnWidth(20)
	@ExcelProperty("支付状态：0-待支付 1-支付中 2-支付成功 3-支付失败")
	private Integer payStatus;
	/**
	 * 0-未截单 1已截单
	 */
	@ColumnWidth(20)
	@ExcelProperty("0-未截单 1已截单")
	private Integer isCutOrder;
	/**
	 * 采购批次号
	 */
	@ColumnWidth(20)
	@ExcelProperty("采购批次号")
	private String batchNo;
	/**
	 * 退款原因/取消原因
	 */
	@ColumnWidth(20)
	@ExcelProperty("退款原因/取消原因")
	private String returnReason;
	/**
	 * 退款处理人员
	 */
	@ColumnWidth(20)
	@ExcelProperty("退款处理人员")
	private String returnHandleMan;
	/**
	 * 退款时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("退款时间")
	private LocalDateTime returnTime;
	/**
	 * 取消订单时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("取消订单时间")
	private LocalDateTime cancelTime;
	/**
	 * 取消订单人
	 */
	@ColumnWidth(20)
	@ExcelProperty("取消订单人")
	private Long cancelUserId;
	/**
	 * 支付时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("支付时间")
	private LocalDateTime payTime;
	/**
	 * 收货时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货时间")
	private LocalDateTime receiverTime;
	/**
	 * 仓库配置配送基础费用
	 */
	@ColumnWidth(20)
	@ExcelProperty("仓库配置配送基础费用")
	private Integer deliveryFee;
	/**
	 * 仓库配置服务费费用
	 */
	@ColumnWidth(20)
	@ExcelProperty("仓库配置服务费费用")
	private Integer serviceFee;
	/**
	 * 提货上传文件
	 */
	@ColumnWidth(20)
	@ExcelProperty("提货上传文件")
	private String takeDeliveryPath;
	/**
	 * 授信额度是否还款
	 */
	@ColumnWidth(20)
	@ExcelProperty("授信额度是否还款")
	private Integer isCreditaRepayment;
	/**
	 * 对接第三方支付编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("对接第三方支付编号")
	private String payOrderNo;
	/**
	 * 授信额度还款时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("授信额度还款时间")
	private LocalDateTime creditaRepayTime;
	/**
	 * 支付时间戳
	 */
	@ColumnWidth(20)
	@ExcelProperty("支付时间戳")
	private Long payTimeAt;
	/**
	 * 下单客户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("下单客户ID")
	private Long orderCustId;
	/**
	 * 确认收货方式：1. 手动确认收货；2. 自动确认收货。结算时推送才有该字段
	 */
	@ColumnWidth(20)
	@ExcelProperty("确认收货方式：1. 手动确认收货；2. 自动确认收货。结算时推送才有该字段")
	private Integer receiveMethod;

}
