<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.warehouse.mapper.WarehouseStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="warehouseStoreResultMap" type="org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreEntity">
        <result column="warehouse_id" property="warehouseId"/>
        <result column="biz_type" property="bizType"/>
        <result column="related_order_id" property="relatedOrderId"/>
        <result column="related_order_no" property="relatedOrderNo"/>
        <result column="related_source_id" property="relatedSourceId"/>
        <result column="note" property="note"/>
        <result column="create_time_at" property="createTimeAt"/>
    </resultMap>

    <resultMap id="WarehouseStorePageMap" type="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStorePageVO">
        <id column="id" property="id"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="store_no" property="storeNo"/>
        <result column="related_order_id" property="relatedOrderId"/>
        <result column="related_order_no" property="relatedOrderNo"/>
        <result column="biz_type" property="bizType"/>
        <result column="related_source_id" property="relatedSourceId"/>
        <result column="phone" property="phone"/>
        <result column="completion_time" property="completionTime"/>
        <result column="note" property="note"/>
        <result column="status" property="status"/>
        <result column="update_user" property="updateUser"/>
        <result column="store_item_id" property="storeItemId"/>
        <result column="product_id" property="productId"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sp_data" property="spData"/>
        <result column="expect_quantity" property="expectQuantity"/>
        <result column="actual_quantity" property="actualQuantity"/>
        <result column="actual_weight" property="actualWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result column="unit_price" property="unitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result column="unit_inquiry" property="unitInquiry" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result column="whole_price" property="wholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result column="whole_inquiry" property="wholeInquiry" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
    </resultMap>

    <resultMap id="ListByWarehouseAndSkuMap" type="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStoreBatchVO">
        <id column="id" property="id"/>
        <result column="store_no" property="storeNo"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="remaining_quantity" property="remainingQuantity"/>
        <result column="remaining_weight" property="remainingWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
    </resultMap>

    <resultMap id="storeByStoreIdAndSku" type="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStoreRecordVO">
        <id column="id" property="id"/>
        <result column="store_batch_no" property="storeBatchNo"/>
        <result column="completion_time" property="completionTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="store_quantity" property="storeQuantity"/>
        <result column="store_weight" property="storeWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
    </resultMap>

    <resultMap id="aggQuantityAndWeightMap" type="org.springblade.modules.business.warehouse.pojo.vo.AggQuantityWeightVO">
        <id column="id" property="id"/>
        <result column="quantity" property="quantity"/>
        <result column="weight" property="weight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
    </resultMap>

    <select id="selectWarehouseStorePage" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStorePageVO">
        SELECT s.id,s.warehouse_id,s.store_no,
        s.related_order_id,
        s.related_order_no,
        s.biz_type,
        s.related_source_id,
        s.phone,
        s.completion_time,
        s.note,
        s.status,
        s.update_user
        FROM chy_warehouse_store s
        WHERE s.is_deleted = 0 AND s.warehouse_id = #{ew.warehouseId}
        <if test="excludeBizTypes != null and excludeBizTypes.size() > 0">
            AND s.biz_type NOT IN
            <foreach collection="excludeBizTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ew.bizType != null">
            AND s.biz_type = #{ew.bizType}
        </if>
        <if test="ew.storeNo != null">
            AND s.store_no = #{ew.storeNo}
        </if>
        <if test="ew.completionTime != null">
            AND DATE_FORMAT(s.completion_time, '%Y-%m-%d') = #{ew.completionTime}
        </if>
        order by s.completion_time desc
    </select>

    <select id="selectByStoreNo" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseScopeStoreNoPageVO">
        SELECT
            wsi.product_id,
            wsi.product_sku_id,
            p.name as product_name,
            wsi.actual_quantity,
            wsi.actual_weight,
            wsi.unit_price,
            wsi.whole_price,
            CASE
                WHEN ro.supply_way = 2 THEN sfc.price
                ELSE NULL
            END as feePrice,
            CASE
                WHEN ro.supply_way = 2 THEN sfc.price_type
                ELSE NULL
            END as priceType,
            CASE
                WHEN ro.supply_way = 2 AND sfc.price_type = 0 THEN sfc.price * wsi.actual_quantity
                WHEN ro.supply_way = 2 AND sfc.price_type = 1 AND wsi.actual_weight > 0 THEN ROUND(sfc.price * wsi.actual_weight / 500)
                ELSE 0
            END as feeAmount,
            wst.support_trans_unit_id,
            cst.transport_name as support_trans_unit_name,
            wst.support_trans_num,
            wst.support_trans_price
        FROM
            chy_warehouse_store ws
                LEFT JOIN chy_warehouse_store_item wsi on ws.id=wsi.warehouse_store_id
                LEFT JOIN chy_product p ON wsi.product_id = p.id
                LEFT JOIN chy_replenishment_order ro ON ws.related_order_no = ro.replenishment_no AND ro.is_deleted = 0
                LEFT JOIN chy_sku_forwarding_charges sfc ON wsi.product_sku_id = sfc.sku_id AND sfc.is_deleted = 0
                LEFT JOIN chy_warehouse_support_trans wst ON wst.related_record_id = ws.id AND wst.biz_type = 1 AND wst.product_sku_id = wsi.product_sku_id AND wst.is_deleted = 0
                LEFT JOIN chy_transport_unit cst ON wst.support_trans_unit_id = cst.id
        WHERE
            ws.is_deleted = 0 AND wsi.is_deleted = 0 AND (ws.related_order_no = #{storeNo} or ws.store_no = #{storeNo})
    </select>


    <select id="exportWarehouseStore" resultType="org.springblade.modules.business.warehouse.excel.WarehouseStoreExcel">
        SELECT * FROM chy_warehouse_store ${ew.customSqlSegment}
    </select>

    <select id="listByWarehouseAndSku" resultMap="ListByWarehouseAndSkuMap">
        SELECT s.store_no, s.id, i.product_sku_id,i.remaining_quantity,i.remaining_weight
        FROM chy_warehouse_store s
        LEFT JOIN chy_warehouse_store_item i
        ON i.warehouse_store_id = s.id AND i.is_deleted = 0
        WHERE s.is_deleted = 0 AND s.status = 1
        AND s.warehouse_id = #{warehouseId}
        AND i.product_sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        AND (remaining_quantity > 0 OR remaining_weight > 0)
    </select>

    <select id="getStoreByStoreIdAndSku" resultMap="storeByStoreIdAndSku">
        SELECT s.id,
               s.store_no        AS store_batch_no,
               s.completion_time,
               s.update_user,
               i.actual_quantity AS store_quantity,
               i.actual_weight   AS store_weight,
               s.biz_type,s.warehouse_id,w.warehouse_name AS warehouse_name
        FROM chy_warehouse_store s
                 INNER JOIN chy_warehouse_store_item i ON i.warehouse_store_id = s.id AND i.is_deleted = 0
                 inner join chy_warehouse w on w.id = s.warehouse_id
        WHERE s.id = #{storeId}
          AND i.product_sku_id = #{skuId}
          AND s.is_deleted = 0
          AND s.`status` = 1
        limit 1
    </select>

    <select id="aggQuantityAndWeight" resultMap="aggQuantityAndWeightMap">
        SELECT
        s.related_order_id AS id,
        SUM(i.actual_quantity) AS quantity,
        SUM(i.actual_weight) AS weight
        FROM chy_warehouse_store s
        LEFT JOIN chy_warehouse_store_item i ON i.warehouse_store_id = s.id AND i.is_deleted = 0
        WHERE s.is_deleted = 0 AND s.related_order_id IN
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        GROUP BY s.related_order_id
    </select>

    <select id="selectInboundRecordsForDiffDetail" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStoreRecordVO">
        WITH DiffAdjustments AS (
            SELECT
                ws.related_order_id as original_store_id,
                wsi.product_sku_id,
                wsi.supplier_id,
                SUM(wsi.actual_quantity) as diff_quantity,
                SUM(wsi.actual_weight) as diff_weight
            FROM chy_warehouse_store ws
                     JOIN chy_warehouse_store_item wsi ON ws.id = wsi.warehouse_store_id
            WHERE ws.biz_type = 6 -- 差异入库
              AND ws.is_deleted = 0 AND wsi.is_deleted = 0
                <if test="warehouseId != null and warehouseId != ''">AND ws.warehouse_id = #{warehouseId}</if>
                <if test="supplierId != null and supplierId != ''">AND wsi.supplier_id = #{supplierId}</if>
                <if test="productSkuId != null and productSkuId != ''">AND wsi.product_sku_id = #{productSkuId}</if>
                <if test="startTime != null and startTime != ''">AND ws.completion_time &gt;= #{startTime}</if>
                <if test="endTime != null and endTime != ''">AND ws.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
            GROUP BY ws.related_order_id, wsi.product_sku_id, wsi.supplier_id
        ),
             DiffSupportAdjustments AS (
                 SELECT
                     ws.related_order_id as original_store_id,
                     wst.product_sku_id,
                     wst.supplier_id,
                     SUM(wst.support_trans_num) as diff_support_num
                 FROM chy_warehouse_store ws
                          JOIN chy_warehouse_support_trans wst ON ws.id = wst.related_record_id AND wst.biz_type = 1
                 WHERE ws.biz_type = 6 -- 差异入库
                   AND ws.is_deleted = 0 AND wst.is_deleted = 0
                     <if test="warehouseId != null and warehouseId != ''">AND ws.warehouse_id = #{warehouseId}</if>
                     <if test="supplierId != null and supplierId != ''">AND wst.supplier_id = #{supplierId}</if>
                     <if test="productSkuId != null and productSkuId != ''">AND wst.product_sku_id = #{productSkuId}</if>
                     <if test="startTime != null and startTime != ''">AND ws.completion_time &gt;= #{startTime}</if>
                     <if test="endTime != null and endTime != ''">AND ws.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
                 GROUP BY ws.related_order_id, wst.product_sku_id, wst.supplier_id
             ),
             InboundScope AS (
                 SELECT
                     ws.id as record_id,
                     wsi.product_sku_id,
                     wsi.supplier_id
                 FROM chy_warehouse_store ws
                          JOIN chy_warehouse_store_item wsi ON ws.id = wsi.warehouse_store_id
                 WHERE ws.is_deleted = 0 AND wsi.is_deleted = 0 AND ws.status = 1 AND ws.biz_type NOT IN (6, 7)
                   <if test="warehouseId != null and warehouseId != ''">AND ws.warehouse_id = #{warehouseId}</if>
                   <if test="supplierId != null and supplierId != ''">AND wsi.supplier_id = #{supplierId}</if>
                   <if test="productSkuId != null and productSkuId != ''">AND wsi.product_sku_id = #{productSkuId}</if>
                   <if test="startTime != null and startTime != ''">AND ws.completion_time &gt;= #{startTime}</if>
                   <if test="endTime != null and endTime != ''">AND ws.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
                 UNION
                 SELECT
                     ws.id as record_id,
                     wst.product_sku_id,
                     wst.supplier_id
                 FROM chy_warehouse_store ws
                          JOIN chy_warehouse_support_trans wst ON ws.id = wst.related_record_id
                 WHERE ws.is_deleted = 0 AND wst.is_deleted = 0 AND wst.biz_type = 1 AND ws.status = 1 AND ws.biz_type NOT IN (6, 7)
                   <if test="warehouseId != null and warehouseId != ''">AND ws.warehouse_id = #{warehouseId}</if>
                   <if test="supplierId != null and supplierId != ''">AND wst.supplier_id = #{supplierId}</if>
                   <if test="productSkuId != null and productSkuId != ''">AND wst.product_sku_id = #{productSkuId}</if>
                   <if test="startTime != null and startTime != ''">AND ws.completion_time &gt;= #{startTime}</if>
                   <if test="endTime != null and endTime != ''">AND ws.completion_time &lt;= CONCAT(#{endTime}, ' 23:59:59')</if>
             )
        SELECT
            ws.id,
            ws.store_no as storeBatchNo,
            ws.biz_type as bizType,
            ws.related_order_no as relatedOrderNo,
            ws.completion_time as completionTime,
            ws.update_user as updateUser,
            ws.warehouse_id as warehouseId,
            w.warehouse_name as warehouseName,

            ps.product_id as productId,
            scope.product_sku_id as productSkuId,
            (COALESCE(wsi.actual_quantity, 0) + COALESCE(da.diff_quantity, 0)) as storeQuantity,
            (COALESCE(wsi.actual_weight, 0) + COALESCE(da.diff_weight, 0)) as storeWeight,
            scope.supplier_id as supplierId,

            p.name as productName,
            ps.sku_code as skuCode,
            ps.sp_data as spData,

            sup.name as supplierName,

            cst.transport_name as supportTransUnitName,
            (COALESCE(wst.support_trans_num, 0) + COALESCE(dsa.diff_support_num, 0)) as storeSupportTransNum,
            wst.support_trans_unit_id as supportTransUnitId
        FROM
            InboundScope scope
                JOIN chy_warehouse_store ws ON scope.record_id = ws.id
                LEFT JOIN chy_warehouse_store_item wsi ON scope.record_id = wsi.warehouse_store_id AND scope.product_sku_id = wsi.product_sku_id AND wsi.is_deleted = 0
                LEFT JOIN DiffAdjustments da ON ws.id = da.original_store_id AND scope.product_sku_id = da.product_sku_id AND scope.supplier_id = da.supplier_id
                LEFT JOIN DiffSupportAdjustments dsa ON ws.id = dsa.original_store_id AND scope.product_sku_id = dsa.product_sku_id AND scope.supplier_id = dsa.supplier_id
                LEFT JOIN chy_supplier sup ON scope.supplier_id = sup.id
                LEFT JOIN chy_warehouse_support_trans wst ON wst.related_record_id = ws.id AND wst.biz_type = 1 AND scope.product_sku_id = wst.product_sku_id AND scope.supplier_id = wst.supplier_id AND wst.is_deleted = 0
                LEFT JOIN chy_transport_unit cst ON wst.support_trans_unit_id = cst.id
                LEFT JOIN chy_sku_stock ps ON scope.product_sku_id = ps.id
                LEFT JOIN chy_product p ON ps.product_id = p.id
                LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id
        WHERE
            ws.is_deleted = 0 AND ws.status = 1 AND ws.biz_type NOT IN (6, 7)
        ORDER BY ws.completion_time DESC
    </select>

    <select id="listByWarehouseIdAndSkuIds" resultType="org.springblade.modules.business.warehouse.pojo.vo.WarehouseStoreRecordVO">
        SELECT
            wsi.*
        FROM
            chy_warehouse_store ws
            JOIN chy_warehouse_store_item wsi ON ws.id = wsi.warehouse_store_id
        WHERE
            ws.is_deleted = 0 AND wsi.is_deleted = 0
            AND ws.warehouse_id = #{warehouseId}
            AND wsi.product_sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId}
            </foreach>
    </select>

    <!-- =================================== 入库记录统计 =================================== -->
    <sql id="statistics_common_from_join">
        FROM chy_warehouse_store ws
        INNER JOIN chy_warehouse_store_item wsi ON ws.id = wsi.warehouse_store_id AND wsi.is_deleted = 0
        INNER JOIN chy_replenishment_order_item roi ON wsi.replenishment_item_id = roi.id AND roi.is_deleted = 0
        LEFT JOIN chy_product p ON wsi.product_id = p.id AND p.is_deleted = 0
        LEFT JOIN chy_base_unit bu ON p.base_unit_id = bu.id AND bu.is_deleted = 0
        LEFT JOIN chy_warehouse_support_trans wst ON wsi.id = wst.related_record_item_id AND wst.is_deleted = 0
        LEFT JOIN chy_transport_unit tu ON wst.support_trans_unit_id = tu.id AND tu.is_deleted = 0
    </sql>

    <sql id="statistics_common_where">
        WHERE ws.is_deleted = 0 AND ws.status = 1
        <if test="ew.bizType != null">
            AND ws.biz_type = #{ew.bizType}
        </if>
        <if test="ew.bizType == null">
            AND ws.biz_type IN (1, 2)
        </if>
        <if test="ew.startTime != null and ew.startTime != ''">
            AND ws.create_time_at &gt;= UNIX_TIMESTAMP(#{ew.startTime}) * 1000
        </if>
        <if test="ew.endTime != null and ew.endTime != ''">
            AND ws.create_time_at &lt;= (UNIX_TIMESTAMP(CONCAT(#{ew.endTime}, ' 23:59:59'))) * 1000
        </if>
        <if test="ew.warehouseId != null">
            AND ws.warehouse_id = #{ew.warehouseId}
        </if>
        <if test="ew.purchaserId != null">
            AND po.purchaser_id = #{ew.purchaserId}
        </if>
        <if test="ew.productCategoryId != null">
            AND p.product_category_id = #{ew.productCategoryId}
        </if>
        <if test="ew.keyword != null and ew.keyword != ''">
            AND (p.name LIKE CONCAT('%', #{ew.keyword}, '%') OR wsi.sku_code LIKE CONCAT('%', #{ew.keyword}, '%'))
        </if>
        <if test="ew.supplierId != null">
            AND wsi.supplier_id = #{ew.supplierId}
        </if>
    </sql>

    <resultMap id="baseProductSummary" type="org.springblade.modules.business.warehouse.pojo.vo.ProductSummaryVO">
        <result property="skuCode" column="skuCode"/>
        <result property="productName" column="productName"/>
        <result property="isStandard" column="isStandard"/>
        <result property="spData" column="spData"/>
        <result property="baseUnitName" column="baseUnitName"/>
        <result property="purchaseQuantity" column="purchaseQuantity"/>
        <result property="purchaseWholePrice" column="purchaseWholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseUnitPrice" column="purchaseUnitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseTotalAmount" column="purchaseTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeQuantity" column="storeQuantity"/>
        <result property="storeWeight" column="storeWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result property="storeWholePrice" column="storeWholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeUnitPrice" column="storeUnitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeTotalAmount" column="storeTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="supportTransName" column="supportTransName"/>
        <result property="purchaseSupportTransNum" column="purchaseSupportTransNum"/>
        <result property="supportTransPrice" column="supportTransPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseSupportTransTotalAmount" column="purchaseSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeSupportTransNum" column="storeSupportTransNum"/>
        <result property="storeSupportTransTotalAmount" column="storeSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
    </resultMap>
    <resultMap id="resultPartitionSummary" type="org.springblade.modules.business.warehouse.pojo.vo.PartitionSummaryVO" extends="baseProductSummary">
        <result property="warehouseName" column="warehouseName"/>
    </resultMap>
    <resultMap id="resultSupplierSummary" type="org.springblade.modules.business.warehouse.pojo.vo.SupplierSummaryVO" extends="baseProductSummary">
        <result property="supplierName" column="supplierName"/>
        <result property="purchaserName" column="purchaserName"/>
    </resultMap>
    <resultMap id="resultPurchaserSummary" type="org.springblade.modules.business.warehouse.pojo.vo.PurchaserSummaryVO" extends="baseProductSummary">
        <result property="purchaserName" column="purchaserName"/>
    </resultMap>

    <select id="getPartitionSummary" resultMap="resultPartitionSummary">
        SELECT
            w.warehouse_name AS warehouseName,
            wsi.sku_code AS skuCode,
            p.name AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            wsi.sp_data AS spData,
            bu.unit_name AS baseUnitName,
            SUM(roi.quantity) AS purchaseQuantity,
            roi.whole_price AS purchaseWholePrice,
            roi.unit_price AS purchaseUnitPrice,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                    ELSE 0
                END
            ) AS purchaseTotalAmount,
            SUM(wsi.actual_quantity) AS storeQuantity,
            SUM(wsi.actual_weight) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                END
            ) AS storeTotalAmount,
            tu.transport_name AS supportTransName,
            SUM(roi.support_trans_num) as purchaseSupportTransNum,
            wst.support_trans_price AS supportTransPrice,
            COALESCE(SUM(roi.support_trans_num * roi.support_trans_price), 0) as purchaseSupportTransTotalAmount,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount
        <include refid="statistics_common_from_join"/>
        LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id AND w.is_deleted = 0
        <if test="ew.purchaserId != null">
            LEFT JOIN chy_purchase_order po ON roi.purchase_order_id = po.id AND po.is_deleted = 0
        </if>
        <if test="ew.supplierId != null">
            LEFT JOIN chy_supplier sup ON wsi.supplier_id = sup.id AND sup.is_deleted = 0
        </if>
        <include refid="statistics_common_where"/>
        GROUP BY
            wsi.warehouse_id,
            wsi.product_sku_id,
            roi.whole_price,
            roi.unit_price,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price
    </select>

    <select id="getProductSummary" resultMap="baseProductSummary">
        SELECT
            wsi.sku_code AS skuCode,
            p.name AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            wsi.sp_data AS spData,
            bu.unit_name AS baseUnitName,
            SUM(roi.quantity) AS purchaseQuantity,
            roi.whole_price AS purchaseWholePrice,
            roi.unit_price AS purchaseUnitPrice,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                ELSE 0
                END
            ) AS purchaseTotalAmount,
            SUM(wsi.actual_quantity) AS storeQuantity,
            SUM(wsi.actual_weight) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                ELSE 0
                END
            ) AS storeTotalAmount,
            tu.transport_name AS supportTransName,
            SUM(roi.support_trans_num) as purchaseSupportTransNum,
            wst.support_trans_price AS supportTransPrice,
            COALESCE(SUM(roi.support_trans_num * roi.support_trans_price), 0) as purchaseSupportTransTotalAmount,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount
        <include refid="statistics_common_from_join"/>
        <if test="ew.warehouseId != null">
            LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id AND w.is_deleted = 0
        </if>
        <if test="ew.purchaserId != null">
            LEFT JOIN chy_purchase_order po ON roi.purchase_order_id = po.id AND po.is_deleted = 0
        </if>
        <if test="ew.supplierId != null">
            LEFT JOIN chy_supplier sup ON wsi.supplier_id = sup.id AND sup.is_deleted = 0
        </if>
        <include refid="statistics_common_where"/>
        GROUP BY
            wsi.product_sku_id,
            roi.whole_price,
            roi.unit_price,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price
    </select>

    <select id="getSupplierSummary" resultMap="resultSupplierSummary">
        SELECT
            sup.full_name AS supplierName,
            pu.real_name as purchaserName,
            wsi.sku_code AS skuCode,
            p.name AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            wsi.sp_data AS spData,
            bu.unit_name AS baseUnitName,
            SUM(roi.quantity) AS purchaseQuantity,
            roi.whole_price AS purchaseWholePrice,
            roi.unit_price AS purchaseUnitPrice,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                    ELSE 0
                    END
            ) AS purchaseTotalAmount,
            SUM(wsi.actual_quantity) AS storeQuantity,
            SUM(wsi.actual_weight) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                    END
            ) AS storeTotalAmount,
            tu.transport_name AS supportTransName,
            SUM(roi.support_trans_num) as purchaseSupportTransNum,
            wst.support_trans_price AS supportTransPrice,
            COALESCE(SUM(roi.support_trans_num * roi.support_trans_price), 0) as purchaseSupportTransTotalAmount,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount
        <include refid="statistics_common_from_join"/>
        <if test="ew.warehouseId != null">
            LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id AND w.is_deleted = 0
        </if>
        LEFT JOIN chy_purchase_order po ON roi.purchase_order_id = po.id AND po.is_deleted = 0
        LEFT JOIN chy_supplier sup ON wsi.supplier_id = sup.id AND sup.is_deleted = 0
        LEFT JOIN blade_user pu ON po.purchaser_id = pu.id AND pu.is_deleted = 0
        <include refid="statistics_common_where"/>
        GROUP BY
            wsi.supplier_id,
            wsi.product_sku_id,
            roi.whole_price,
            roi.unit_price,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price
    </select>

    <select id="getPurchaserSummary" resultMap="resultPurchaserSummary">
        SELECT
            pu.real_name as purchaserName,
            wsi.sku_code AS skuCode,
            p.name AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            wsi.sp_data AS spData,
            bu.unit_name AS baseUnitName,
            SUM(roi.quantity) AS purchaseQuantity,
            roi.whole_price AS purchaseWholePrice,
            roi.unit_price AS purchaseUnitPrice,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                    ELSE 0
                END
            ) AS purchaseTotalAmount,
            SUM(wsi.actual_quantity) AS storeQuantity,
            SUM(wsi.actual_weight) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                END
            ) AS storeTotalAmount,
            tu.transport_name AS supportTransName,
            SUM(roi.support_trans_num) as purchaseSupportTransNum,
            wst.support_trans_price AS supportTransPrice,
            COALESCE(SUM(roi.support_trans_num * roi.support_trans_price), 0) as purchaseSupportTransTotalAmount,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount
        <include refid="statistics_common_from_join"/>
        <if test="ew.warehouseId != null">
            LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id AND w.is_deleted = 0
        </if>
        LEFT JOIN chy_purchase_order po ON roi.purchase_order_id = po.id AND po.is_deleted = 0
        LEFT JOIN blade_user pu ON po.purchaser_id = pu.id AND pu.is_deleted = 0
        <include refid="statistics_common_where"/>
        GROUP BY
            po.purchaser_id,
            wsi.product_sku_id,
            roi.whole_price,
            roi.unit_price,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price
    </select>

    <resultMap id="resultStatisticsTotal" type="org.springblade.modules.business.warehouse.pojo.vo.TotalSummaryVO">
        <result property="purchaseQuantity" column="purchaseQuantity"/>
        <result property="purchaseTotalAmount" column="purchaseTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeQuantity" column="storeQuantity"/>
        <result property="storeWeight" column="storeWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result property="storeTotalAmount" column="storeTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseSupportTransNum" column="purchaseSupportTransNum"/>
        <result property="purchaseSupportTransTotalAmount" column="purchaseSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeSupportTransNum" column="storeSupportTransNum"/>
        <result property="storeSupportTransTotalAmount" column="storeSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
    </resultMap>

    <select id="getStatisticsTotal" resultMap="resultStatisticsTotal">
        SELECT
            SUM(roi.quantity) AS purchaseQuantity,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                ELSE 0
                END
            ) AS purchaseTotalAmount,
            COALESCE(SUM(wsi.actual_quantity), 0) AS storeQuantity,
            COALESCE(SUM(wsi.actual_weight), 0) AS storeWeight,
            COALESCE(SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                END
            ), 0) AS storeTotalAmount,
            COALESCE(SUM(roi.support_trans_num), 0) as purchaseSupportTransNum,
            COALESCE(SUM(roi.support_trans_num * roi.support_trans_price), 0) as purchaseSupportTransTotalAmount,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount
        <include refid="statistics_common_from_join"/>
        <if test="ew.warehouseId != null">
            LEFT JOIN chy_warehouse w ON ws.warehouse_id = w.id AND w.is_deleted = 0
        </if>
        <if test="ew.purchaserId != null">
            LEFT JOIN chy_purchase_order po ON roi.purchase_order_id = po.id AND po.is_deleted = 0
        </if>
        <if test="ew.supplierId != null">
            LEFT JOIN chy_supplier sup ON wsi.supplier_id = sup.id AND sup.is_deleted = 0
        </if>
        <include refid="statistics_common_where"/>
    </select>

    <resultMap id="resultSupplierStatistics" type="org.springblade.modules.business.warehouse.pojo.vo.SupplierStatisticsVO">
        <result property="replenishmentNo" column="replenishmentNo"/>
        <result property="skuCode" column="skuCode"/>
        <result property="productName" column="productName"/>
        <result property="isStandard" column="isStandard"/>
        <result property="spData" column="spData"/>
        <result property="baseUnitName" column="baseUnitName"/>
        <result property="purchaseQuantity" column="purchaseQuantity"/>
        <result property="purchaseWholePrice" column="purchaseWholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseUnitPrice" column="purchaseUnitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseTotalAmount" column="purchaseTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseSupportTransName" column="purchaseSupportTransName"/>
        <result property="purchaseSupportTransPrice" column="purchaseSupportTransPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="purchaseSupportTransNum" column="purchaseSupportTransNum"/>
        <result property="purchaseSupportTransTotalAmount" column="purchaseSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeQuantity" column="storeQuantity"/>
        <result property="storeWeight" column="storeWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result property="diffQuantity" column="diffQuantity"/>
        <result property="diffWeight" column="diffWeight" typeHandler="org.springblade.common.handle.WeightTypeHandle"/>
        <result property="storeWholePrice" column="storeWholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeUnitPrice" column="storeUnitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="diffWholePrice" column="diffWholePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="diffUnitPrice" column="diffUnitPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeTotalAmount" column="storeTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeSupportTransName" column="storeSupportTransName"/>
        <result property="storeSupportTransPrice" column="storeSupportTransPrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="storeSupportTransNum" column="storeSupportTransNum"/>
        <result property="diffSupportTransNum" column="diffSupportTransNum"/>
        <result property="storeSupportTransTotalAmount" column="storeSupportTransTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="forwardingFeeType" column="forwardingFeeType"/>
        <result property="forwardingFeePrice" column="forwardingFeePrice" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
        <result property="forwardingFeeTotalAmount" column="forwardingFeeTotalAmount" typeHandler="org.springblade.common.handle.AmountTypeHandle"/>
    </resultMap>

    <select id="getSupplierDetailSummary" resultMap="resultSupplierStatistics">
        SELECT
            roi.sku_code AS skuCode,
            p.name AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            roi.sp_data AS spData,
            bu.unit_name AS baseUnitName,

            SUM(roi.quantity) AS purchaseQuantity,
            roi.whole_price AS purchaseWholePrice,
            roi.unit_price AS purchaseUnitPrice,
            SUM(
                CASE
                    WHEN roi.unit_price IS NOT NULL AND roi.unit_price > 0 AND roi.weight IS NOT NULL THEN roi.weight / 500.0 * roi.unit_price
                    WHEN roi.whole_price IS NOT NULL AND roi.whole_price > 0 THEN roi.quantity * roi.whole_price
                ELSE 0
                END
            ) AS purchaseTotalAmount,
            tur.transport_name AS purchaseSupportTransName,
            roi.support_trans_price AS purchaseSupportTransPrice,
            SUM(roi.support_trans_num) AS purchaseSupportTransNum,
            SUM(roi.support_trans_num * roi.support_trans_price) AS purchaseSupportTransTotalAmount,

            SUM(wsi.actual_quantity) AS storeQuantity,
            SUM(wsi.actual_weight) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                END
            ) AS storeTotalAmount,
            tu.transport_name AS storeSupportTransName,
            wst.support_trans_price AS storeSupportTransPrice,
            COALESCE(SUM(wst.support_trans_num), 0) AS storeSupportTransNum,
            COALESCE(SUM(wst.support_trans_num * wst.support_trans_price), 0) AS storeSupportTransTotalAmount,

            SUM(wsi.actual_quantity) - SUM(roi.quantity) AS diffQuantity,
            SUM(wsi.actual_weight) - SUM(roi.weight) AS diffWeight,
            (wsi.whole_price - roi.whole_price) as diffWholePrice,
            (wsi.unit_price - roi.unit_price) as diffUnitPrice,
            (COALESCE(SUM(wst.support_trans_num), 0) - SUM(roi.support_trans_num)) as diffSupportTransNum,

            tfi.price_type AS forwardingFeeType,
            tfi.price AS forwardingFeePrice,
            SUM(tfi.amount) as forwardingFeeTotalAmount
        FROM
            chy_replenishment_order ro
            INNER JOIN chy_replenishment_order_item roi ON ro.id = roi.replenishment_order_id
            LEFT JOIN chy_warehouse_store_item wsi ON wsi.replenishment_item_id = roi.id AND wsi.is_deleted = 0
            LEFT JOIN chy_transport_unit tur ON roi.support_trans_unit_id = tur.id AND tur.is_deleted = 0
            LEFT JOIN chy_product p ON roi.product_id = p.id AND p.is_deleted = 0
            LEFT JOIN chy_base_unit bu ON p.base_unit_id = bu.id AND bu.is_deleted = 0
            LEFT JOIN chy_warehouse_support_trans wst ON wsi.id = wst.related_record_item_id AND wst.is_deleted = 0
            LEFT JOIN chy_transport_unit tu ON wst.support_trans_unit_id = tu.id AND tu.is_deleted = 0
            LEFT JOIN chy_transport_fee tf ON ro.replenishment_no = tf.replenishment_no AND tf.is_deleted = 0
            LEFT JOIN chy_transport_fee_item tfi ON tf.id = tfi.transport_fee_id AND wsi.id = tfi.store_item_id AND tfi.is_deleted = 0
        WHERE
        ro.is_deleted = 0 AND ro.supplier_id = #{supplierId}
        <if test="startTime != null">
            AND ro.create_time_at &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND ro.create_time_at &lt;= #{endTime}
        </if>
        GROUP BY
            roi.product_sku_id,
            roi.sku_code,
            p.name,
            p.is_standard,
            roi.sp_data,
            bu.unit_name,
            tur.transport_name,
            tu.transport_name,
            roi.whole_price,
            roi.unit_price,
            roi.support_trans_price,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price,
            tfi.price_type,
            tfi.price
        UNION ALL
        SELECT
            wsi.sku_code AS skuCode,
            p.NAME AS productName,
            CASE p.is_standard WHEN 0 THEN '标品' ELSE '非标品' END AS isStandard,
            wsi.sp_data AS spData,
            bu.unit_name AS baseUnitName,
            null AS purchaseQuantity,
            null AS purchaseWholePrice,
            null AS purchaseUnitPrice,
            null AS purchaseTotalAmount,
            null AS purchaseSupportTransName,
            null AS purchaseSupportTransPrice,
            null AS purchaseSupportTransNum,
            null AS purchaseSupportTransTotalAmount,
            SUM( wsi.actual_quantity ) AS storeQuantity,
            SUM( wsi.actual_weight ) AS storeWeight,
            wsi.whole_price AS storeWholePrice,
            wsi.unit_price AS storeUnitPrice,
            SUM(
                CASE
                    WHEN wsi.unit_price IS NOT NULL AND wsi.unit_price > 0 AND wsi.actual_weight IS NOT NULL THEN wsi.actual_weight / 500.0 * wsi.unit_price
                    WHEN wsi.whole_price IS NOT NULL AND wsi.whole_price > 0 THEN wsi.actual_quantity * wsi.whole_price
                    ELSE 0
                END
            ) AS storeTotalAmount,
            tu.transport_name AS storeSupportTransName,
            wst.support_trans_price AS storeSupportTransPrice,
            COALESCE ( SUM( wst.support_trans_num ), 0 ) AS storeSupportTransNum,
            COALESCE ( SUM( wst.support_trans_num * wst.support_trans_price ), 0 ) AS storeSupportTransTotalAmount,
            null AS diffQuantity,
            null AS diffWeight,
            null AS diffWholePrice,
            null AS diffUnitPrice,
            null AS diffSupportTransNum,
            null AS forwardingFeeType,
            null AS forwardingFeePrice,
            null AS forwardingFeeTotalAmount
        FROM
            chy_warehouse_store ws
            INNER JOIN chy_warehouse_store_item wsi ON ws.id = wsi.warehouse_store_id AND wsi.is_deleted = 0
            LEFT JOIN chy_product p ON wsi.product_id = p.id AND p.is_deleted = 0
            LEFT JOIN chy_base_unit bu ON p.base_unit_id = bu.id AND bu.is_deleted = 0
            LEFT JOIN chy_warehouse_support_trans wst ON wsi.id = wst.related_record_item_id AND wst.is_deleted = 0
            LEFT JOIN chy_transport_unit tu ON wst.support_trans_unit_id = tu.id AND tu.is_deleted = 0
        WHERE
            ws.is_deleted = 0 AND ws.STATUS = 1 AND ws.biz_type = 2 AND wsi.supplier_id = #{supplierId}
            <if test="startTime != null">
                AND ws.create_time_at &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND ws.create_time_at &lt;= #{endTime}
            </if>
        GROUP BY
            wsi.product_sku_id,
            wsi.sku_code,
            p.name,
            p.is_standard,
            wsi.sp_data,
            bu.unit_name,
            tu.transport_name,
            wsi.whole_price,
            wsi.unit_price,
            wst.support_trans_price
    </select>

</mapper>
