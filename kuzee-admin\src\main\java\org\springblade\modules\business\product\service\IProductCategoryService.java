/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.product.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.validation.Valid;
import org.springblade.modules.business.product.pojo.dto.ProductCategoryDTO;
import org.springblade.modules.business.product.pojo.dto.SaveProductCategoryDTO;
import org.springblade.modules.business.product.pojo.entity.ProductCategoryEntity;
import org.springblade.modules.business.product.pojo.vo.OneListVO;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryTreeVO;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryVO;
import org.springblade.modules.business.product.excel.ProductCategoryExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryWithChildrenItem;

import java.util.List;
import java.util.Set;

/**
 * 商品分类表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface IProductCategoryService extends BaseService<ProductCategoryEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param productCategory 查询参数
	 * @return IPage<ProductCategoryVO>
	 */
	IPage<ProductCategoryVO> selectProductCategoryPage(IPage<ProductCategoryVO> page, ProductCategoryVO productCategory);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<ProductCategoryExcel>
	 */
	List<ProductCategoryExcel> exportProductCategory(Wrapper<ProductCategoryEntity> queryWrapper);

	boolean saveProductCategory(@Valid SaveProductCategoryDTO saveProductCategoryDTO);

	List<ProductCategoryWithChildrenItem> listWithChildren(String name);

	List<ProductCategoryEntity> getAll();

	/**
	 * 通过分类ID查找分类及其所有子分类
	 *
	 * @param categoryId 分类ID
	 * @return 分类及其子分类的树形结构
	 */
	ProductCategoryWithChildrenItem findCategoryWithChildrenById(Long categoryId);

	/**
	 * 通过分类ID查找此分类及其所有子分类的ID集合
	 *
	 * @param categoryId 分类ID
	 * @return 包含此分类ID及其所有子分类ID的集合
	 */
	Set<Long> findCategoryIdsById(Long categoryId);

    List<ProductCategoryTreeVO> listTree(QueryWrapper<ProductCategoryEntity> queryWrapper);

	List<OneListVO> getOneList();
}
