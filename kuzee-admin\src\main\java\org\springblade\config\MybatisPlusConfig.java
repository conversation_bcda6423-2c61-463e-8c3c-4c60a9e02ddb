package org.springblade.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@MapperScan("org.springblade.**.mapper.**")
public class MybatisPlusConfig  {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

//    @Override
//    public void afterPropertiesSet() {
//        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
//            org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
//            // 注册自定义的 TypeHandler
//            configuration.getTypeHandlerRegistry().register(NullValueTypeHandler.class);
//        }
//    }


}
