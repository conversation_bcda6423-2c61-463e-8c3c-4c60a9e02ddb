/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.job.CutOrderJob;
import org.springblade.modules.business.order.pojo.dto.*;
import org.springblade.modules.business.order.pojo.entity.PurchaseOrderEntity;
import org.springblade.modules.business.order.pojo.vo.*;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.order.wrapper.PurchaseOrderWrapper;
import org.springblade.modules.business.product.pojo.entity.SkuWarehouseRelationEntity;
import org.springblade.modules.business.product.pojo.entity.TransportUnitEntity;
import org.springblade.modules.business.product.service.ISkuOemService;
import org.springblade.modules.business.product.service.ISkuPpService;
import org.springblade.modules.business.product.service.ISkuWarehouseRelationService;
import org.springblade.modules.business.product.service.ITransportUnitService;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.TransportFeeVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseJobVO;
import org.springblade.modules.business.warehouse.service.*;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采购单主表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/purchaseOrder")
@Tag(name = "采购单主表", description = "采购单主表接口")
public class PurchaseOrderController extends BladeController {

	private final ISaleOrderService saleOrderService;
	private final ISaleOrderItemService saleOrderItemService;
	private final IWarehouseService warehouseService;
	private final IWarehousePurchaseService warehousePurchaseService;
	private final IPurchaseOrderService purchaseOrderService;
	private final IPurchaseOrderItemService purchaseOrderItemService;
	private final IInventoryService inventoryService;
	private final ISkuOemService skuOemService;
	private final ISkuPpService skuPpService;
	private final ITransportOrderService iTransportOrderService;
	private final ITransportOrderItemService iTransportOrderItemService;
	private final ISkuWarehouseRelationService iSkuWarehouseRelationService;

	private final IPurchaseOrderSaleItemService purchaseOrderSaleItemService;

	private final ITransportUnitService transportUnitService;
	private final RedisLockClient redisLockClient;
	private final IPurchaseAllocationService purchaseAllocationService;

	private final CutOrderJob cutOrderJob;
	/**
	 * 采购单主表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入purchaseOrder")
	public R<PurchaseOrderVO> detail(PurchaseOrderEntity purchaseOrder) {
		PurchaseOrderEntity detail = purchaseOrderService.getOne(Condition.getQueryWrapper(purchaseOrder));
		return R.data(PurchaseOrderWrapper.build().entityVO(detail));
	}

	/**
	 * 采购单主表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入purchaseOrder")
	public R<IPage<PurchaseOrderVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> purchaseOrder, Query query) {
		IPage<PurchaseOrderEntity> pages = purchaseOrderService.page(Condition.getPage(query), Condition.getQueryWrapper(purchaseOrder, PurchaseOrderEntity.class));
		return R.data(PurchaseOrderWrapper.build().pageVO(pages));
	}


	/**
	 * 采购单主表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入purchaseOrder")
	public R<IPage<PurchaseOrderVO>> page(PurchaseOrderVO purchaseOrder, Query query) {
		IPage<PurchaseOrderVO> pages = purchaseOrderService.selectPurchaseOrderPage(Condition.getPage(query), purchaseOrder);
		return R.data(pages);
	}

	/**
	 * 采购单主表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入purchaseOrder")
	public R save(@Valid @RequestBody PurchaseOrderEntity purchaseOrder) {
		return R.status(purchaseOrderService.save(purchaseOrder));
	}

	/**
	 * 销售订单表 调整批次
	 */
	@PostMapping("/adjust-batch")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "调整批次", description = "传入批次号")
	public R adjustBatch(@Parameter(description = "传入订单id", required = true) @RequestParam List<Long> orderIds, @Parameter(description = "传入批次号", required = true) @RequestParam String batchNo) {
		return R.status(purchaseOrderService.adjustBatch(orderIds, batchNo));
	}

	/**
	 * 采购单主表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入purchaseOrder")
	public R update(@Valid @RequestBody PurchaseOrderEntity purchaseOrder) {
		return R.status(purchaseOrderService.updateById(purchaseOrder));
	}

	/**
	 * 采购单主表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入purchaseOrder")
	public R submit(@Valid @RequestBody PurchaseOrderEntity purchaseOrder) {
		return R.status(purchaseOrderService.saveOrUpdate(purchaseOrder));
	}

	/**
	 * 采购单主表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(purchaseOrderService.deleteLogic(Func.toLongList(ids)));
	}

//	/**
//	 * 导出数据
//	 */
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
//	@GetMapping("/export-purchaseOrder")
//	@ApiOperationSupport(order = 8)
//	@Operation(summary = "导出数据", description  = "传入purchaseOrder")
//	public void exportPurchaseOrder(@Parameter(hidden = true) @RequestParam Map<String, Object> purchaseOrder, BladeUser bladeUser, HttpServletResponse response) {
//		QueryWrapper<PurchaseOrderEntity> queryWrapper = Condition.getQueryWrapper(purchaseOrder, PurchaseOrderEntity.class);
//		//if (!AuthUtil.isAdministrator()) {
//		//	queryWrapper.lambda().eq(PurchaseOrderEntity::getTenantId, bladeUser.getTenantId());
//		//}
//		//queryWrapper.lambda().eq(PurchaseOrderEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
//		List<PurchaseOrderExcel> list = purchaseOrderService.exportPurchaseOrder(queryWrapper);
//		ExcelUtil.export(response, "采购单主表数据" + DateUtil.time(), "采购单主表数据表", list, PurchaseOrderExcel.class);
//	}

	/**
	 * 采购单主表 自定义分页
	 */
	@GetMapping("/pageList")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "采购单主表", description = "传入purchaseOrder")
	public R<IPage<PurchaseOrderPageListVO>> pageList(PurchaseOrderPageListDTO dto, Query query) {
		IPage<PurchaseOrderPageListVO> pages = purchaseOrderService.pageList(dto, query);
		return R.data(pages);
	}

	/**
	 * 入库采购（自采）商品列表
	 */
	@GetMapping("/getWarehousePurchaseList")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "入库采购（自采）商品列表", description = "入库采购（自采）商品列表")
	public R<List<WarehousePurchaseListVO>> getWarehousePurchaseList(WarehousePurchaseListDTO dto, Query query) {

//		WarehouseEntity warehouse = warehouseService.getByUserIdPurchase(AuthUtil.getUserId());
//		dto.setWarehouseId(warehouse.getId());
		//dto.setWarehouseId(1898916841057714178L);
		List<WarehousePurchaseListVO> pages = purchaseOrderService.getWarehousePurchaseList(dto, query);
		return R.data(pages);
	}

	/**
	 * 添加自采单
	 */
	@PostMapping("/addSelfPurchaseOrder")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "添加自采单", description = "添加自采单")
	public R addSelfPurchaseOrder(@Valid @RequestBody List<AddSelfPurchaseOrderDTO> dto) {
		//获取仓库
		//todo 获取仓库
		WarehouseEntity warehouseEntity = warehouseService.getByUserIdPurchase(AuthUtil.getUserId());
		//WarehouseEntity warehouseEntity = warehouseService.getById(1898916841057714178L);
		WarehouseJobVO warehouse = Objects.requireNonNull(BeanUtil.copyProperties(warehouseEntity, WarehouseJobVO.class));

		//获取销售订单商品
		List<SaleOrderItemJobVO> saleOrderList = purchaseOrderService.getSelfPurchaseOrder(dto, warehouse.getId());

		//获取采购员
		List<Long> purchaseIdList = new ArrayList<>();
		purchaseIdList.add(AuthUtil.getUserId());

		//获取运输单位
		List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
		if (saleOrderList.size() > 0) {
			transportUnitEntities = transportUnitService.getIds(saleOrderList.stream().map(SaleOrderItemJobVO::getProductSkuId).distinct().collect(Collectors.toList()));
		}

//		CutOrderJob sdk = new CutOrderJob(saleOrderService, saleOrderItemService, warehouseService, warehousePurchaseService,
//			purchaseOrderService, purchaseOrderItemService, inventoryService, skuOemService, skuPpService, iTransportOrderService,
//			iTransportOrderItemService, iSkuWarehouseRelationService, purchaseOrderSaleItemService, transportUnitService, redisLockClient);
		List<Long> ids=saleOrderList.stream().map(SaleOrderItemJobVO::getProductSkuId).toList();
		//获取专供商品并获取到对应的仓库
		List<SkuWarehouseRelationEntity> warehouseList = iSkuWarehouseRelationService.getWarehouseBySkuIds(ids);
		//判断对应的SkuID存在仓库等于1的集合
		Map<Long, Long> skuCountMap = warehouseList.stream()
			.collect(Collectors.groupingBy(SkuWarehouseRelationEntity::getSkuId, Collectors.counting()));
		// 筛选出只出现一次的 skuId
		List<Long> singleOccurrenceSkus = skuCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() == 1)
			.map(Map.Entry::getKey)
			.toList();
		// 获取这些 skuId 对应的原始记录
		List<SkuWarehouseRelationEntity> singleRecords = warehouseList.stream()
			.filter(relation -> singleOccurrenceSkus.contains(relation.getSkuId()))
			.toList();
		//总仓下单的数据
		List<SaleOrderItemJobVO> saleOrderItemJobVOS = saleOrderList.stream().filter(i -> !singleOccurrenceSkus.contains(i.getProductSkuId())).toList();
		if (saleOrderItemJobVOS.size() > 0){
			log.info("总仓下单的数据:{}",saleOrderItemJobVOS);
			cutOrderJob.GeneratePurchaseOrder(saleOrderItemJobVOS, warehouse, purchaseIdList, transportUnitEntities, BusinessConstant.ORDER_TYPE_SELF);
		}
		//获取对应的仓库
		if (singleRecords.size()>0) {
			List<WarehouseEntity> warehouseList1 = warehouseService.getWareHouseByIds(singleRecords.stream().map(SkuWarehouseRelationEntity::getWarehouseId).toList());
			List<SaleOrderItemJobVO> saleOrderItemJobVOS1 = saleOrderList.stream().filter(i -> singleOccurrenceSkus.contains(i.getProductSkuId())).toList();
			for (WarehouseEntity warehouse1 : warehouseList1) {
				log.info("分仓仓库下单的数据:{}", warehouse1);
				WarehouseJobVO warehouse1VO = Objects.requireNonNull(BeanUtil.copyProperties(warehouse1, WarehouseJobVO.class));
				List<SkuWarehouseRelationEntity> skuWarehouseRelationEntities = warehouseList.stream().filter(i -> i.getWarehouseId().equals(warehouse1.getId())).toList();
				List<Long> skuIds = skuWarehouseRelationEntities.stream().map(SkuWarehouseRelationEntity::getSkuId).toList();
				List<SaleOrderItemJobVO> saleOrderItemJobVOS2 = saleOrderItemJobVOS1.stream().filter(i -> skuIds.contains(i.getProductSkuId())).toList();
				for (SaleOrderItemJobVO itemJobVO : saleOrderItemJobVOS2) {
					itemJobVO.setWarehouseId(warehouse1.getId());
				}
				log.info("分仓仓库下单的数据:{}", saleOrderItemJobVOS2);
				cutOrderJob.GeneratePurchaseOrder(saleOrderItemJobVOS2, warehouse1VO, purchaseIdList, transportUnitEntities, BusinessConstant.ORDER_TYPE_SELF);
			}
		}
		return R.success("添加成功");
	}


	/**
	 * 采购单主表 自定义分页
	 */
	@GetMapping("/SaleOrderPageList")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "采购单主表", description = "传入purchaseOrder")
	public R<IPage<PurchaseOrderPageListVO>> SaleOrderPageList(PurchaseOrderPageListDTO dto, Query query) {
		IPage<PurchaseOrderPageListVO> pages = purchaseOrderService.SaleOrderPageList(dto, query);
		return R.data(pages);
	}

	/**
	 * 查询采购分配汇总
	 */
	@GetMapping("/allocation-summary")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "查询采购分配汇总", description = "根据采购员、日期、批次查询分配量")
	public R<IPage<PurchaseAllocationSummaryVO>> getAllocationSummary(PurchaseAllocationSummaryDTO dto, Query query) {
		return R.data(purchaseAllocationService.getPurchaseAllocationSummary(dto, query));
	}

	/**
	 * 查询采购分配详情
	 */
	@GetMapping("/allocation-detail")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "查询采购分配详情", description = "根据采购单ID查询分配详情")
	public R<List<PurchaseAllocationDetailVO>> getAllocationDetail(PurchaseAllocationDetailDTO dto) {
		return R.data(purchaseAllocationService.getPurchaseAllocationDetail(dto));
	}

	/**
	 * 报缺
	 */
	@PostMapping("/missing")
	@ApiOperationSupport(order = 14)
	@Operation(summary = "报缺", description = "报缺")
	public R<?> missing(@Valid @RequestBody PurchaseOrderMissingItemDTO dto) {
		purchaseOrderService.missing(dto);
		return R.success();
	}
}
