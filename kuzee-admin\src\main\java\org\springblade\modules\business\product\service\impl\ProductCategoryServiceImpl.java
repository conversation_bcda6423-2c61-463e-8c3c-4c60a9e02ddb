/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.product.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.modules.business.product.mapper.ProductCategoryAttributeRelationMapper;
import org.springblade.modules.business.product.pojo.dto.ProductCategoryDTO;
import org.springblade.modules.business.product.pojo.dto.SaveProductCategoryDTO;
import org.springblade.modules.business.product.pojo.entity.ProductCategoryAttributeRelationEntity;
import org.springblade.modules.business.product.pojo.entity.ProductCategoryEntity;
import org.springblade.modules.business.product.pojo.vo.*;
import org.springblade.modules.business.product.excel.ProductCategoryExcel;
import org.springblade.modules.business.product.mapper.ProductCategoryMapper;
import org.springblade.modules.business.product.service.IProductCategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 商品分类表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
public class ProductCategoryServiceImpl extends BaseServiceImpl<ProductCategoryMapper, ProductCategoryEntity> implements IProductCategoryService {

	@Autowired
	private ProductCategoryMapper productCategoryMapper;

	@Autowired
	private ProductCategoryAttributeRelationMapper productCategoryAttributeRelationMapper;

	/**
	 * 两位数字组合的正则表达式
	 */
	private static final Pattern CATEGORY_CODE_PATTERN = Pattern.compile("^\\d{2}$");

	@Override
	public IPage<ProductCategoryVO> selectProductCategoryPage(IPage<ProductCategoryVO> page, ProductCategoryVO productCategory) {
		return page.setRecords(baseMapper.selectProductCategoryPage(page, productCategory));
	}

	@Override
	public List<ProductCategoryEntity> getAll() {
		return productCategoryMapper.getAllProductCategoryEntities();
	}

	@Override
	public List<ProductCategoryTreeVO> listTree(QueryWrapper<ProductCategoryEntity> queryWrapper) {
		List<ProductCategoryEntity> productCategoryEntities = baseMapper.selectList(queryWrapper);
		return treeInfo(productCategoryEntities);

	}

	@Override
	public List<OneListVO> getOneList() {

		QueryWrapper<ProductCategoryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ProductCategoryEntity::getParentId, 0)
			.select(ProductCategoryEntity::getId, ProductCategoryEntity::getParentId, ProductCategoryEntity::getName);

		List<ProductCategoryEntity> productCategoryEntities = baseMapper.selectList(queryWrapper);
		List<OneListVO> oneListVOS = new ArrayList<>();
		for (ProductCategoryEntity item : productCategoryEntities) {
			OneListVO vo = new OneListVO();
			vo.setId(item.getId());
			vo.setName(item.getName());
			oneListVOS.add(vo);
		}
		return oneListVOS;
	}

	private List<ProductCategoryTreeVO> treeInfo(List<ProductCategoryEntity> categoryList) {
		List<ProductCategoryTreeVO> result = new ArrayList<>();
		List<ProductCategoryEntity> categoryParentList = categoryList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
		for (ProductCategoryEntity category : categoryParentList) {
			ProductCategoryTreeVO skuForwardingChargesTreeVO = new ProductCategoryTreeVO();
			skuForwardingChargesTreeVO.setId(category.getId());
			skuForwardingChargesTreeVO.setName(category.getName());
			skuForwardingChargesTreeVO.setParentId(category.getParentId());
			List<ProductCategoryTreeVO> skuForwardingChargesTreeVOS = treeChildren(categoryList, category.getId());
			for (ProductCategoryTreeVO skuForwardingChargesTreeVO1 : skuForwardingChargesTreeVOS) {
				List<ProductCategoryTreeVO> chargesTreeVOS = treeChildren(null, skuForwardingChargesTreeVO1.getId());
				skuForwardingChargesTreeVO1.setChildren(chargesTreeVOS);
			}
			skuForwardingChargesTreeVO.setChildren(skuForwardingChargesTreeVOS);
			result.add(skuForwardingChargesTreeVO);
		}
		return result;
	}

	private List<ProductCategoryTreeVO> treeChildren(List<ProductCategoryEntity> categoryList, Long parentId) {
		List<ProductCategoryTreeVO> result = new ArrayList<>();
		if (Func.isNotEmpty(categoryList)) {
			result = categoryList.stream()
				.filter(i -> i.getParentId().equals(parentId))
				.map(category -> {
					ProductCategoryTreeVO skuForwardingChargesTreeVO = new ProductCategoryTreeVO();
					skuForwardingChargesTreeVO.setId(category.getId());
					skuForwardingChargesTreeVO.setName(category.getName());
					skuForwardingChargesTreeVO.setChildren(new ArrayList<>()); // 如果需要设置子节点
					return skuForwardingChargesTreeVO;
				})
				.collect(Collectors.toList());
		}
		return result;
	}

	@Override
	public List<ProductCategoryExcel> exportProductCategory(Wrapper<ProductCategoryEntity> queryWrapper) {
		List<ProductCategoryExcel> productCategoryList = baseMapper.exportProductCategory(queryWrapper);
		//productCategoryList.forEach(productCategory -> {
		//	productCategory.setTypeName(DictCache.getValue(DictEnum.YES_NO, ProductCategoryEntity.getType()));
		//});
		return productCategoryList;
	}

	@Override
	public boolean saveProductCategory(SaveProductCategoryDTO saveProductCategoryDTO) {
		// 校验分类编码格式
		if (saveProductCategoryDTO.getCategoryCode() == null ||
			!CATEGORY_CODE_PATTERN.matcher(saveProductCategoryDTO.getCategoryCode()).matches()) {
			throw new ServiceException("分类编码必须为两位数字组合，例如：01、21、99等");
		}

		ProductCategoryEntity productCategory = new ProductCategoryEntity();
		BeanUtils.copyProperties(saveProductCategoryDTO, productCategory);
		//没有父分类时为一级分类
		this.setCategoryLevel(productCategory);
		int count = productCategoryMapper.insert(productCategory);
		return count > 0;
	}

	/**
	 * 根据分类的parentId设置分类的level
	 */
	private void setCategoryLevel(ProductCategoryEntity productCategory) {
		//没有父分类时为一级分类
		if (ObjectUtil.isEmpty(productCategory.getParentId()) || productCategory.getParentId() == 0) {
			productCategory.setLevel(0);
		} else {
			//有父分类时选择根据父分类level设置
			Optional.ofNullable(productCategoryMapper.selectById(productCategory.getParentId()))
				.ifPresent(parentCategory -> productCategory.setLevel(parentCategory.getLevel() + 1));
		}
	}

	@Override
	public List<ProductCategoryWithChildrenItem> listWithChildren(String name) {
		// 按名称查询分类并获取所有子分类
		List<ProductCategoryWithChildrenItem> allCategories = this.getAllProductCategories();
		List<ProductCategoryWithChildrenItem> rootCategories = new ArrayList<>();
		Set<Long> processedIds = new HashSet<>();
		if (StringUtils.isBlank(name)) {    // 如果名称为空，遍历所有分类，查找根分类
			// 确保分类未被处理过，避免重复构建子分类
			for (ProductCategoryWithChildrenItem category : allCategories) {
				if (!processedIds.contains(category.getId())) {
					ProductCategoryWithChildrenItem rootCategory = this.convertToProductCategoryWithChildrenItem(category, allCategories, processedIds);
					// 只将非空的根分类添加到结果中
					if (rootCategory != null) {
						rootCategories.add(rootCategory);
					}
				}
			}
		} else {    // 如果名称不为空，尝试查找特定分类
			Optional<ProductCategoryWithChildrenItem> categoryOptional = allCategories.stream()
				.filter(category -> category.getName().contains(name))
				.findFirst();
			// 如果找到了分类，则构建其子分类结构
			categoryOptional.ifPresent(category -> {
				ProductCategoryWithChildrenItem rootCategory = this.convertToProductCategoryWithChildrenItem(category, allCategories, processedIds);
				// 只将非空的分类添加到结果中
				if (rootCategory != null) {
					rootCategories.add(rootCategory);
				}
			});
		}
		return rootCategories;
	}

	/**
	 * 通过分类ID查找分类及其所有子分类
	 *
	 * @param categoryId 分类ID
	 * @return 分类及其子分类的树形结构
	 */
	@Override
	public ProductCategoryWithChildrenItem findCategoryWithChildrenById(Long categoryId) {
		if (categoryId == null) {
			return null;
		}

		// 获取所有分类
		List<ProductCategoryWithChildrenItem> allCategories = this.getAllProductCategories();

		// 查找指定ID的分类
		Optional<ProductCategoryWithChildrenItem> targetCategory = allCategories.stream()
			.filter(category -> categoryId.equals(category.getId()))
			.findFirst();

		if (targetCategory.isEmpty()) {
			return null;
		}

		// 构建分类树
		Set<Long> processedIds = new HashSet<>();
		ProductCategoryWithChildrenItem result = targetCategory.get();

		// 设置子分类
		result.setChildren(this.getChildren(result.getId(), allCategories, processedIds));

		return result;
	}

	/**
	 * 通过分类ID查找此分类及其所有子分类的ID集合
	 *
	 * @param categoryId 分类ID
	 * @return 包含此分类ID及其所有子分类ID的集合
	 */
	@Override
	public Set<Long> findCategoryIdsById(Long categoryId) {
		if (categoryId == null) {
			return new HashSet<>();
		}

		// 获取所有分类
		List<ProductCategoryWithChildrenItem> allCategories = this.getAllProductCategories();
		if (CollectionUtils.isEmpty(allCategories)) {
			return new HashSet<>();
		}

		// 验证categoryId是否存在
		boolean categoryExists = allCategories.stream()
			.anyMatch(category -> categoryId.equals(category.getId()));
		if (!categoryExists) {
			return new HashSet<>();
		}

		// 创建结果集合，并添加当前分类ID
		Set<Long> resultIds = new HashSet<>();
		resultIds.add(categoryId);

		// 递归查找所有子分类ID
		this.findChildrenIds(categoryId, allCategories, resultIds);

		return resultIds;
	}

	/**
	 * 递归查找所有子分类ID
	 *
	 * @param parentId      父分类ID
	 * @param allCategories 所有分类列表
	 * @param resultIds     结果ID集合
	 */
	private void findChildrenIds(Long parentId, List<ProductCategoryWithChildrenItem> allCategories, Set<Long> resultIds) {
		if (parentId == null || CollectionUtils.isEmpty(allCategories) || resultIds == null) {
			return;
		}

		allCategories.stream()
			.filter(category -> parentId.equals(category.getParentId()))
			.forEach(category -> {
				resultIds.add(category.getId());
				this.findChildrenIds(category.getId(), allCategories, resultIds);
			});
	}

	// 递归地将 ProductCategoryEntity 转换为 ProductCategoryWithChildrenItem 并填充子分类
	private ProductCategoryWithChildrenItem convertToProductCategoryWithChildrenItem(ProductCategoryEntity category, List<ProductCategoryWithChildrenItem> allCategories, Set<Long> processedIds) {
		if (processedIds.contains(category.getId())) {
			return null;
		}
		ProductCategoryWithChildrenItem item = new ProductCategoryWithChildrenItem();
		BeanUtils.copyProperties(category, item);
		item.setChildren(this.getChildren(item.getId(), allCategories, processedIds));
		return item;
	}

	// 递归地获取子分类
	private List<ProductCategoryEntity> getChildren(Long parentId, List<ProductCategoryWithChildrenItem> allCategories, Set<Long> processedIds) {
		return allCategories.stream()
			.filter(category -> parentId.equals(category.getParentId()))
			.peek(category -> {
				processedIds.add(category.getId());
				category.setChildren(getChildren(category.getId(), allCategories, processedIds));
			})
			.collect(Collectors.toList());
	}

	// 获取所有属性分类
	private List<ProductCategoryWithChildrenItem> getAllProductCategories() {
		return productCategoryMapper.selectAll();
	}

}
