/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.order.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.pojo.vo.PrintTransferOrderProductVO;
import org.springblade.common.pojo.vo.PrintTransferOrderTransportVO;
import org.springblade.common.pojo.vo.PrintTransferOrderWarehouseVO;
import org.springblade.modules.business.order.excel.ReplenishmentOrderExcel;
import org.springblade.modules.business.order.pojo.dto.ReplenishmentListDTO;
import org.springblade.modules.business.order.pojo.dto.ReplenishmentListSupplierPageDTO;
import org.springblade.modules.business.order.pojo.dto.ReplenishmentListSupplierPageQueryDTO;
import org.springblade.modules.business.order.pojo.dto.TransferOrderPrintDTO;
import org.springblade.modules.business.order.pojo.entity.ReplenishmentOrderEntity;
import org.springblade.modules.business.order.pojo.vo.*;

import java.util.List;

/**
 * 供应商备货表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface ReplenishmentOrderMapper extends BaseMapper<ReplenishmentOrderEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param replenishmentOrder 查询参数
	 * @return List<ReplenishmentOrderVO>
	 */
	List<ReplenishmentOrderVO> selectReplenishmentOrderPage(IPage page, ReplenishmentOrderVO replenishmentOrder);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<ReplenishmentOrderExcel>
	 */
	List<ReplenishmentOrderExcel> exportReplenishmentOrder(@Param("ew") Wrapper<ReplenishmentOrderEntity> queryWrapper);

    IPage<ReplenishmentListVO> replenishmentList(@Param("dto") ReplenishmentListDTO dto, IPage<ReplenishmentListVO> page);

	List<ReplenishmentDetailVO> replenishmentDetail(@Param("ids") List<Long> ids);

	/**
	 * 根据采购单id和skuId获取采购单详情
	 *
	 * @param purchaseOrderId	采购单id
	 * @param skuId				商品sku
	 */
	List<ReplenishmentOrderAllocationVO> listByPurchaseOrderAndSku(@Param("purchaseOrderId") Long purchaseOrderId,@Param("skuId") Long skuId);

	/**
	 * 根据采购单id获取所有供应商备货明细
	 */
    List<SupplierReplenishmentOrderVO> listSupplierReplenishment(@Param("orderId") Long purchaseOrderId);

	/**
	 * 获取供应商备货单
	 */
	List<ReplenishmentListSupplierPageDTO> listBySupplier(IPage page, @Param("dto") ReplenishmentListSupplierPageQueryDTO dto, @Param("supplierId") Long supplierId);

	/**
	 * 根据时间获取供应商备货单
	 *
	 * @param warehouseId       仓库id
	 * @param orderWarehouseIds	截单仓库ids
	 * @param startTime         备货单开始日期
	 * @param endTime           备货单结束日期
	 */
    List<ReplenishmentOrderItemByTimeVO> listByTime(@Param("warehouseId") Long warehouseId, @Param("orderWarehouseIds") List<Long> orderWarehouseIds, @Param("startTime") long startTime, @Param("endTime") long endTime);

    List<PrintTransferOrderTransportVO> getPrintTransferOrderTransport(@Param("dto") TransferOrderPrintDTO dto);

	List<PrintTransferOrderProductVO> getPrintTransferOrderProduct(@Param("dto") TransferOrderPrintDTO dto);

	List<PrintTransferOrderWarehouseVO> getPrintTransferOrderWarehouse(@Param("dto") TransferOrderPrintDTO dto);
}
