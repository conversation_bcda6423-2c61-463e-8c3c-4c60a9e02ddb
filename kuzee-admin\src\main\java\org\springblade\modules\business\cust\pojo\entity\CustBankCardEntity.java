/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户银行卡绑定表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_cust_bank_card")
@Schema(description = "CustBankCardEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustBankCardEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long custId;
	/**
	 * 银行名称
	 */
	@Schema(description = "银行名称")
	private String bankName;
	/**
	 * 银行卡号
	 */
	@Schema(description = "银行卡号")
	private String cardNumber;
	/**
	 * 持卡人姓名
	 */
	@Schema(description = "持卡人姓名")
	private String holderName;
	/**
	 * 是否默认卡（0-否，1-是）
	 */
	@Schema(description = "是否默认卡（0-否，1-是）")
	private Integer isDefault;

}
