/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.cust.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.business.cust.pojo.dto.CustReconciliationPageDTO;
import org.springblade.modules.business.cust.pojo.vo.CustReconciliationPageVO;
import org.springblade.modules.business.cust.pojo.vo.CustReconciliationDetailVO;
import org.springblade.modules.business.cust.pojo.vo.CustReconciliationDetailItemVO;

import java.util.List;

/**
 * 客户对账单Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Mapper
public interface CustReconciliationMapper {

	/**
	 * 分页查询客户对账单
	 *
	 * @param page 分页参数
	 * @param dto 查询条件
	 * @return 对账单列表
	 */
	List<CustReconciliationPageVO> selectPage(IPage<CustReconciliationPageVO> page, @Param("dto") CustReconciliationPageDTO dto);

	CustReconciliationDetailVO selectDetail(@Param("custId") Long custId);

	List<CustReconciliationDetailItemVO> selectDetailItems(@Param("custId") Long custId);
}
