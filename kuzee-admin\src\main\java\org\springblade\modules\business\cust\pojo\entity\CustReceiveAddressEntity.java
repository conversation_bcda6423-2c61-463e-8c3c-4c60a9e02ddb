/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 客户收货地址表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_cust_receive_address")
@Schema(description = "CustReceiveAddressEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustReceiveAddressEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private Long custId;
	/**
	 * 收货人名称
	 */
	@Schema(description = "收货人名称")
	private String name;
	/**
	 * 收货人手机号
	 */
	@Schema(description = "收货人手机号")
	private String phone;
	/**
	 * 是否默认地址
	 */
	@Schema(description = "是否默认地址")
	private Integer defaultStatus;
	/**
	 * 邮政编码
	 */
	@Schema(description = "邮政编码")
	private String postCode;
	/**
	 * 省份/直辖市
	 */
	@Schema(description = "省份/直辖市")
	private String province;
	/**
	 * 城市
	 */
	@Schema(description = "城市")
	private String city;
	/**
	 * 区
	 */
	@Schema(description = "区")
	private String region;
	/**
	 * 区域名称
	 */
	@Schema(description = "区域名称")
	private String regionName;
	/**
	 * 详细地址(街道)
	 */
	@Schema(description = "详细地址(街道)")
	private String detailAddress;

}
