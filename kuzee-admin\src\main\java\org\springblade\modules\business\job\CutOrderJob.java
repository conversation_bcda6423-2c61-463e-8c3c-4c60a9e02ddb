package org.springblade.modules.business.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.Get;
import org.aspectj.bridge.ICommand;
import org.springblade.common.constant.BusinessConstant;
import org.springblade.common.enums.WarehouseTypeEnum;
import org.springblade.common.utills.CommonUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.order.pojo.entity.*;
import org.springblade.modules.business.order.pojo.vo.OrderOperateHistoryVO;
import org.springblade.modules.business.order.pojo.vo.SaleOrderItemJobVO;
import org.springblade.modules.business.order.service.*;
import org.springblade.modules.business.product.pojo.entity.*;
import org.springblade.modules.business.product.service.*;
import org.springblade.modules.business.warehouse.pojo.entity.*;
import org.springblade.modules.business.warehouse.pojo.vo.InventorySupportTransVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseJobVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehousePurchaseIdVO;
import org.springblade.modules.business.warehouse.service.*;
import org.springblade.modules.system.service.IParamService;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
@Slf4j
public class CutOrderJob {
	private final ISaleOrderService saleOrderService;
	private final ISaleOrderItemService saleOrderItemService;
	private final IWarehouseService warehouseService;
	private final IWarehousePurchaseService warehousePurchaseService;
	private final IPurchaseOrderService purchaseOrderService;
	private final IPurchaseOrderItemService purchaseOrderItemService;
	private final IInventoryService inventoryService;
	private final ISkuOemService skuOemService;
	private final ISkuPpService skuPpService;
	private final ITransportOrderService iTransportOrderService;
	private final ITransportOrderItemService iTransportOrderItemService;
	private final ISkuWarehouseRelationService iSkuWarehouseRelationService;
	private final IPurchaseOrderSaleItemService purchaseOrderSaleItemService;
	private final ITransportUnitService transportUnitService;
	private final RedisLockClient redisLockClient;
	private final Environment environment;
	private final IParamService paramService;

	/**
	 * 仓库关联采购员了，仓库关联了商品SKU，商品关联了订单，订单关联了商品，商品数量大于库存数量，则需要截单
	 * 仓库关联了SKU 的是实现
	 */
	@Scheduled(cron = "0 * * * * ? ")
//	@XxlJob("cutOrder")
	@Transactional
	public void cutOrderNew() {
		if (!environment.getProperty("blade.env").contains("prod")) {
			log.info("非正式环境不执行");
			return;
		}
		log.info("[order_cutOrderNew]定时任务开始-----------------");
		log.info("[截单]开始正常截单");
		Boolean lock = redisLockClient.lockFair("lock:cutOrderNew", 1, 300, () -> {

			String kuzee = paramService.getValue("cutRun");
			//订单ID集合
			List<Long> orderIds = new ArrayList<>();
			//获取需要截单的仓库/判断仓库是否需要截单/是否已经截单成功
			List<WarehouseJobVO> warehouseList = warehouseService.getWarehouseCutList();
			if (warehouseList.size() == 0) return Boolean.TRUE;
			List<Long> warehouseIdList = warehouseList.stream().map(WarehouseJobVO::getId).distinct().collect(Collectors.toList());

			//根据商品和仓库信息详情获取采购员
			//仓库获取采购员
			List<WarehousePurchaseIdVO> purchaseEntities = warehousePurchaseService.getWarehousePurchase(warehouseIdList);
			//if (purchaseEntities.size() == 0) return Boolean.TRUE;
			List<Long> purchaseIdList = purchaseEntities.stream().map(WarehousePurchaseIdVO::getPurchaserId).distinct().collect(Collectors.toList());

			//通过仓库获取需要接单的订单/判断订单是否需要截单/通过截单信息获取所有订单商品
			//判断截单信息 支付完成、未结单、备货中 三种状态下的截单
			List<SaleOrderItemJobVO> saleOrderList = saleOrderItemService.getCutOrderList(warehouseIdList);
			if (saleOrderList.size() == 0) {
				//没有订单，仓库已结单
				if (Objects.equals(kuzee, "1"))
					warehouseService.updateCutTimeJob(warehouseIdList);
				return Boolean.TRUE;
			}
			orderIds = saleOrderList.stream().map(SaleOrderItemJobVO::getOrderId).distinct().collect(Collectors.toList());
			//  获取框架的价格信息
			List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
			if (saleOrderList.size() > 0)
				transportUnitEntities = transportUnitService.getIds(saleOrderList.stream().map(SaleOrderItemJobVO::getProductSkuId).distinct().collect(Collectors.toList()));
			for (WarehouseJobVO warehouse : warehouseList) {
				log.info("[order_cutOrderNew]开始处理仓库：{}", warehouse.getId());
				GeneratePurchaseOrder(saleOrderList, warehouse, purchaseIdList, transportUnitEntities, BusinessConstant.ORDER_TYPE_SALE);
			}
			//更新截单状态
			if (orderIds.size() > 0)
				log.info("[order_cutOrderNew]更新截单状态");
			saleOrderService.UpdateCutOrder(orderIds, GenerateNumberUtil.PurchaseCodeF());
			//修改时间获取时间
			if (warehouseIdList.size() > 0 && Objects.equals(kuzee, "1"))
				warehouseService.updateCutTimeJob(warehouseIdList);
			return Boolean.TRUE;
		});

		if (Func.isNull(lock)) {
			log.warn("[order_cutOrderNew]已存在正在处理中的定时任务，本次处理结束");
		}
	}

	/**
	 * 生成采购单
	 *
	 * @param saleOrderList         订单信息
	 * @param warehouse             仓库信息
	 * @param purchaseIdList        采购员ID
	 * @param transportUnitEntities 运输单位信息
	 * @param type                  类型 0 销售 1 自采
	 */
	//生成采购单
	public void GeneratePurchaseOrder(List<SaleOrderItemJobVO> saleOrderList, WarehouseJobVO warehouse,
									  List<Long> purchaseIdList, List<TransportUnitEntity> transportUnitEntities, int type) {
		List<SaleOrderItemJobVO> orderList = saleOrderList.stream().filter(i -> i.getWarehouseId().equals(warehouse.getId())).toList();
		if (orderList.size() == 0) return;
		//获取或SkuId
		List<Long> skuIdList = orderList.stream().map(SaleOrderItemJobVO::getProductSkuId).distinct().collect(Collectors.toList());
		//专采购单
		List<Long> oemPpSku = new ArrayList<>();//专采专供
		//专供购单-运货到档口
		List<SkuOemEntity> oemSku = skuOemService.getOemSku(skuIdList, warehouse.getId());
		//获取专采SKUId-运货到总仓
		List<SkuPpEntity> ppSku = skuPpService.getPpSku(skuIdList);
		List<SkuPpEntity> ppSkuAll = new ArrayList<>(ppSku);
		boolean isTran = true;
		//获取总仓采购单-运货到总仓
		//总仓采购单 -运货到总仓
		List<Long> oemSkuIds = oemSku.stream().map(SkuOemEntity::getSkuId).toList();
		List<Long> ppSkuIds = ppSku.stream().map(SkuPpEntity::getSkuId).toList(); //专采
		skuIdList.removeAll(oemSkuIds);
		skuIdList.removeAll(ppSkuIds);
		//总仓采购单
		List<Long> zCSkuList = new ArrayList<>(skuIdList);
		//是专供又是专采的商品 -运货到档口
		for (SkuPpEntity pp : ppSku) {
			List<SkuOemEntity> collect = oemSku.stream().filter(i -> i.getSkuId().equals(pp.getSkuId())).toList();
			if (collect.size() > 0) {    //有专供
				oemPpSku.add(collect.get(0).getSkuId());
				oemSku.remove(collect.get(0));
			}
		}
		for (Long id : oemPpSku) {
			List<SkuPpEntity> ppEntities = ppSku.stream().filter(i -> i.getSkuId().equals(id)).toList();
			ppSku.remove(ppEntities.get(0));
		}
		//获取今日价格 专供价格、专采价格、总仓价格
		//专供价格
		for (SkuOemEntity oem : oemSku) {
			List<SaleOrderItemJobVO> collect = orderList.stream().filter(i -> i.getProductSkuId().equals(oem.getSkuId())).toList();
			if (collect.size() > 0) {
				if (oem.getPriceType().equals(1)) {
					collect.get(0).setPrice(oem.getPrice());
				} else {
					collect.get(0).setWholePrice(oem.getPrice());
				}
			}
			zCSkuList.remove(oem.getSkuId());
		}
		//专采价格
		for (SkuPpEntity oem : ppSku) {
			List<SaleOrderItemJobVO> collect = orderList.stream().filter(i -> i.getProductSkuId().equals(oem.getSkuId())).toList();
			if (collect.size() > 0) {
				if (oem.getPriceType().equals(1)) {
					collect.get(0).setPrice(oem.getPrice());
				} else {
					collect.get(0).setWholePrice(oem.getPrice());
				}
			}
		}
		for (Long oem : oemPpSku) {
			List<SaleOrderItemJobVO> collect = orderList.stream().filter(i -> i.getProductSkuId().equals(oem)).toList();
			if (collect.size() > 0) {
				List<SkuPpEntity> collect1 = ppSkuAll.stream().filter(i -> i.getSkuId().equals(oem)).toList();
				SkuPpEntity spModel = collect1.get(0);
				if (spModel.getPriceType().equals(1)) {
					collect.get(0).setPrice(spModel.getPrice());
				} else {
					collect.get(0).setWholePrice(spModel.getPrice());
				}
			}
		}
		if (skuIdList.size() > 0) {
			//总仓价格
			Long warehouseIdZC = warehouse.getId();
			if (warehouse.getWarehouseType().equals(WarehouseTypeEnum.DIVIDE.getCode())) {
				warehouseIdZC = warehouse.getParentId();
			}
			List<SkuWarehouseRelationEntity> WarehouseRelationEntities = iSkuWarehouseRelationService.getByWarehouseId(warehouseIdZC, skuIdList);
			for (SkuWarehouseRelationEntity oem : WarehouseRelationEntities) {
				List<SaleOrderItemJobVO> collect = orderList.stream().filter(i -> i.getProductSkuId().equals(oem.getSkuId())).toList();
				if (collect.size() > 0) {
					if (oem.getPriceType().equals(1)) {
						collect.get(0).setPrice(oem.getPrice());
					} else {
						collect.get(0).setWholePrice(oem.getPrice());
					}
				}
			}
		}

		Long purchaserWarehouseId = 0l;
		Long transportWarehouseId = 0l;
		//订单skuId与oemSkuId匹配的数据
		//生成专供购单-运货到档口
		if (oemSku.size() > 0) {
			log.info("生成专供购单-运货到档口");
			purchaserWarehouseId = warehouse.getId();
			transportWarehouseId = warehouse.getId();
			isTran = false;
			List<Long> skuIds = oemSku.stream().map(i -> i.getSkuId()).distinct().toList();
			Map<Long, List<Long>> longListMap = GetPurchaseOrderIds(skuIds);
			Long finalTransportWarehouseId = transportWarehouseId;
			Long finalPurchaserWarehouseId = purchaserWarehouseId;
			boolean finalIsTran = isTran;
			longListMap.forEach((k, v) -> {
				List<SaleOrderItemJobVO> finalCcOrderList = new ArrayList<>();
				List<SkuOemEntity> skuOemEntities = oemSku.stream().filter(i -> v.contains(i.getSkuId())).toList();
				skuOemEntities.forEach(i -> {
					List<SaleOrderItemJobVO> collect = orderList.stream().filter(j -> i.getSkuId().equals(j.getProductSkuId())).toList();
					if (collect.size() > 0) {
						finalCcOrderList.addAll(collect);
					}
				});
				GeneratePurchaseOrder(warehouse.getId(), k, finalCcOrderList, warehouse.getId(), warehouse.getParentId(),
					finalPurchaserWarehouseId, finalTransportWarehouseId, transportUnitEntities, type, finalIsTran, false);

			});
		}
		//生成是专供又是专采的商品-运货到档口
		if (oemPpSku.size() > 0) {
			log.info("生成是专供又是专采的商品-运货到档口");
			transportWarehouseId = warehouse.getId();
			List<SkuPpEntity> collect = ppSkuAll.stream().filter(i -> oemPpSku.contains(i.getSkuId())).toList();
			List<Long> purchaseIds = collect.stream().map(SkuPpEntity::getPurchaserId).distinct().toList();
			for (Long id : purchaseIds) {
				List<SkuPpEntity> pp = collect.stream().filter(i -> i.getPurchaserId().equals(id)).toList();
				purchaserWarehouseId = pp.get(0).getWarehouseId();
				List<SaleOrderItemJobVO> finalCcOrderList = new ArrayList<>();
				pp.forEach(i -> {
					List<SaleOrderItemJobVO> collect1 = orderList.stream().filter(j -> i.getSkuId().equals(j.getProductSkuId())).toList();
					if (collect1.size() > 0) {
						finalCcOrderList.addAll(collect1);
					}
				});
				isTran = false;
				GeneratePurchaseOrder(warehouse.getId(), id, finalCcOrderList, warehouse.getId(), warehouse.getParentId(),
					purchaserWarehouseId, transportWarehouseId, transportUnitEntities, type, isTran, true);
			}
		}
		//生成总仓采购单-运货到总仓
		if (zCSkuList.size() > 0) {
			log.info("生成总仓采购单-运货到总仓");
			QueryWrapper queryWrapper = new QueryWrapper();
			if (warehouse.getWarehouseType().equals(WarehouseTypeEnum.DIVIDE.getCode())) {
				queryWrapper.eq("warehouse_id", warehouse.getParentId());
			} else {
				queryWrapper.eq("warehouse_id", warehouse.getId());
			}
			queryWrapper.eq("role_type", 1);//采购员
			List<WarehousePurchaseEntity> warehouseEntities = warehousePurchaseService.list(queryWrapper);
			Long purchaseId = warehouseEntities.get(0).getPurchaserId();
			if (warehouse.getParentId().equals(0L)) {
				purchaserWarehouseId = warehouse.getId();
				transportWarehouseId = warehouse.getId();
			} else {
				purchaserWarehouseId = warehouse.getParentId();
				transportWarehouseId = warehouse.getParentId();
			}
			List<SaleOrderItemJobVO> finalCcOrderList = new ArrayList<>();
			zCSkuList.forEach(i -> {
				List<SaleOrderItemJobVO> collect1 = orderList.stream().filter(j -> i.equals(j.getProductSkuId())).toList();
				if (collect1.size() > 0) {
					finalCcOrderList.addAll(collect1);
				}
			});
			if (!warehouse.getParentId().equals(0L))
				isTran = true;
			else
				isTran = false;
			GeneratePurchaseOrder(warehouse.getId(), purchaseId, finalCcOrderList, warehouse.getId(), warehouse.getParentId(),
				purchaserWarehouseId, transportWarehouseId, transportUnitEntities, type, isTran, false);
		}
		//生成专采购单-运货到总仓
		if (ppSku.size() > 0) {
			log.info("生成专采购单-运货到总仓");
			log.info("[截单]生成专采购单-运货到总仓 订单信息：{}",orderList);
			transportWarehouseId = warehouse.getParentId();
			if (type==1){
				transportWarehouseId = warehouse.getId();
			}


			List<Long> purchaseIds = ppSku.stream().map(SkuPpEntity::getPurchaserId).distinct().toList();
			for (Long id : purchaseIds) {
				List<SkuPpEntity> pp = ppSku.stream().filter(i -> i.getPurchaserId().equals(id)).toList();
				purchaserWarehouseId = pp.get(0).getWarehouseId();
				List<SaleOrderItemJobVO> finalCcOrderList = new ArrayList<>();
				pp.forEach(i -> {
					List<SaleOrderItemJobVO> collect1 = orderList.stream().filter(j -> i.getSkuId().equals(j.getProductSkuId())).toList();
					if (collect1.size() > 0) {
						finalCcOrderList.addAll(collect1);
					}
				});
				if (!warehouse.getParentId().equals(0L))
					isTran = true;
				else
					isTran = false;
				log.info("[截单]生成专采购单-运货到总仓 订单信息：{}",finalCcOrderList.size());
				GeneratePurchaseOrder(warehouse.getId(), id, finalCcOrderList, warehouse.getId(), warehouse.getParentId(),
					purchaserWarehouseId, transportWarehouseId, transportUnitEntities, type, isTran, false);
			}
		}
	}


	/**
	 * 获取仓库对应的采购员
	 *
	 * @param warehouseId          采购仓库ID
	 * @param purchaserId          采购员ID
	 * @param orderList            订单
	 * @param orderWarehouseId     订单ID
	 * @param orderWarehousePId    订单总仓ID
	 * @param purchaserWarehouseId 采购员仓库ID
	 * @param transportWarehouseId 运输到仓库ID
	 * @param isTran               是否生成转运单
	 */
	private void GeneratePurchaseOrder(Long warehouseId, Long purchaserId, List<SaleOrderItemJobVO> orderList,
									   Long orderWarehouseId, Long orderWarehousePId, Long purchaserWarehouseId,
									   Long transportWarehouseId, List<TransportUnitEntity> transportUnitEntities,
									   int type, boolean isTran, boolean isZCZG) {
		log.info("转运仓库：{}", transportWarehouseId);
		log.info("下单仓库：{}", orderWarehouseId);
		log.info("采购员仓库：{}", purchaserWarehouseId);
		log.info("采购仓库：{}", warehouseId);
		List<PurchaseOrderItemEntity> purchaseOrderItemList = new ArrayList<>();//采购单商品详情列表
		List<PurchaseOrderItemEntity> purchaseOrderItemUpdateList = new ArrayList<>();
		List<TransportOrderItemEntity> transportOrderItemEntities = new ArrayList<>();
		List<InventoryEntity> inventoryLists = new ArrayList<>();
		List<SaleOrderItemJobVO> purOrderList = orderList.stream().filter(i -> i.getWarehouseId().equals(orderWarehouseId)).toList();
		if (purOrderList.size() == 0) {
			return;
		}
		String purchaseNo = GenerateNumberUtil.PurchaseCodeF();
		String purchaseBathNo = GenerateNumberUtil.PurchaseCodeFNo();
		if (type == 1) {
			purchaseNo = GenerateNumberUtil.PurchaseCodeT();
			purchaseBathNo = GenerateNumberUtil.PurchaseCodeTNo();
		}
		//获取仓库库存量信息(当前仓库和上级仓库)
		List<Long> orderWarehouseIds = new ArrayList<>();
		orderWarehouseIds.add(orderWarehouseId);
		orderWarehouseIds.add(orderWarehousePId);
		List<InventoryEntity> inventoryEntities = new ArrayList<>();
		if (type == 0) {
			inventoryEntities = inventoryService.getInventory(orderWarehouseIds); //仓库库存量信息
		}
		//生成采购单
		//判断是不是存在这样的采购单
		PurchaseOrderEntity purchaseOrder = GetPurchaseOrder(purchaserId, purchaseNo, type, transportWarehouseId, orderWarehouseId);
		List<PurchaseOrderItemEntity> purchaseOrderItemListOld = new ArrayList<>();
		if (purchaseOrder != null)
			purchaseOrderItemListOld = purchaseOrderItemService.list(new QueryWrapper<PurchaseOrderItemEntity>().eq("purchase_id", purchaseOrder.getId()));
		if (Func.isEmpty(purchaseOrder)) {
			purchaseOrder = purchaseOrderInit(purchaseNo, purchaseBathNo, purchaserId, purchaserWarehouseId, transportWarehouseId, orderWarehouseId, type);
		} else {
			purchaseOrder.setWarehouseId(transportWarehouseId);
			purchaseOrder.setOrderWarehouseId(orderWarehouseId);
//			if (!purchaseOrder.getOrderWarehouseId().contains(orderWarehouseId.toString())) {
//				purchaseOrder.setOrderWarehouseId(purchaseOrder.getOrderWarehouseId() + "," + orderWarehouseId.toString());
//			}
		}
		log.info("[截单]生成采购单 订单信息：{}",purOrderList.size());
		List<Long> skuIdList = purOrderList.stream().map(SaleOrderItemJobVO::getProductSkuId).distinct().toList();
		//  需要生成ID
		if (inventoryEntities.size() == 0) {
			log.info("[截单]商品没有库存情况开始处理");
			//新增采购单
			if (Func.isEmpty(purchaseOrder.getId())) {
				log.info("[截单]不存在采购单开始处理");
				purchaseOrderService.save(purchaseOrder);
				for (Long skuId : skuIdList) {
					List<SaleOrderItemJobVO> pOrderSKUs = purOrderList.stream().filter(i -> i.getProductSkuId().equals(skuId)).toList();
					log.info("[截单]生成采购单通过sku筛选 订单信息：{}",pOrderSKUs.size());
					PurchaseOrderItemEntity purchaseOrderItem = purchaseOrderItemInit(purchaseOrder.getId(), skuId, pOrderSKUs);
					purchaseOrderItemList.add(purchaseOrderItem);
					if (isTran) {
						TransportOrderItemEntity transportOrderItemEntity = transportOrderItemInit(skuId, pOrderSKUs);
						transportOrderItemEntities.add(transportOrderItemEntity);
					}
				}
				log.info("[截单]不存在采购单：{}", purchaseOrderItemList.size());
				purchaseOrderItemList = getMergeIdenticalSKUPurItem(purchaseOrderItemList);
				log.info("[截单]不存在采购单：{}", purchaseOrderItemList.size());
				purchaseOrderItemService.saveBatch(purchaseOrderItemList);
				int sum = purchaseOrderItemList.stream().mapToInt(PurchaseOrderItemEntity::getQuantity).sum();
				int weightSum = purchaseOrderItemList.stream().mapToInt(PurchaseOrderItemEntity::getWeight).sum();
				int amountSum = purchaseOrderItemList.stream().mapToInt(i -> i.getQuantity() * i.getOrderPrice()).sum();
				purchaseOrder.setTotalCount(sum);
				purchaseOrder.setTotalWeight(weightSum);
				purchaseOrder.setTotalAmount(amountSum);
				purchaseOrder.setAllocationStatus(1);
				purchaseOrderService.updateById(purchaseOrder);
				if (isTran) {
					GenerateTransportOrder(transportOrderItemEntities, purchaseOrder, orderWarehouseId, orderWarehousePId, null);
				}
				//更新采购单和订单关系
				AddSaleOrderItem(purchaseOrderItemList, orderList);
			} else {
				log.info("[截单]存在采购单开始处理");
				//修改采购单
				for (Long skuId : skuIdList) {
					List<SaleOrderItemJobVO> pOrderSKUs = purOrderList.stream().filter(i -> i.getProductSkuId().equals(skuId)).toList();
					//是否存在SKUID
					List<PurchaseOrderItemEntity> purchaseOrderUpList = new ArrayList<>();
					if (purchaseOrderItemListOld.size() > 0) {
						purchaseOrderUpList = purchaseOrderItemListOld.stream().filter(i -> i.getProductSkuId().equals(skuId)).toList();
						log.info("[截单]存在购买商品id:{}", purchaseOrderUpList.size());
					}
					if (purchaseOrderUpList.size() == 0) {
						PurchaseOrderItemEntity purchaseOrderItem = purchaseOrderItemInit(purchaseOrder.getId(), skuId, pOrderSKUs);
						purchaseOrderItemList.add(purchaseOrderItem);
					} else {
						log.info("[截单]跟新采购单商品数据信息:{}",purchaseOrderUpList.size());
						PurchaseOrderItemEntity purchaseOrderItem = purchaseOrderItemUpdate(purchaseOrderUpList.get(0), pOrderSKUs);
						purchaseOrderItemUpdateList.add(purchaseOrderItem);
					}
					if (isTran) {
						TransportOrderItemEntity transportOrderItemEntity = transportOrderItemInit(skuId, pOrderSKUs);
						transportOrderItemEntities.add(transportOrderItemEntity);
					}
				}
				int sum = 0;
				int amountSum = 0;
				int weightSum = 0;
				if (purchaseOrderItemList.size() > 0) {
					sum = purchaseOrderItemList.stream().mapToInt(PurchaseOrderItemEntity::getQuantity).sum();
					weightSum = purchaseOrderItemUpdateList.stream().mapToInt(PurchaseOrderItemEntity::getWeight).sum();
					amountSum = purchaseOrderItemList.stream().mapToInt(i -> i.getQuantity() * i.getOrderPrice()).sum();
					log.info("[截单]存在采购单：{}", purchaseOrderItemList.size());
					purchaseOrderItemList = getMergeIdenticalSKUPurItem(purchaseOrderItemList);
					log.info("[截单]存在采购单：{}", purchaseOrderItemList.size());
					purchaseOrderItemService.saveBatch(purchaseOrderItemList);
					//更新采购单和订单关系
					AddSaleOrderItem(purchaseOrderItemList, orderList);
				}
				//跟新的处理
				if (purchaseOrderItemUpdateList.size() > 0) {
					sum += purchaseOrderItemUpdateList.stream().mapToInt(PurchaseOrderItemEntity::getQuantity).sum();
					weightSum += purchaseOrderItemUpdateList.stream().mapToInt(PurchaseOrderItemEntity::getWeight).sum();
					amountSum += purchaseOrderItemUpdateList.stream().mapToInt(i -> i.getQuantity() * i.getOrderPrice()).sum();
					purchaseOrderItemService.updateBatchById(purchaseOrderItemUpdateList);
					//更新采购单和订单关系
					AddSaleOrderItem(purchaseOrderItemUpdateList, orderList);
				}
				purchaseOrder.setTotalCount(purchaseOrder.getTotalCount() + sum);
				purchaseOrder.setTotalWeight(purchaseOrder.getTotalWeight() + weightSum);
				purchaseOrder.setTotalAmount(purchaseOrder.getTotalAmount() + amountSum);
				purchaseOrder.setAllocationStatus(1);
				purchaseOrderService.updateById(purchaseOrder);
				if (isTran) {
					GenerateTransportOrder(transportOrderItemEntities, purchaseOrder, orderWarehouseId, orderWarehousePId, null);
				}
			}
		} else {
			log.info("[截单]商品有库存情况开始处理");
			//获取仓库和总仓的商品数据量
			//判断仓库里面是不是有库存
			for (Long skuId : skuIdList) {
				List<SaleOrderItemJobVO> pOrderSKUs = purOrderList.stream().filter(i -> i.getProductSkuId().equals(skuId)).toList();
				if (pOrderSKUs.size() == 0) {
					continue;
				}
				int purchaseCount = pOrderSKUs.stream().mapToInt(SaleOrderItemJobVO::getProductQuantity).sum();
				//判断本地仓库和总仓库是否有库存
				//先判断本地仓库
				SaleOrderItemJobVO orderSku = pOrderSKUs.get(0);
				int price = orderSku.getProductUnitPrice();
				int quantity = purchaseCount;
				PurchaseOrderItemEntity purchaseOrderItem = new PurchaseOrderItemEntity();
				purchaseOrderItem.setQuantity(0);
				//减去本地仓和总仓的库存
				TransportOrderItemEntity transportOrderItemEntity = new TransportOrderItemEntity();
				transportOrderItemEntity.setQuantity(0);
				List<InventoryEntity> inventoryList = new ArrayList<>();
				Integer localCount = 0;
				localCount = WarehouseProcessing(inventoryEntities, skuId, warehouseId, orderWarehouseId, orderWarehousePId, quantity,
					price, purchaseOrderItem, transportOrderItemEntity, inventoryList, pOrderSKUs.get(0).getPackageGrossConversionRate()
					, localCount);
				log.info("转运单信息：{}", transportOrderItemEntity);
				//生成转运单
				if (isTran) {
					Integer su = pOrderSKUs.stream().mapToInt(SaleOrderItemJobVO::getProductQuantity).sum();
					if (!localCount.equals(su)) {
						transportOrderItemEntity.setQuantity(su - localCount);
						transportOrderItemEntity.setWeight(GetBigDecimalWeight(su - localCount, pOrderSKUs.get(0).getPackageGrossConversionRate()));
						transportOrderItemEntity.setStatus(0);
						transportOrderItemEntity.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
						transportOrderItemEntity.setProductSkuId(skuId);
						transportOrderItemEntity.setProductName(pOrderSKUs.get(0).getProductName());
						transportOrderItemEntity.setSpData(pOrderSKUs.get(0).getSpData());
						transportOrderItemEntity.setSkuCode(pOrderSKUs.get(0).getSkuCode());
						transportOrderItemEntity.setProductId(pOrderSKUs.get(0).getProductId());
						transportOrderItemEntity.setSupportTrans(pOrderSKUs.get(0).getSupportTrans());
						transportOrderItemEntity.setSupportTransPrice(pOrderSKUs.get(0).getSupportTransPrice());
						transportOrderItemEntity.setSupportTransUnitId(pOrderSKUs.get(0).getSupportTransUnitId());
						if (Func.isNotEmpty(transportOrderItemEntity.getSupportTransUnitId()))
							transportOrderItemEntity.setSupportTransNum(GetSupportTransNum(transportOrderItemEntity.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));

						transportOrderItemEntities.add(transportOrderItemEntity);
					}

//					}
				} else {
					if (transportOrderItemEntity.getQuantity() > 0) {
						transportOrderItemEntity.setWeight(GetBigDecimalWeight(pOrderSKUs.stream().mapToInt(SaleOrderItemJobVO::getProductQuantity).sum(), pOrderSKUs.get(0).getPackageGrossConversionRate()));
						transportOrderItemEntity.setStatus(0);
						transportOrderItemEntity.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
						transportOrderItemEntity.setProductSkuId(skuId);
						transportOrderItemEntity.setProductName(orderSku.getProductName());
						transportOrderItemEntity.setSpData(orderSku.getSpData());
						transportOrderItemEntity.setSkuCode(orderSku.getSkuCode());
						transportOrderItemEntity.setProductId(orderSku.getProductId());
						transportOrderItemEntity.setProductId(pOrderSKUs.get(0).getProductId());
						transportOrderItemEntity.setSupportTrans(pOrderSKUs.get(0).getSupportTrans());
						transportOrderItemEntity.setSupportTransPrice(pOrderSKUs.get(0).getSupportTransPrice());
						transportOrderItemEntity.setSupportTransUnitId(pOrderSKUs.get(0).getSupportTransUnitId());
						if (Func.isNotEmpty(transportOrderItemEntity.getSupportTransUnitId()))
							transportOrderItemEntity.setSupportTransNum(GetSupportTransNum(transportOrderItemEntity.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));

						transportOrderItemEntities.add(transportOrderItemEntity);
					}
				}
				//仓库修改
				if (inventoryList.size() > 0)
					inventoryLists.addAll(inventoryList);
				//判断是不是存在该商品
				List<PurchaseOrderItemEntity> purchaseOrderUpList = new ArrayList<>();
				if (purchaseOrderItemListOld.size() > 0)
					purchaseOrderUpList = purchaseOrderItemListOld.stream().filter(i -> i.getProductSkuId().equals(skuId)).toList();
				if (purchaseOrderUpList.size() == 0) {
					//生成采购单商品
					if (purchaseOrderItem.getQuantity() > 0) {
						purchaseOrderItem.setProductSkuId(skuId);
						purchaseOrderItem.setProductId(pOrderSKUs.get(0).getProductId());
						purchaseOrderItem.setProductName(pOrderSKUs.get(0).getProductName());
						purchaseOrderItem.setProductId(orderSku.getProductId());
						purchaseOrderItem.setUnitPrice(pOrderSKUs.get(0).getPrice());
						purchaseOrderItem.setWholePrice(pOrderSKUs.get(0).getWholePrice());
						purchaseOrderItem.setSpData(pOrderSKUs.get(0).getSpData());
						purchaseOrderItem.setSkuCode(pOrderSKUs.get(0).getSkuCode());
						purchaseOrderItem.setOrderPrice(pOrderSKUs.get(0).getProductUnitPrice());
						purchaseOrderItem.setRemainingQuantity(purchaseOrderItem.getQuantity());
						purchaseOrderItem.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
						purchaseOrderItem.setWeight(CommonUtil.WeightBigDecimalInt(new BigDecimal(purchaseOrderItem.getQuantity()).multiply(pOrderSKUs.get(0).getPackageGrossConversionRate())));

						purchaseOrderItem.setSupportTrans(pOrderSKUs.get(0).getSupportTrans());
						purchaseOrderItem.setSupportTransPrice(pOrderSKUs.get(0).getSupportTransPrice());
						purchaseOrderItem.setSupportTransUnitId(pOrderSKUs.get(0).getSupportTransUnitId());
						if (Func.isNotEmpty(orderSku.getSupportTransUnitId())) {
							purchaseOrderItem.setSupportTransNum(GetSupportTransNum(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));
						}

						if (Func.isNotEmpty(orderSku.getSupportTransUnitId()) && Func.isNotEmpty(transportUnitEntities) && transportUnitEntities.size() > 0) {
							TransportUnitEntity transportUnitEntity = transportUnitEntities.stream().filter(q -> q.getId().equals(orderSku.getSupportTransUnitId())).findFirst().orElse(null);
							if (transportUnitEntity != null) {
								purchaseOrderItem.setSupportTrans(transportUnitEntity.getTransportName());
								purchaseOrderItem.setSupportTransPrice(transportUnitEntity.getPrice());
								purchaseOrderItem.setSupportTransUnitId(transportUnitEntity.getId());
								//计算出需要多少运输单位
								if (Func.isNotEmpty(orderSku.getSupportTransUnitId()))
									purchaseOrderItem.setSupportTransNum(GetSupportTransNum(purchaseOrderItem.getQuantity(), orderSku.getTransportConversionRate(), orderSku.getSupportTransUnitId()));
							}
						}
						purchaseOrderItemList.add(purchaseOrderItem);
					}
				} else {
					PurchaseOrderItemEntity purchaseOrderItemUpdate = purchaseOrderUpList.get(0);
					if (purchaseOrderItem.getQuantity() > 0) {
						purchaseOrderItemUpdate.setQuantity(purchaseOrderItemUpdate.getQuantity() + purchaseOrderItem.getQuantity());
						purchaseOrderItemUpdate.setRemainingQuantity(purchaseOrderItemUpdate.getRemainingQuantity() + purchaseOrderItem.getQuantity());
						purchaseOrderItemUpdate.setWeight(purchaseOrderItemUpdate.getWeight() + GetWeight(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getPackageGrossConversionRate()));
						purchaseOrderItemUpdate.setSubtotal(purchaseOrderItemUpdate.getSubtotal() + price * purchaseOrderItem.getQuantity());
						purchaseOrderItemUpdateList.add(purchaseOrderItemUpdate);
					}
				}
			}
			//生成采购单
			if (purchaseOrderItemList.size() > 0 || purchaseOrderItemUpdateList.size() > 0) {
				//获取总览之和
				int sum = 0;
				int amountSum = 0;
				int weightSum = 0;
				if (purchaseOrderItemList.size() > 0) {
					sum = purchaseOrderItemList.stream().mapToInt(PurchaseOrderItemEntity::getQuantity).sum();
					weightSum = purchaseOrderItemList.stream().mapToInt(PurchaseOrderItemEntity::getWeight).sum();
					amountSum = purchaseOrderItemList.stream().mapToInt(i -> i.getQuantity() * i.getOrderPrice()).sum();
				}
				if (purchaseOrderItemUpdateList.size() > 0) {
					sum = purchaseOrderItemUpdateList.stream().mapToInt(PurchaseOrderItemEntity::getQuantity).sum();
					weightSum = purchaseOrderItemUpdateList.stream().mapToInt(PurchaseOrderItemEntity::getWeight).sum();
					amountSum = purchaseOrderItemUpdateList.stream().mapToInt(i -> i.getQuantity() * i.getOrderPrice()).sum();
				}
				purchaseOrder.setTotalCount(sum);
				purchaseOrder.setTotalWeight(weightSum);
				purchaseOrder.setTotalAmount(amountSum);
				purchaseOrder.setSelfPurchaser(type);
				purchaseOrder.setPurchaseNo(purchaseNo);
				purchaseOrder.setBatchNo(purchaseBathNo);
				purchaseOrder.setAllocationStatus(1);
				if (purchaseOrder.getId() == null)
					purchaseOrderService.save(purchaseOrder);
				else {
					purchaseOrderService.updateById(purchaseOrder);
				}
				for (PurchaseOrderItemEntity purchaseOrderItem : purchaseOrderItemList) {
					purchaseOrderItem.setPurchaseId(purchaseOrder.getId());
				}
				if (purchaseOrderItemList.size() > 0) {
					log.info("[截单]结算采购单：{}", purchaseOrderItemList.size());
					purchaseOrderItemList = getMergeIdenticalSKUPurItem(purchaseOrderItemList);
					log.info("[截单]结算采购单：{}", purchaseOrderItemList.size());
					purchaseOrderItemService.saveBatch(purchaseOrderItemList);
				}
				if (purchaseOrderItemUpdateList.size() > 0) {
					purchaseOrderItemService.updateBatchById(purchaseOrderItemUpdateList);
				}


				//更新采购单和订单关系
				AddSaleOrderItem(purchaseOrderItemList, orderList);
				AddSaleOrderItem(purchaseOrderItemUpdateList, orderList);
			}

			//生成转运单
			GenerateTransportOrder(transportOrderItemEntities, purchaseOrder, orderWarehouseId, orderWarehousePId, null);

			//更新仓库库存
			if (inventoryLists.size() > 0) {
				for (InventoryEntity temp : inventoryLists) {
					temp.setWeight(CommonUtil.WeightToBigDecimal(temp.getWeight()));
				}
				inventoryService.updateBatchById(inventoryLists);
			}
		}

	}

	/**
	 * 仓库处理
	 *
	 * @param inventoryEntity    仓库
	 * @param skuId              商品ID
	 * @param warehouseId        仓库ID
	 * @param orderWarehouseId   订单
	 * @param orderWarehousePId  订单总仓ID
	 * @param quantity           商品数量
	 * @param price              商品价格
	 * @param purchaseOrderItem  采购单商品
	 * @param transportOrderItem 转运单商品
	 */
	private Integer WarehouseProcessing(List<InventoryEntity> inventoryEntity, Long skuId, Long warehouseId,
										Long orderWarehouseId, Long orderWarehousePId, int quantity, int price,
										PurchaseOrderItemEntity purchaseOrderItem, TransportOrderItemEntity transportOrderItem,
										List<InventoryEntity> inventoryEntities, BigDecimal pgRate, Integer localCount) {
		/**
		 * 总仓数量
		 */
		Integer totalCKNum = 0;
		/**
		 * 分仓数量
		 */
		Integer totalBCKNum = 0;
		List<InventoryEntity> inventoryPList =new ArrayList<>();
		if (!orderWarehousePId.equals(warehouseId)){
			 inventoryPList = inventoryEntity.stream().filter(i -> i.getWarehouseId().equals(orderWarehousePId) && i.getProductSkuId().equals(skuId)).toList();
		}
		List<InventoryEntity> inventoryList = inventoryEntity.stream().filter(i -> i.getWarehouseId().equals(warehouseId) && i.getProductSkuId().equals(skuId)).toList();
		if (inventoryPList.size() > 0) {

			Integer currentStockP = inventoryPList.stream().mapToInt(InventoryEntity::getCurrentStock).sum();
			Integer lockStockP = inventoryPList.stream().mapToInt(InventoryEntity::getLockStock).sum();
			//获取仓库库存量
			totalCKNum = currentStockP - lockStockP;
			if (totalCKNum <= 0) {
				totalCKNum = 0;
			}
			log.info("总仓数量：{}", totalCKNum);
		}
		if (inventoryList.size() > 0) {
			Integer currentStock = inventoryList.stream().mapToInt(InventoryEntity::getCurrentStock).sum();
			Integer lockStock = inventoryList.stream().mapToInt(InventoryEntity::getLockStock).sum();
			//获取仓库库存量
			totalBCKNum = currentStock - lockStock;
			if (totalBCKNum <= 0) {
				totalBCKNum = 0;
			}
			log.info("分仓数量：{}", totalBCKNum);
		}
		if (totalBCKNum > 0) {
			if (totalBCKNum >= quantity) {
				localCount = quantity;
			} else {
				localCount = totalBCKNum;
			}
		}
		if (totalBCKNum == 0 && totalCKNum == 0) {
			log.info("总仓和分仓无库存");
			//仓库没有存货，直接采购
			purchaseOrderItem.setQuantity(quantity);
			purchaseOrderItem.setWeight(GetWeight(quantity, pgRate));
			purchaseOrderItem.setSubtotal(price * quantity);
			//更新仓库库存
		} else {
			//仓库处理
			//判断本地仓库
			if (totalBCKNum == 0) {
				log.info("分仓无库存");
				//减去总仓数据
				if (totalCKNum >= quantity) {
					log.info("总仓有库存:{}", totalCKNum);
					// 减去总仓库存库
					//总仓处理
					if (Func.isNotEmpty(inventoryPList.size() > 0)) {
						totalCKNum = totalCKNum - quantity;
						Integer toQuantity = quantity;
						for (InventoryEntity inventoryP : inventoryPList) {
							//inventoryP.setCurrentStock(totalCKNum);
							int i = inventoryP.getCurrentStock() - inventoryP.getLockStock();
							if (i > 0) {
								if (toQuantity >= i) {
									inventoryP.setLockStock(inventoryP.getLockStock() + i);
								} else {
									inventoryP.setLockStock(inventoryP.getLockStock() + toQuantity);
								}
								toQuantity = toQuantity - i;
								inventoryEntities.add(inventoryP);
								if (toQuantity < 0) {
									break;
								}
							}
						}
						//转运表处理
						transportOrderItem.setQuantity(quantity);
					}
				} else {
					if (Func.isNotEmpty(inventoryPList.size() > 0)) {
						// 减去总仓库存库
						Integer toQuantity = quantity;
						quantity = quantity - totalCKNum;
						for (InventoryEntity inventoryP : inventoryPList) {
							//inventoryP.setCurrentStock(totalCKNum);
							int i = inventoryP.getCurrentStock() - inventoryP.getLockStock();
							if (i > 0) {
								if (toQuantity >= i) {
									inventoryP.setLockStock(inventoryP.getLockStock() + i);
								} else {
									inventoryP.setLockStock(inventoryP.getLockStock() + toQuantity);
								}
								toQuantity = toQuantity - i;
								inventoryEntities.add(inventoryP);
								if (toQuantity < 0) {
									break;
								}
							}
						}
						//inventoryP.setCurrentStock(0);
//						inventoryP.setLockStock(inventoryP.getLockStock() + totalCKNum);
//						inventoryEntities.add(inventoryP);
						transportOrderItem.setQuantity(totalCKNum);
					}
					//生成采购单商品
					purchaseOrderItem.setQuantity(quantity);
					purchaseOrderItem.setWeight(GetWeight(quantity, pgRate));
					purchaseOrderItem.setSubtotal(price * quantity);
					log.info("分仓处理完成，并生成对应的采购数量");
				}
			} else if (totalBCKNum > 0) {
				log.info("分仓有库存:{}", totalBCKNum);
				//生成采购单商品
				if (totalBCKNum >= quantity) {
					// 减去本地仓库存库
					Integer toQuantity = quantity;
					totalBCKNum = totalBCKNum - quantity;
					//inventory.setCurrentStock(totalBCKNum);
//					inventory.setLockStock(inventory.getLockStock() + quantity);
//					inventoryEntities.add(inventory);
					for (InventoryEntity inventory : inventoryList) {
						//inventoryP.setCurrentStock(totalCKNum);
						int i = inventory.getCurrentStock() - inventory.getLockStock();
						if (i > 0) {
							if (toQuantity >= i) {
								inventory.setLockStock(inventory.getLockStock() + i);
							} else {
								inventory.setLockStock(inventory.getLockStock() + toQuantity);
							}
							toQuantity = toQuantity - i;
							inventoryEntities.add(inventory);
							if (toQuantity < 0) {
								break;
							}
						}
					}
				} else {
					log.info("分仓库存不足:{}", totalBCKNum);
					//本地仓库存库不足，减去本地仓库存库
					//转运数量
					Integer transportQuantity = quantity - totalBCKNum;
					Integer toQuantity = quantity;
					quantity = quantity - totalBCKNum;

					//inventory.setCurrentStock(0);
//					inventory.setLockStock(inventory.getLockStock() + totalBCKNum);
//					inventoryEntities.add(inventory);
					for (InventoryEntity inventory : inventoryList) {
						//inventoryP.setCurrentStock(totalCKNum);
						int i = inventory.getCurrentStock() - inventory.getLockStock();
						if (i > 0) {
							if (toQuantity >= i) {
								inventory.setLockStock(inventory.getLockStock() + i);
							} else {
								inventory.setLockStock(inventory.getLockStock() + toQuantity);
							}
							toQuantity = toQuantity - i;
							inventoryEntities.add(inventory);
							if (toQuantity < 0) {
								break;
							}
						}
					}
					log.info("分仓处理库存量完成");
					// 减去本地仓库存库0
					//总仓数量大于需求量
					if (totalCKNum >= quantity) {
						log.info("总仓有库存:{},商品数量：{}", totalCKNum, quantity);
						if (Func.isNotEmpty(inventoryPList.size() > 0)) {
							//总仓处理
							toQuantity = quantity;
							totalCKNum = totalCKNum - quantity;
							//inventoryP.setCurrentStock(totalCKNum);
//							inventoryP.setLockStock(inventoryP.getLockStock() + quantity);
//							inventoryEntities.add(inventoryP);

							for (InventoryEntity inventoryP : inventoryPList) {
								//inventoryP.setCurrentStock(totalCKNum);
								int i = inventoryP.getCurrentStock() - inventoryP.getLockStock();
								if (i > 0) {
									if (toQuantity >= i) {
										inventoryP.setLockStock(inventoryP.getLockStock() + i);
									} else {
										inventoryP.setLockStock(inventoryP.getLockStock() + toQuantity);
									}
									toQuantity = toQuantity - i;
									inventoryEntities.add(inventoryP);
									if (toQuantity < 0) {
										break;
									}
								}
							}
							log.info("总仓处理完成");
							//转运表处理
							transportOrderItem.setQuantity(transportQuantity);
						}

					} else {
						if (Func.isNotEmpty(inventoryPList.size() > 0)) {
							log.info("总仓有库存:{},商品数量：{}", totalCKNum, quantity);
							// 减去总仓库存库
							toQuantity = quantity;
							quantity = quantity - totalCKNum;
							//inventoryP.setCurrentStock(0);
//							inventoryP.setLockStock(inventoryP.getLockStock() + totalCKNum);
//							inventoryEntities.add(inventoryP);
							for (InventoryEntity inventoryP : inventoryPList) {
								//inventoryP.setCurrentStock(totalCKNum);
								int i = inventoryP.getCurrentStock() - inventoryP.getLockStock();
								if (i > 0) {
									if (toQuantity >= i) {
										inventoryP.setLockStock(inventoryP.getLockStock() + i);
									} else {
										inventoryP.setLockStock(inventoryP.getLockStock() + toQuantity);
									}
									toQuantity = toQuantity - i;
									inventoryEntities.add(inventoryP);
									if (toQuantity < 0) {
										break;
									}
								}
							}
							//转运表处理
							transportOrderItem.setQuantity(transportQuantity);
						}
						//生成采购单商品
						purchaseOrderItem.setQuantity(quantity);
						purchaseOrderItem.setWeight(GetWeight(quantity, pgRate));
						purchaseOrderItem.setSubtotal(price * quantity);
						log.info("分仓处理完成，并生成对应的采购数量");
					}
				}
			}
		}
		return localCount;
	}


	@Scheduled(cron = "0 * * * * ? ")
//	@XxlJob("cutAddOrder")
	public void CutOrderAddOrder() {
		log.info("[order_cutAddOrder]定时任务开始-----------------");
		log.info("[截单]开始加单截单");
		Boolean lock = redisLockClient.lockFair("lock:cutAddOrder", 1, 300, () -> {
			QueryWrapper<SaleOrderEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("is_cut_order", BusinessConstant.IS_CUT_ORDER_NO);
			queryWrapper.eq("add_order", BusinessConstant.IS_ADD_ORDER_YES);
			queryWrapper.eq("batch_no", GenerateNumberUtil.PurchaseCodeF());
			List<SaleOrderEntity> saleOrderEntityList = saleOrderService.list(queryWrapper);

			if (saleOrderEntityList.size() == 0) {
				return Boolean.TRUE;
			}
			List<Long> warehouseIds = saleOrderEntityList.stream().map(i -> i.getWarehouseId()).distinct().toList();
			List<TransportUnitEntity> transportUnitEntities = new ArrayList<>();
			List<SaleOrderItemJobVO> saleOrderList = saleOrderItemService.getAddOrderList(GenerateNumberUtil.PurchaseCodeF());
			if (saleOrderList.size() == 0) {
				return Boolean.TRUE;
			}
			List<WarehouseJobVO> warehouseList = warehouseService.getWarehouseAddCutList(warehouseIds);
			if (warehouseList.size() == 0) return Boolean.TRUE;
			List<Long> warehouseIdList = warehouseList.stream().map(WarehouseJobVO::getId).distinct().collect(Collectors.toList());

			//根据商品和仓库信息详情获取采购员
			//仓库获取采购员
			List<WarehousePurchaseIdVO> purchaseEntities = warehousePurchaseService.getWarehousePurchase(warehouseIdList);
			List<Long> purchaseIdList = purchaseEntities.stream().map(WarehousePurchaseIdVO::getPurchaserId).distinct().collect(Collectors.toList());

			if (saleOrderEntityList.size() > 0)
				transportUnitEntities = transportUnitService.getIds(saleOrderList.stream().map(SaleOrderItemJobVO::getProductSkuId).distinct().collect(Collectors.toList()));

			for (WarehouseJobVO warehouse : warehouseList) {
				log.info("[截单]开始加单截单订单信息：{}",saleOrderList.size());
				GeneratePurchaseOrder(saleOrderList, warehouse, purchaseIdList, transportUnitEntities, BusinessConstant.ORDER_TYPE_SALE);
			}

			if (saleOrderEntityList.size() > 0) {
				saleOrderEntityList.forEach(item -> {
					if (Objects.equals(item.getIsCutOrder(), BusinessConstant.IS_CUT_ORDER_NO)) {
						item.setIsCutOrder(BusinessConstant.IS_CUT_ORDER_YES);
					}
				});
				saleOrderService.updateBatchById(saleOrderEntityList);
			}
			return Boolean.TRUE;
		});

		if (Func.isNull(lock)) {
			log.warn("[order_cutAddOrder]已存在正在处理中的定时任务，本次处理结束");
		}
	}

	/***
	 * 获取重量
	 * @param quantity 数据量
	 * @param cm 转换率
	 * @return
	 */
	public int GetWeight(Integer quantity, BigDecimal cm) {
		if (Func.isEmpty(quantity) || Func.isEmpty(cm)) return 0;
		Integer weightInt = CommonUtil.WeightBigDecimalInt(new BigDecimal(quantity).multiply(cm));
		return weightInt;
	}

	/***
	 * 获取重量
	 * @param quantity 数据量
	 * @param cm 转换率
	 * @return
	 */
	public BigDecimal GetBigDecimalWeight(Integer quantity, BigDecimal cm) {
		if (Func.isEmpty(quantity) || Func.isEmpty(cm)) return BigDecimal.ZERO;
		Integer weightInt = CommonUtil.WeightBigDecimalInt(new BigDecimal(quantity).multiply(cm));
		return CommonUtil.WeightIntBigDecimal(weightInt);
	}

	/**
	 * 获取配套运输数量
	 *
	 * @param quantity 数量
	 * @param tr       转换率
	 * @param id       配套运输单位id
	 * @return
	 */
	public Integer GetSupportTransNum(Integer quantity, Integer tr, Long id) {
		if (Func.isEmpty(quantity) || Func.isEmpty(tr) || Func.isEmpty(id)) return null;
		Integer supportTransNum = quantity * tr;
		return supportTransNum;
	}

	/**
	 * * 生成转运单
	 *
	 * @param transportOrderItemEntities 转运单集合
	 * @param purchaseOrderEntity        采购单
	 * @param orderWarehouseId           订单ID
	 * @param orderWarehousePId          订单父级ID
	 */
	private void GenerateTransportOrder(List<TransportOrderItemEntity> transportOrderItemEntities,
										PurchaseOrderEntity purchaseOrderEntity, Long orderWarehouseId, Long orderWarehousePId,
										TransportOrderEntity transportOrderEntity) {
		if (orderWarehouseId.equals(orderWarehousePId)) {
			return;
		}
		if (transportOrderItemEntities.size() > 0) {
			int sum = transportOrderItemEntities.stream().mapToInt(TransportOrderItemEntity::getQuantity).sum();
			int sumW = transportOrderItemEntities.stream()
				.filter(i -> Func.isNotEmpty(i.getWeight()))
				.mapToInt(i -> CommonUtil.WeightBigDecimalInt(i.getWeight()))
				.sum();//先判断转运单是不是存在
			if (Func.isEmpty(transportOrderEntity)) {
				QueryWrapper<TransportOrderEntity> queryWrapper = new QueryWrapper<>();

				queryWrapper.eq("warehouse_id", orderWarehousePId);
				queryWrapper.eq("transport_warehouse_id", orderWarehouseId);
				queryWrapper.eq("purchase_order_no", purchaseOrderEntity.getPurchaseNo());
				queryWrapper.eq("status", 0);

				List<TransportOrderEntity> list = iTransportOrderService.list(queryWrapper);
				if (list.size() > 0) {
					transportOrderEntity = iTransportOrderService.list(queryWrapper).get(0);
				}
			}
			if (Func.isNotEmpty(transportOrderEntity)) {
				transportOrderEntity.setQuantity(sum + transportOrderEntity.getQuantity());
				transportOrderEntity.setWeight(sumW + transportOrderEntity.getWeight());
				iTransportOrderService.updateById(transportOrderEntity);
				QueryWrapper<TransportOrderItemEntity> queryWrapper = new QueryWrapper<>();
				queryWrapper.eq("transport_order_id", transportOrderEntity.getId());
				List<TransportOrderItemEntity> list = iTransportOrderItemService.list(queryWrapper);
				List<TransportOrderItemEntity> updateList = new ArrayList<>();
				List<TransportOrderItemEntity> addList = new ArrayList<>();
				for (TransportOrderItemEntity item : transportOrderItemEntities) {
					List<TransportOrderItemEntity> itemEntities = list.stream().filter(i -> i.getProductSkuId().equals(item.getProductSkuId())).toList();
					if (itemEntities.size() > 0) {
						TransportOrderItemEntity item1 = itemEntities.get(0);
						item1.setQuantity(item1.getQuantity() + item.getQuantity());
						item1.setWeight(item1.getWeight().add(item.getWeight()));
						if (Func.isNotEmpty(item.getSupportTransNum())) {
							item1.setSupportTransNum(Func.isEmpty(item1.getSupportTransNum()) ? 0 : item1.getSupportTransNum() + (item.getSupportTransNum()));
						}
						updateList.add(item1);
					} else {
						item.setTransportOrderId(transportOrderEntity.getId());
						addList.add(item);
					}
				}
				if (updateList.size() > 0)
					iTransportOrderItemService.updateBatchById(updateList);
				if (addList.size() > 0) {
					addList = getMergeIdenticalSKU(addList);
					iTransportOrderItemService.saveBatch(addList);
				}
			} else {
				transportOrderEntity = new TransportOrderEntity();
				//获取子集中的总件数量
				if (Func.isNotEmpty(purchaseOrderEntity.getId())) {
					transportOrderEntity.setPurchaseOrderId(purchaseOrderEntity.getId());
				}
				transportOrderEntity.setPurchaseOrderNo(purchaseOrderEntity.getPurchaseNo());
				transportOrderEntity.setTransportNo(GenerateNumberUtil.TransportCode());
				transportOrderEntity.setStatus(0);
				transportOrderEntity.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
				transportOrderEntity.setTransportWarehouseId(orderWarehouseId);
				transportOrderEntity.setWarehouseId(orderWarehousePId);
				transportOrderEntity.setQuantity(sum);
				transportOrderEntity.setWeight(sumW);

				iTransportOrderService.save(transportOrderEntity);

				//修改自己中的主表的id关联
				TransportOrderEntity finalTransportOrderEntity = transportOrderEntity;
				transportOrderItemEntities.forEach(i -> {
					i.setTransportOrderId(finalTransportOrderEntity.getId());
				});
				//判断是不是存在相同的SKU
				transportOrderItemEntities = getMergeIdenticalSKU(transportOrderItemEntities);
				//保存子表
				iTransportOrderItemService.saveBatch(transportOrderItemEntities);
			}

		}
	}

	/**
	 * 合并相同的SKU转运单
	 *
	 * @param transportOrderItemEntities
	 * @return
	 */
	private List<TransportOrderItemEntity> getMergeIdenticalSKU(List<TransportOrderItemEntity> transportOrderItemEntities) {
		List<TransportOrderItemEntity> mergeList = new ArrayList<>();
		for (TransportOrderItemEntity item : transportOrderItemEntities) {
			if (mergeList.size() > 0) {
				TransportOrderItemEntity item1 = mergeList.stream().filter(i -> Objects.equals(i.getProductSkuId(), item.getProductSkuId())).findFirst().orElse(null);
				if (item1 != null) {
					if (Func.isNotEmpty(item.getSupportTransUnitId()))
						item1.setSupportTransNum(item.getSupportTransNum() + (Func.isEmpty(item1.getSupportTransNum()) ? 0 : item1.getSupportTransNum()));
					item1.setQuantity(item.getQuantity() + item1.getQuantity());
					item1.setWeight(item.getWeight().add(item1.getWeight()));
				} else {
					mergeList.add(item);
				}
			} else {
				mergeList.add(item);
			}
		}
		return mergeList;
	}

	/**
	 * 合并相同的SKU采购单
	 *
	 * @param purchaseOrderItemEntities 采购单子项列表
	 * @return 合并后的采购单子项列表
	 */
	private List<PurchaseOrderItemEntity> getMergeIdenticalSKUPurItem(List<PurchaseOrderItemEntity> purchaseOrderItemEntities) {

		if (purchaseOrderItemEntities == null || purchaseOrderItemEntities.isEmpty()) {
			return Collections.emptyList();
		}
		long timestamp = GenerateNumberUtil.TimeStamp();
		return new ArrayList<>(purchaseOrderItemEntities.stream()
			.collect(Collectors.groupingBy(
				PurchaseOrderItemEntity::getProductSkuId,
				Collectors.reducing(null, (existing, item) -> {
					// 如果 existing 为 null，返回一个新的副本作为初始值
					if (existing == null) {
						item.setCreateTimeAt(timestamp);
						return BeanUtil.copyProperties(item, PurchaseOrderItemEntity.class);
					}
					// 否则进行合并操作
					PurchaseOrderItemEntity merged =Objects.requireNonNull(BeanUtil.copyProperties(existing, PurchaseOrderItemEntity.class));

					// 合并数量相关字段
					merged.setQuantity(existing.getQuantity() + item.getQuantity());
					merged.setWeight(existing.getWeight() + item.getWeight());
					merged.setSubtotal(existing.getSubtotal() + item.getSubtotal());
					merged.setRemainingQuantity(existing.getRemainingQuantity() + item.getRemainingQuantity());

					// 支持运输单位数量合并
					if (Func.isNotEmpty(item.getSupportTransUnitId())) {
						merged.setSupportTransNum(
							(Func.isEmpty(existing.getSupportTransNum()) ? 0 : existing.getSupportTransNum())
								+ item.getSupportTransNum()
						);
					}
					// 统一设置时间戳
					merged.setCreateTimeAt(timestamp);
					return merged;
				})
			))
			.values());
	}

	/**
	 * 初始化采购单
	 *
	 * @param purchaseNo           采购单批次号
	 * @param purchaseBathNo       编号
	 * @param purchaserId          采购人ID
	 * @param purchaserWarehouseId 采购仓库ID
	 * @param transportWarehouseId 转运仓库ID
	 * @param orderWarehouseId     订单仓库ID
	 * @param type                 采购类型 1 自采 0 采购
	 * @return
	 */
	private PurchaseOrderEntity purchaseOrderInit(String purchaseNo, String purchaseBathNo, Long purchaserId,
												  Long purchaserWarehouseId, Long transportWarehouseId, Long orderWarehouseId, int type) {
		PurchaseOrderEntity purchaseOrder = new PurchaseOrderEntity();
		purchaseOrder.setPurchaseNo(purchaseNo);
		purchaseOrder.setBatchNo(purchaseBathNo);
		purchaseOrder.setTotalAmount(0);
		purchaseOrder.setTotalCount(0);
		purchaseOrder.setTotalWeight(0);
		purchaseOrder.setPurchaseStatus(2);
		purchaseOrder.setPurchaserId(purchaserId);
		purchaseOrder.setWarehouseId(transportWarehouseId);
		purchaseOrder.setPurchaserWarehouseId(purchaserWarehouseId);
		purchaseOrder.setOrderWarehouseId(orderWarehouseId);
		purchaseOrder.setSelfPurchaser(type);
		purchaseOrder.setAllocationStatus(1);
		purchaseOrder.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
		purchaseOrder.setCutTime(LocalDateTime.now());
		return purchaseOrder;
	}

	/**
	 * 初始化采购单子表
	 *
	 * @param purchaseOrderId 采购单ID
	 * @param productSkuId    商品SKU ID
	 * @param pOrderSKUs      采购单子表
	 * @return
	 */
	private PurchaseOrderItemEntity purchaseOrderItemInit(Long purchaseOrderId, Long productSkuId, List<SaleOrderItemJobVO> pOrderSKUs) {
		int price = pOrderSKUs.get(0).getProductUnitPrice();
		int quantity = pOrderSKUs.stream().mapToInt(SaleOrderItemJobVO::getProductQuantity).sum();
		log.info("[截单日志]sku：{}对应的数量：{}",productSkuId, quantity);
		PurchaseOrderItemEntity purchaseOrderItem = new PurchaseOrderItemEntity();
		purchaseOrderItem.setPurchaseId(purchaseOrderId);
		purchaseOrderItem.setProductSkuId(productSkuId);
		purchaseOrderItem.setProductId(pOrderSKUs.get(0).getProductId());
		purchaseOrderItem.setProductName(pOrderSKUs.get(0).getProductName());
		purchaseOrderItem.setUnitPrice(pOrderSKUs.get(0).getPrice());
		purchaseOrderItem.setWholePrice(pOrderSKUs.get(0).getWholePrice());
		purchaseOrderItem.setQuantity(quantity);
		purchaseOrderItem.setRemainingQuantity(quantity);
		purchaseOrderItem.setWeight(GetWeight(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getPackageGrossConversionRate()));
		purchaseOrderItem.setOrderPrice(price);
		purchaseOrderItem.setSubtotal(price * quantity);
		purchaseOrderItem.setSpData(pOrderSKUs.get(0).getSpData());
		purchaseOrderItem.setSkuCode(pOrderSKUs.get(0).getSkuCode());
		purchaseOrderItem.setSupportTrans(pOrderSKUs.get(0).getSupportTrans());
		purchaseOrderItem.setSupportTransPrice(pOrderSKUs.get(0).getSupportTransPrice());
		purchaseOrderItem.setSupportTransUnitId(pOrderSKUs.get(0).getSupportTransUnitId());
		purchaseOrderItem.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
		if (Func.isNotEmpty(purchaseOrderItem.getSupportTransUnitId()))
			purchaseOrderItem.setSupportTransNum(GetSupportTransNum(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));
		return purchaseOrderItem;
	}

	/**
	 * 更新采购单子表
	 *
	 * @param purchaseOrderItem 采购单子表
	 * @param pOrderSKUs        采购单子表
	 * @return
	 */
	private PurchaseOrderItemEntity purchaseOrderItemUpdate(PurchaseOrderItemEntity purchaseOrderItem, List<SaleOrderItemJobVO> pOrderSKUs) {
		int price = pOrderSKUs.get(0).getProductUnitPrice();
		int quantity = pOrderSKUs.stream().mapToInt(SaleOrderItemJobVO::getProductQuantity).sum();
		purchaseOrderItem.setQuantity(purchaseOrderItem.getQuantity() + quantity);
		purchaseOrderItem.setRemainingQuantity(purchaseOrderItem.getRemainingQuantity() + quantity);
		purchaseOrderItem.setWeight(purchaseOrderItem.getWeight() + GetWeight(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getPackageGrossConversionRate()));
		purchaseOrderItem.setSubtotal(purchaseOrderItem.getSubtotal() + price * quantity);
		if (Func.isNotEmpty(purchaseOrderItem.getSupportTransUnitId()))
			purchaseOrderItem.setSupportTransNum(GetSupportTransNum(purchaseOrderItem.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));

		return purchaseOrderItem;
	}

	/**
	 * 采购单子表初始化
	 *
	 * @param skuId      SKU ID
	 * @param pOrderSKUs 采购单子表
	 * @return
	 */
	private TransportOrderItemEntity transportOrderItemInit(Long skuId, List<SaleOrderItemJobVO> pOrderSKUs) {
		TransportOrderItemEntity transportOrderItemEntity = new TransportOrderItemEntity();
		Integer quantityTran = pOrderSKUs.stream().mapToInt(i -> i.getProductQuantity()).sum();
		transportOrderItemEntity.setQuantity(quantityTran);
		transportOrderItemEntity.setWeight(GetBigDecimalWeight(quantityTran, pOrderSKUs.get(0).getPackageGrossConversionRate()));
		transportOrderItemEntity.setStatus(0);
		transportOrderItemEntity.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
		transportOrderItemEntity.setProductSkuId(skuId);
		transportOrderItemEntity.setProductName(pOrderSKUs.get(0).getProductName());
		transportOrderItemEntity.setSpData(pOrderSKUs.get(0).getSpData());
		transportOrderItemEntity.setSkuCode(pOrderSKUs.get(0).getSkuCode());
		transportOrderItemEntity.setProductId(pOrderSKUs.get(0).getProductId());
		transportOrderItemEntity.setSupportTrans(pOrderSKUs.get(0).getSupportTrans());
		transportOrderItemEntity.setSupportTransPrice(pOrderSKUs.get(0).getSupportTransPrice());
		transportOrderItemEntity.setSupportTransUnitId(pOrderSKUs.get(0).getSupportTransUnitId());
		if (Func.isNotEmpty(transportOrderItemEntity.getSupportTransUnitId()))
			transportOrderItemEntity.setSupportTransNum(GetSupportTransNum(transportOrderItemEntity.getQuantity(), pOrderSKUs.get(0).getTransportConversionRate(), pOrderSKUs.get(0).getSupportTransUnitId()));

		return transportOrderItemEntity;
	}

	/**
	 * 添加销售单子表
	 *
	 * @param purchaseOrderItemList 采购单子表
	 * @param orderList             订单列表
	 */
	private void AddSaleOrderItem(List<PurchaseOrderItemEntity> purchaseOrderItemList, List<SaleOrderItemJobVO> orderList) {
		if (purchaseOrderItemList.size() == 0) return;
		//更新采购单和订单关系
		List<PurchaseOrderSaleItemEntity> purchaseOrderSaleItemEntities = new ArrayList<>();
		for (PurchaseOrderItemEntity purchaseOrderItem : purchaseOrderItemList) {
			//生成采购单和订单关系
			List<SaleOrderItemJobVO> orderListDetails = orderList.stream().filter(i -> i.getProductSkuId().equals(purchaseOrderItem.getProductSkuId())).toList();
			for (SaleOrderItemJobVO orderListDetail : orderListDetails) {
				if (Func.isNotEmpty(orderListDetail.getId())) {
					PurchaseOrderSaleItemEntity purchaseOrderSaleItemEntity = new PurchaseOrderSaleItemEntity();
					purchaseOrderSaleItemEntity.setOrderItemId(orderListDetail.getId());
					purchaseOrderSaleItemEntity.setPurchaseItemId(purchaseOrderItem.getId());
					purchaseOrderSaleItemEntity.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
					purchaseOrderSaleItemEntity.setProductSkuId(orderListDetail.getProductSkuId());
					purchaseOrderSaleItemEntities.add(purchaseOrderSaleItemEntity);
				}
			}
		}
		if (purchaseOrderSaleItemEntities.size() > 0)
			purchaseOrderSaleItemService.saveBatch(purchaseOrderSaleItemEntities);
	}

	/**
	 * 获取采购单
	 *
	 * @param purchaserId          采购员ID
	 * @param purchaseNo           采购单号
	 * @param type                 采购类型
	 * @param transportWarehouseId 采购商仓库ID
	 * @return
	 */
	private PurchaseOrderEntity GetPurchaseOrder(Long purchaserId, String purchaseNo, Integer type, Long transportWarehouseId,Long orderWarehouseId) {
		List<PurchaseOrderEntity> purchaseOrder = purchaseOrderService.list(new QueryWrapper<PurchaseOrderEntity>()
			.eq("purchaser_id", purchaserId)
			.eq("purchase_no", purchaseNo)
			.eq("self_purchaser", type)
			.eq("warehouse_id", transportWarehouseId)
			.eq("order_warehouse_id", orderWarehouseId));
		if (purchaseOrder.size() > 0)
			return purchaseOrder.get(0);
		return null;
	}

	/**
	 * 获取采购单ID
	 *
	 * @param skuIds SKU ID
	 * @return
	 */
	private Map<Long, List<Long>> GetPurchaseOrderIds(List<Long> skuIds) {
		List<SkuPpEntity> list = skuPpService.list(new LambdaQueryWrapper<SkuPpEntity>()
			.in(SkuPpEntity::getSkuId, skuIds)
			.eq(SkuPpEntity::getStatus, 1)
			.select(SkuPpEntity::getPurchaserId));
		List<Long> purchaseids = list.stream().map(SkuPpEntity::getPurchaserId).distinct().toList();
		Map<Long, List<Long>> map = new HashMap<>();
		for (Long id : purchaseids) {
			map.put(id, list.stream().filter(i -> i.getPurchaserId().equals(id)).map(SkuPpEntity::getSkuId).toList());
		}
		return map;
	}
}
