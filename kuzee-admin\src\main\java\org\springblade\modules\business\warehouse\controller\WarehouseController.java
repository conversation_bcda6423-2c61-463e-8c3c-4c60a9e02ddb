/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.warehouse.controller;

import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.common.cache.DictCache;
import org.springblade.common.enums.DictEnum;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.product.pojo.entity.SkuOemEntity;
import org.springblade.modules.business.product.service.ISkuOemService;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseEntityDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseJobDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseTreeDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehousePurchaseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseCutTimeListVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseListTreeVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseListVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseVO;
import org.springblade.modules.business.warehouse.service.IWarehousePurchaseService;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springblade.modules.business.warehouse.service.impl.WarehousePurchaseServiceImpl;
import org.springblade.modules.business.warehouse.wrapper.WarehouseWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 仓库信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/warehouse")
@Tag(name = "仓库信息表", description = "仓库信息表接口")
public class WarehouseController extends BladeController {

	private final IWarehouseService warehouseService;

	private final ISkuOemService skuOemService;

	private final IWarehousePurchaseService warehousePurchase;
	/**
	 * 仓库信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入warehouse")
	public R<WarehouseVO> detail(WarehouseEntity warehouse) {
		WarehouseEntity detail = warehouseService.getOne(Condition.getQueryWrapper(warehouse));
		if (Func.isEmpty(detail)) {
			return R.fail("数据不存在");
		}
		WarehouseVO temp = WarehouseWrapper.build().entityVO(detail);
		temp.setServiceFee(CommonUtil.ConvertIntBigDecimal(detail.getServiceFee()));
		temp.setDeliveryFee(CommonUtil.ConvertIntBigDecimal(detail.getDeliveryFee()));
		temp.setWarehouseTypeName(DictCache.getValue(DictEnum.WAREHOUSE_TYPE, detail.getWarehouseType()));
		return R.data(temp);
	}

//	/**
//	 * 仓库信息表 分页
//	 */
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 2)
//	@Operation(summary = "分页", description  = "传入warehouse")
//	public R<IPage<WarehouseVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> warehouse, Query query) {
//		IPage<WarehouseEntity> pages = warehouseService.page(Condition.getPage(query), Condition.getQueryWrapper(warehouse, WarehouseEntity.class));
//		return R.data(WarehouseWrapper.build().pageVO(pages));
//	}
//
//
//	/**
//	 * 仓库信息表 自定义分页
//	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 3)
//	@Operation(summary = "分页", description  = "传入warehouse")
//	public R<IPage<WarehouseVO>> page(WarehouseVO warehouse, Query query) {
//		IPage<WarehouseVO> pages = warehouseService.selectWarehousePage(Condition.getPage(query), warehouse);
//		return R.data(pages);
//	}

//	/**
//	 * 仓库信息表 新增
//	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 4)
//	@Operation(summary = "新增", description  = "传入warehouse")
//	public R save(@Valid @RequestBody WarehouseEntity warehouse) {
//		return R.status(warehouseService.save(warehouse));
//	}

	/**
	 * 仓库信息表 修改(pc端-库存)
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改(pc端-库存)", description = "传入warehouse")
	public R update(@Valid @RequestBody WarehouseEntityDTO warehouse) {
		if (!warehouse.getParentId().equals(0L)) {
			WarehouseEntity pWarehouse = warehouseService.getById(warehouse.getParentId());
			List<Integer> startTime = Arrays.stream(pWarehouse.getBusinessStartTime().split(":"))
				.map(Integer::valueOf).toList();
			List<Integer> endTime = Arrays.stream(pWarehouse.getBusinessEndTime().split(":"))
				.map(Integer::valueOf).toList();
			LocalTime parentStartTime = LocalTime.of(startTime.get(0), startTime.get(1));
			LocalTime parentEndTime = LocalTime.of(endTime.get(0), endTime.get(1));

			// 获取子集时间
			LocalTime childEndTime = LocalTime.parse(warehouse.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
			LocalTime childStartTime = LocalTime.parse(warehouse.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
			// 验证子集时间是否在父级时间范围内
			if (childStartTime.isBefore(parentStartTime) || childEndTime.isAfter(parentEndTime)) {
				return R.fail("子集时间必须在父级时间范围内");
			}
			if (pWarehouse.getWarehouseType() >= warehouse.getWarehouseType()) {
				return R.fail("请选择正确的父节点");
			}
		}
		WarehouseEntity entity = Objects.requireNonNull(BeanUtil.copyProperties(warehouse, WarehouseEntity.class));
		entity.setServiceFee(CommonUtil.ConvertBigDecimalInt(warehouse.getServiceFee()));
		entity.setDeliveryFee(CommonUtil.ConvertBigDecimalInt(warehouse.getDeliveryFee()));
		entity.setMiniDeliveryAmount(warehouse.getMiniDeliveryAmount());
		entity.setMiniSettlementAmount(warehouse.getMiniSettlementAmount());
		return R.status(warehouseService.updateById(entity));
	}

	/**
	 * 仓库信息表 新增或修改(pc端-库存)
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改(pc端-库存)", description = "传入warehouse")
	public R submit(@Valid @RequestBody WarehouseEntityDTO warehouse) {
		if (Func.isNotEmpty(warehouse.getFormula())) {
			String FORMULA_REGEX = "^[+\\-*/][^+\\-*/](?:[^+\\-*/]*[+\\-*/](?![+\\-*/]))*[^+\\-*/]*$";
			String formula = warehouse.getFormula();
			if (!formula.matches(FORMULA_REGEX)) {
				return R.fail("公式格式不正确(如：+2*3)");
			}
		}
		boolean isCreate = Func.isEmpty(warehouse.getId());
		if (Func.isEmpty(warehouse.getId())) {
			if (Func.isEmpty(warehouse.getParentId())) {
				warehouse.setParentId(0L);
			}
		}
		if (!warehouse.getParentId().equals(0L)) {
			WarehouseEntity pWarehouse = warehouseService.getById(warehouse.getParentId());
			List<Integer> startTime = Arrays.stream(pWarehouse.getBusinessStartTime().split(":"))
				.map(Integer::valueOf).toList();
			List<Integer> endTime = Arrays.stream(pWarehouse.getBusinessEndTime().split(":"))
				.map(Integer::valueOf).toList();
			LocalTime parentStartTime = LocalTime.of(startTime.get(0), startTime.get(1));
			LocalTime parentEndTime = LocalTime.of(endTime.get(0), endTime.get(1));

			// 获取子集时间
			LocalTime childEndTime = LocalTime.parse(warehouse.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"));
			LocalTime childStartTime = LocalTime.parse(warehouse.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
			// 验证子集时间是否在父级时间范围内
			if (childStartTime.isBefore(parentStartTime) || childEndTime.isAfter(parentEndTime)) {
				return R.fail("子集时间必须在父级时间范围（" + pWarehouse.getBusinessStartTime() + "-" + pWarehouse.getBusinessEndTime() + "）内");
			}
			if (pWarehouse.getWarehouseType() >= warehouse.getWarehouseType()) {
				return R.fail("请选择正确的父节点");
			}
		}
		WarehouseEntity entity = Objects.requireNonNull(BeanUtil.copyProperties(warehouse, WarehouseEntity.class));
		entity.setServiceFee(CommonUtil.ConvertBigDecimalInt(warehouse.getServiceFee()));
		entity.setDeliveryFee(CommonUtil.ConvertBigDecimalInt(warehouse.getDeliveryFee()));
		entity.setMiniDeliveryAmount(warehouse.getMiniDeliveryAmount());
		entity.setMiniSettlementAmount(warehouse.getMiniSettlementAmount());
		warehouseService.saveOrUpdate(entity);

		WarehouseJobDTO job = new WarehouseJobDTO();
		job.setWarehouseId(entity.getId());
		warehouseService.saveJob(job);

		return R.success("操作成功");
	}

	/**
	 * 仓库信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		// 校验是否已被商品关联
		List<Long> idList = Func.toLongList(ids);
		List<SkuOemEntity> checkEntityList = skuOemService.list(new QueryWrapper<SkuOemEntity>().lambda()
			.in(SkuOemEntity::getWarehouseId, idList)
			.eq(SkuOemEntity::getIsDeleted, 0)
		);
		if (!checkEntityList.isEmpty()) {
			return R.fail("该仓库已被商品关联，无法删除");
		}
		//是否被客户关联
		List<WarehousePurchaseEntity> warehousePurchaseList=warehousePurchase.list(new QueryWrapper<WarehousePurchaseEntity>().lambda()
			.in(WarehousePurchaseEntity::getWarehouseId, idList)
			.eq(WarehousePurchaseEntity::getIsDeleted, 0));
		if (!warehousePurchaseList.isEmpty()) {
			return R.fail("该仓库已被客户关联，无法删除");
		}
		return R.status(warehouseService.removeWarehouses(Func.toLongList(ids)));
	}

//	/**
//	 * 导出数据
//	 */
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
//	@GetMapping("/export-warehouse")
//	@ApiOperationSupport(order = 8)
//	@Operation(summary = "导出数据", description  = "传入warehouse")
//	public void exportWarehouse(@Parameter(hidden = true) @RequestParam Map<String, Object> warehouse, BladeUser bladeUser, HttpServletResponse response) {
//		QueryWrapper<WarehouseEntity> queryWrapper = Condition.getQueryWrapper(warehouse, WarehouseEntity.class);
//		//if (!AuthUtil.isAdministrator()) {
//		//	queryWrapper.lambda().eq(WarehouseEntity::getTenantId, bladeUser.getTenantId());
//		//}
//		//queryWrapper.lambda().eq(WarehouseEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
//		List<WarehouseExcel> list = warehouseService.exportWarehouse(queryWrapper);
//		ExcelUtil.export(response, "仓库信息表数据" + DateUtil.time(), "仓库信息表数据表", list, WarehouseExcel.class);
//	}


	/**
	 * 仓库树形结构(pc端-库存树形结构)
	 */
	@GetMapping("/warehouseTree")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "仓库树形结构", description = "仓库树形结构")
	public R<List<TreeNode>> warehouseTree(WarehouseTreeDTO dto) {
		List<TreeNode> tree = warehouseService.warehouseTree(dto);
		return R.data(tree);
	}

	/**
	 * 仓库树形结构(带权限的仓库查询)
	 */
	@GetMapping("/permission/warehouseTree")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "仓库树形结构(区分权限)", description = "仓库树形结构(区分权限)")
	public R<List<TreeNode>> permissionWarehouseTree() {
		return R.data(warehouseService.permissionWarehouseTree());
	}

	/**
	 * 仓库树形结构(pc端-库存)
	 */
	@GetMapping("/warehouseListTree")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "仓库树形结构(pc端-库存)", description = "仓库树形结构")
	public R<List<WarehouseListTreeVO>> warehouseListTree(WarehouseTreeDTO dto) {
		List<WarehouseListTreeVO> tree = warehouseService.warehouseListTree(dto);
		return R.data(tree);
	}

	/**
	 * 仓库list
	 */
	@GetMapping("/warehouseList")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "仓库list", description = "仓库list")
	public R<List<WarehouseListVO>> warehouseList(WarehouseTreeDTO dto) {
		List<WarehouseListVO> tree = warehouseService.warehouseList(dto);
		return R.data(tree);
	}

	/**
	 * 导入仓库数据
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "导入仓库数据", description = "导入Excel文件")
	public R importWarehouse(@RequestParam("file") MultipartFile file) {
		warehouseService.importWarehouse(file);
		return R.success("导入成功");
	}


	/**
	 * 仓库cut时间分类list
	 */
	@GetMapping("/WarehouseCutTimeList")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "仓库cut时间分类list", description = "仓库cut时间分类list")
	public R<List<WarehouseCutTimeListVO>> getWarehouseCutTimeList() {
		List<WarehouseCutTimeListVO> tree = warehouseService.getWarehouseCutTimeList();
		return R.data(tree);
	}

}
