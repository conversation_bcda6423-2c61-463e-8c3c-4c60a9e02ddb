package org.springblade.common.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
/**
* <AUTHOR>
* @date 2022/11/23 10:05
* @desc 仓库出库-商品
*/
public class PrintTransferOrderProductVO {

	//品名
	@Schema(description = "品名")
	private String productName;
	// 单位
	@Schema(description = "单位")
	private String baseUnit;
	// 计量方式
	@Schema(description = "计量方式")
	private String measureMode;
	// 档口
	@Schema(description = "档口")
	private String stall;
	// 数量
	@Schema(description = "数量")
	private Integer quantity;
	// 出库数量
	@Schema(description = "出库数量")
	private Integer outboundQuantity;
	// 出库重量
	@Schema(description = "出库重量")
	private BigDecimal outboundWeight;
	// 档口收货差异
	@Schema(description = "档口收货差异")
	private BigDecimal storeDifference;
	// 备注
	@Schema(description = "备注")
	private String remark;
	// 商品种类
	@Schema(description = "商品种类")
	private String productType;
}
