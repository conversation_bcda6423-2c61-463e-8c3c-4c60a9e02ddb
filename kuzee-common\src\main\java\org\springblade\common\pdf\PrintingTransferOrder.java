package org.springblade.common.pdf;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import org.springblade.common.pojo.vo.PrintSupplyOrderVO;
import org.springblade.common.pojo.vo.PrintTransferOrderProductVO;
import org.springblade.common.pojo.vo.PrintTransferOrderTransportVO;
import org.springblade.common.pojo.vo.PrintTransferOrderWarehouseVO;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 打印转运单
 */
public class PrintingTransferOrder {


	/**
	 * 仓库出库单
	 *
	 * @param stalls        数据集合
	 * @param warehouseName 仓库名称
	 * @param dateTime      时间
	 * @return
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream PrintTransferOrderWarehouse(List<PrintTransferOrderWarehouseVO> stalls,
																	List<String> warehouseName, String zc,String dateTime) throws DocumentException, IOException {
		Document document = new Document(PDFServerUtil.rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);
		// 2. 添加页码事件处理器
		writer.setPageEvent(new PDFPageUtil.PageNumberFooter());
		// 打开文档
		document.open();
		for (int i = 0; warehouseName.size() > i; i++) {
			PDFPageUtil.PageNumberFooter.currentPage = 1;

			bodyWarehouse(document, stalls, warehouseName.get(i),zc, dateTime, writer);
			// 6. 再换新页添加额外内容
			document.newPage();
		}
		document.close();
		return outputStream;
	}


	/**
	 * 分配搬运
	 *
	 * @param stalls
	 * @param warehouseName
	 * @param dateTime
	 * @return
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static ByteArrayOutputStream PrintTransferOrderTransport(List<PrintTransferOrderTransportVO> stalls,
																	String warehouseName, String dateTime) throws DocumentException, IOException {
		Document document = new Document(PDFServerUtil.rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);
		// 2. 添加页码事件处理器
		writer.setPageEvent(new PDFPageUtil.PageNumberFooter());
		// 打开文档
		document.open();

		PDFPageUtil.PageNumberFooter.currentPage = 1;
		bodyTransport(document, stalls, warehouseName, dateTime, writer);
		// 6. 再换新页添加额外内容
		document.newPage();

		document.close();
		return outputStream;
	}


	public static ByteArrayOutputStream PrintTransferOrderProduct(List<PrintTransferOrderProductVO> stalls,
																  String warehouseName, String dateTime) throws DocumentException, IOException {
		Document document = new Document(PDFServerUtil.rectangle);
		// 使用ByteArrayOutputStream代替FileOutputStream
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		// 获取PDF写入器
		PdfWriter writer = PdfWriter.getInstance(document, outputStream);
		// 2. 添加页码事件处理器
		writer.setPageEvent(new PDFPageUtil.PageNumberFooter());
		// 打开文档
		document.open();
		for (int i = 0; stalls.size() > i; i++) {
			PDFPageUtil.PageNumberFooter.currentPage = 1;
			bodyProduct(document, stalls, warehouseName, dateTime, writer);
			// 6. 再换新页添加额外内容
			document.newPage();
		}
		document.close();
		return outputStream;
	}

	/**
	 * 仓库出库单-bogy
	 *
	 * @param document
	 * @param stalls
	 * @param warehouseName
	 * @param dateTime
	 * @param writer
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static void bodyWarehouse(Document document, List<PrintTransferOrderWarehouseVO> stalls, String warehouseName, String zc, String dateTime, PdfWriter writer) throws DocumentException, IOException {

		// Load Chinese font
		String resource = new ClassPathResource(PDFServerUtil.FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 6);
		//表头
		Font headerFont = new Font(baseFont, 6, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 6);

		String title = zc + "出库单（" + warehouseName + "）";

		String dateStr = "日期：  " + dateTime;
		PdfPTable mainTable = new PdfPTable(PDFServerUtil.HEADER_COLUMN_WIDTHS7.length);
		mainTable.setWidths(PDFServerUtil.HEADER_COLUMN_WIDTHS7);
		mainTable.setWidthPercentage(100);
		mainTable.setSpacingAfter(5f);
		PDFServerUtil.addTitleAndDate(mainTable, titleFont, titleRightFont, title, dateStr, 7);
		String headerStr = "序号,品名,商品种类,单位,计量方式,数量,出库数量,出库重量,收货重量,备注";
		PDFServerUtil.addMainHeaderCells(mainTable, headerFont, headerStr);
		mainTable.setHeaderRows(2);

		PDFServerUtil.addDataRowsWarehouse(mainTable, stalls, chineseFont);

		document.add(mainTable);
	}

	/**
	 * 仓库出库单-搬运bogy
	 *
	 * @param document
	 * @param stalls
	 * @param warehouseName
	 * @param dateTime
	 * @param writer
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static void bodyTransport(Document document, List<PrintTransferOrderTransportVO> stalls, String warehouseName, String dateTime, PdfWriter writer) throws DocumentException, IOException {
		// Load Chinese font
		String resource = new ClassPathResource(PDFServerUtil.FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 6);
		//表头
		Font headerFont = new Font(baseFont, 6, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 6);
		String title = warehouseName + "搬运单";
		String dateStr = "日期：  " + dateTime;
		PdfPTable mainTable = new PdfPTable(PDFServerUtil.HEADER_COLUMN_WIDTHS9.length);
		mainTable.setWidths(PDFServerUtil.HEADER_COLUMN_WIDTHS9);
		mainTable.setWidthPercentage(100);
		mainTable.setSpacingAfter(5f);
		PDFServerUtil.addTitleAndDate(mainTable, titleFont, titleRightFont, title, dateStr, 9);
		String headerStr = "序号,供应商,详细地址,品名,商品种类,单位,计量方式,合计,拉货数量,拉货重量,仓库收货,备注";
		PDFServerUtil.addMainHeaderCells(mainTable, headerFont, headerStr);
		mainTable.setHeaderRows(2);

		PDFServerUtil.addDataRowsTransport(mainTable, stalls, chineseFont);

		document.add(mainTable);

	}

	/**
	 * 仓库出库单-商品bogy
	 *
	 * @param document
	 * @param stalls
	 * @param warehouseName
	 * @param dateTime
	 * @param writer
	 * @throws DocumentException
	 * @throws IOException
	 */
	public static void bodyProduct(Document document, List<PrintTransferOrderProductVO> stalls, String warehouseName, String dateTime, PdfWriter writer) throws DocumentException, IOException {
// Load Chinese font
		String resource = new ClassPathResource(PDFServerUtil.FONT_PATH).getURL().toString();
		BaseFont baseFont = BaseFont.createFont(resource, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
		//正文
		Font chineseFont = new Font(baseFont, 6);
		//表头
		Font headerFont = new Font(baseFont, 6, Font.BOLD);
		//头部表中间
		Font titleFont = new Font(baseFont, 14, Font.BOLD);
		//头部表右边
		Font titleRightFont = new Font(baseFont, 8);
		//表尾
		Font footFont = new Font(baseFont, 6);
		String title = warehouseName + "分拣单";
		String dateStr = "日期：  " + dateTime;
		PdfPTable mainTable = new PdfPTable(PDFServerUtil.HEADER_COLUMN_WIDTHS8.length);
		mainTable.setWidths(PDFServerUtil.HEADER_COLUMN_WIDTHS8);
		mainTable.setWidthPercentage(100);
		mainTable.setSpacingAfter(5f);
		PDFServerUtil.addTitleAndDate(mainTable, titleFont, titleRightFont, title, dateStr, 8);
		String headerStr = "序号,品名,商品种类,单位,计量方式,档口,数量,出库数量,出库重量,档口收货差异,备注";
		PDFServerUtil.addMainHeaderCells(mainTable, headerFont, headerStr);
		mainTable.setHeaderRows(2);

		PDFServerUtil.addDataRowsProduct(mainTable, stalls, chineseFont);

		document.add(mainTable);
	}
}
