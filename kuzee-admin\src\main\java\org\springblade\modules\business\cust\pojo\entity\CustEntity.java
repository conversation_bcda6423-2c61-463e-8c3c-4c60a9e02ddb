/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 客户表 实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@TableName("chy_cust")
@Schema(description = "CustEntity对象")
@EqualsAndHashCode(callSuper = true)
public class CustEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 父级客户id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "父级客户id")
	private Long parentId;
	/**
	 * 系统用户id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "系统用户id")
	private Long bladeUserId;
	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private String custName;
	/**
	 * 所属分区
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "所属分区")
	private Long warehouseId;
	/**
	 * 授信额度
	 */
	@Schema(description = "授信额度")
	private Integer creditLimit;
	/**
	 * 授信额度还款周期
	 */
	@Schema(description = "授信额度还款周期")
	private Integer creditCycle;
	/**
	 * 授信开始时间;
	/**
	 * 授信截止时间
	 */
	@Schema(description = "授信截止时间")
	private LocalDateTime creditEndTime;
	/**
	 * 手机号
	 */
	@Schema(description = "手机号")
	private String phone;
	/**
	 * 密码
	 */
	@Schema(description = "密码")
	private String password;
	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String nickname;
	/**
	 * 头像
	 */
	@Schema(description = "头像")
	private String icon;
	/**
	 * 性别
	 */
	@Schema(description = "性别")
	private Integer gender;
	/**
	 * 客户来源
	 */
	@Schema(description = "客户来源")
	private Integer sourceType;
	/**
	 * 客户角色id
	 */
	@Schema(description = "客户角色id")
	private Long custRoleId;
	/**
	 * 微信unionid
	 */
	@Schema(description = "微信unionid")
	private String unionid;

	/**
	 * 业务员id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "业务员id")
	private Long userId;

	/**
	 * 编号
	 */
	@Schema(description = "编号")
	private String code;

	/**
	 * 可用授信额度
	 */
	@Schema(description = "可用授信额度")
	private Integer useCredit;

	/**
	 * 冻结额度
	 */
	@Schema(description = "冻结额度")
	private Integer freezeCredit;



	/**
	 *省份/直辖市
	 */
	@Schema(description = "省份/直辖市")
	private String province;
	/**
	 *城市
	 */
	@Schema(description = "城市")
	private String city;
	/**
	 *区
	 */
	@Schema(description = "区")
	private String region;

	/**
	 *区域名称
	 */
	@Schema(description = "区域名称")
	private String regionName;

	/**
	 *联系人
	 */
	@Schema(description = "联系人")
	private String contacts;

	/**
	 *详细地址
	 */
	@Schema(description = "详细地址")
	private String address;

	/**
	 *统一社会信用代码
	 */
	@Schema(description = "统一社会信用代码")
	private String socialCreditCode;

	/**
	 *营业执照
	 */
	@Schema(description = "营业执照")
	private String license;

	/**
	 *备注
	 */
	@Schema(description = "备注")
	private String note;

	@Schema(description = "客户类型(客户类型 0-正常客户(默认)、1-散客)")
	private Integer custType;
}
