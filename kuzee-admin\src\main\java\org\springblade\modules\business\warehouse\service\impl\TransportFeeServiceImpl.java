/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.product.pojo.entity.SkuForwardingChargesEntity;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.service.ISkuForwardingChargesService;
import org.springblade.modules.business.product.service.ISkuStockService;
import org.springblade.modules.business.warehouse.excel.TransportFeeExcel;
import org.springblade.modules.business.warehouse.mapper.TransportFeeMapper;
import org.springblade.modules.business.warehouse.pojo.entity.TransportFeeEntity;
import org.springblade.modules.business.warehouse.pojo.entity.TransportFeeItemEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseStoreItemEntity;
import org.springblade.modules.business.warehouse.pojo.vo.TransportFeeByNoVO;
import org.springblade.modules.business.warehouse.pojo.vo.TransportFeeItemByNoVO;
import org.springblade.modules.business.warehouse.pojo.vo.TransportFeeVO;
import org.springblade.modules.business.warehouse.service.ITransportFeeItemService;
import org.springblade.modules.business.warehouse.service.ITransportFeeService;
import org.springblade.modules.business.warehouse.utils.WarehouseHelper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 转运费流水表(供应商配送产生) 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Service
@AllArgsConstructor
@Slf4j
public class TransportFeeServiceImpl extends BaseServiceImpl<TransportFeeMapper, TransportFeeEntity> implements ITransportFeeService {

	private ISkuForwardingChargesService skuForwardingChargesService;
	private ISkuStockService skuStockService;
	private ITransportFeeItemService transportFeeItemService;
	private IProductService productService;

	@Override
	public IPage<TransportFeeVO> selectTransportFeePage(IPage<TransportFeeVO> page, TransportFeeVO transportFee) {
		return page.setRecords(baseMapper.selectTransportFeePage(page, transportFee));
	}

	/**
	 * 根据备货单号查询信息
	 *
	 * @param no 备货单号
	 * @return TransportFeeByNoVO
	 */
	@Override
	public TransportFeeByNoVO getListByNo(String no) {
		try {
			// 参数校验
			if (Func.isEmpty(no)) {
				log.warn("备货单号为空");
				return createEmptyTransportFeeByNoVO(no);
			}

			// 初始化返回对象
			TransportFeeByNoVO transportFeeByNoVO = createEmptyTransportFeeByNoVO(no);

			// 查询转运费信息
			List<TransportFeeEntity> transportFeeList = getTransportFeeList(no);
			if (Func.isEmpty(transportFeeList)) {
				log.info("未找到备货单号: {} 的转运费信息", no);
				return transportFeeByNoVO;
			}

			// 查询转运费明细
			List<TransportFeeItemByNoVO> items = getTransportFeeItems(transportFeeList);
			if (Func.isEmpty(items)) {
				log.info("未找到备货单号: {} 的转运费明细信息", no);
				return transportFeeByNoVO;
			}

			// 设置返回结果
			transportFeeByNoVO.setItems(items);
			transportFeeByNoVO.setTotalAmount(calculateTotalAmount(items));
			transportFeeByNoVO.setTotalWeight(calculateTotalWeight(items));

			return transportFeeByNoVO;
		} catch (Exception e) {
			log.error("查询备货单号: {} 的转运费信息失败", no, e);
			throw new ServiceException("查询转运费信息失败");
		}
	}

	/**
	 * 创建空的转运费返回对象
	 */
	private TransportFeeByNoVO createEmptyTransportFeeByNoVO(String no) {
		TransportFeeByNoVO vo = new TransportFeeByNoVO();
		vo.setReplenishmentNo(no);
		vo.setTotalAmount(BigDecimal.ZERO);
		vo.setTotalWeight(BigDecimal.ZERO);
		vo.setItems(new ArrayList<>());
		return vo;
	}

	/**
	 * 查询转运费信息
	 */
	private List<TransportFeeEntity> getTransportFeeList(String no) {
		return this.list(
			Wrappers.<TransportFeeEntity>lambdaQuery()
				.eq(TransportFeeEntity::getReplenishmentNo, no)
				.eq(TransportFeeEntity::getIsDeleted, 0)
		);
	}

	/**
	 * 查询转运费明细
	 */
	private List<TransportFeeItemByNoVO> getTransportFeeItems(List<TransportFeeEntity> transportFeeList) {
		List<TransportFeeItemByNoVO> items = new ArrayList<>();

		// 获取转运费ID列表
		List<Long> transportFeeIds = transportFeeList.stream()
			.map(TransportFeeEntity::getId)
			.collect(Collectors.toList());

		// 查询转运费明细
		List<TransportFeeItemEntity> itemList = transportFeeItemService.list(
			Wrappers.<TransportFeeItemEntity>lambdaQuery()
				.in(TransportFeeItemEntity::getTransportFeeId, transportFeeIds)
				.eq(TransportFeeItemEntity::getIsDeleted, 0)
				.orderByDesc(TransportFeeItemEntity::getId)
		);

		// 转换为VO对象
		for (TransportFeeItemEntity item : itemList) {
			try {
				TransportFeeItemByNoVO vo = convertToItemVO(item);
				vo.setQuantity(item.getQuantity());
				vo.setFeeAmount(CommonUtil.ConvertIntBigDecimal(item.getAmount()));
				vo.setFeePrice(CommonUtil.ConvertIntBigDecimal(item.getPrice()));
				vo.setPriceType(item.getPriceType());
				vo.setPriceTypeStr(item.getPriceType() == 0 ? "基本单位单价" : "包装单位单价");
				vo.setFeePriceStr(item.getPriceType() == 0 ? "每件" + vo.getFeePrice() + "元" : "每斤" + vo.getFeePrice() + "元");
				items.add(vo);
			} catch (Exception e) {
				log.error("转换转运费明细失败, itemId: {}", item.getId(), e);
			}
		}

		return items;
	}

	/**
	 * 转换为明细VO对象
	 */
	private TransportFeeItemByNoVO convertToItemVO(TransportFeeItemEntity item) {
		if (item == null) {
			return null;
		}

		TransportFeeItemByNoVO vo = new TransportFeeItemByNoVO();
		vo.setAmount(CommonUtil.ConvertIntBigDecimal(item.getAmount()));
		vo.setWeight(CommonUtil.WeightToBigDecimal(item.getWeight()));
		vo.setSpData(item.getSpData());

		// 获取商品信息
		try {
			var product = productService.getById(item.getProductId());
			if (product != null) {
				vo.setProductName(product.getName());
			} else {
				log.warn("未找到商品信息, productId: {}", item.getProductId());
				vo.setProductName("");
			}
		} catch (Exception e) {
			log.error("获取商品信息失败, productId: {}", item.getProductId(), e);
			vo.setProductName("");
		}

		return vo;
	}

	/**
	 * 计算总金额
	 */
	private BigDecimal calculateTotalAmount(List<TransportFeeItemByNoVO> items) {
		return items.stream()
			.map(TransportFeeItemByNoVO::getAmount)
			.filter(Objects::nonNull)
			.reduce(BigDecimal::add)
			.orElse(BigDecimal.ZERO);
	}

	/**
	 * 计算总重量
	 */
	private BigDecimal calculateTotalWeight(List<TransportFeeItemByNoVO> items) {
		return items.stream()
			.map(TransportFeeItemByNoVO::getWeight)
			.filter(Objects::nonNull)
			.reduce(BigDecimal::add)
			.orElse(BigDecimal.ZERO);
	}

	//	/**
//	 * 根据备货单号自定义分页
//	 *
//	 * @param page 分页参数
//	 * @param stockOrderNumber 查询参数
//	 * @return IPage<TransportFeeVO>
//	 */
//	@Override
//	public IPage<TransportFeeVO>  selectPageByStoreNo(IPage<TransportFeeVO> page, String stockOrderNumber){
//		return page.setRecords(baseMapper.selectPageByStoreNo(page, stockOrderNumber));
//	}
	@Override
	public List<TransportFeeExcel> exportTransportFee(Wrapper<TransportFeeEntity> queryWrapper) {
		List<TransportFeeExcel> transportFeeList = baseMapper.exportTransportFee(queryWrapper);
		//transportFeeList.forEach(transportFee -> {
		//	transportFee.setTypeName(DictCache.getValue(DictEnum.YES_NO, TransportFeeEntity.getType()));
		//});
		return transportFeeList;
	}

	@Override
	public void generateAmount(String replenishmentNo, List<WarehouseStoreItemEntity> itemEntityList) {
		List<Long> skuIds = itemEntityList.stream().map(WarehouseStoreItemEntity::getProductSkuId).toList();
		Map<Long, SkuForwardingChargesEntity> skuMap = skuForwardingChargesService.listBySkuIds(skuIds);
		// sku没有配置转运费
		if (Func.isEmpty(skuMap)) {
			log.info("[{}]根据备货单计算转运费，未查询到sku配置转运费", replenishmentNo);
			return;
		}

		List<TransportFeeItemEntity> itemList = Lists.newArrayList();
		for (WarehouseStoreItemEntity item : itemEntityList) {
			SkuForwardingChargesEntity charges = skuMap.get(item.getProductSkuId());
			// sku未配置服务费
			if (Func.isNull(charges)) {
				log.info("[{}]根据备货单计算转运费，未查询到sku[{}]配置转运费", replenishmentNo, item.getProductSkuId());
				continue;
			}

			log.info("[{}]根据备货单计算转运费，查询到sku[{}]配置转运费，转运费配置类型:{},单价(分):{}", replenishmentNo, item.getProductSkuId(), charges.getPriceType(), charges.getPrice());
			// 构建转运费明细
			itemList.add(getTransportFeeItemEntity(item, charges));
		}

		if (Func.isEmpty(itemList)) {
			return;
		}


		// 生成转运费
		TransportFeeEntity feeEntity = WarehouseHelper.transportFee(itemList, replenishmentNo);
		feeEntity.setWarehouseId(itemEntityList.get(0).getWarehouseId());
		log.info("[{}]根据备货单计算转运费,生成总费用(分):{}", replenishmentNo, feeEntity.getAmount());
		Assert.isTrue(save(feeEntity), () -> new ServiceException("生成转运费流水失败"));

		// 生成转运费明细
		itemList.forEach(item -> item.setTransportFeeId(feeEntity.getId()));
		transportFeeItemService.saveBatch(itemList);
	}

	/**
	 * 构建转运费明细
	 *
	 * @param item    入库明细
	 * @param charges sku转运费配置
	 */
	private TransportFeeItemEntity getTransportFeeItemEntity(WarehouseStoreItemEntity item, SkuForwardingChargesEntity charges) {
		TransportFeeItemEntity feeItem = new TransportFeeItemEntity();
		feeItem.setStoreItemId(item.getId());
		feeItem.setProductSkuId(item.getProductSkuId());
		feeItem.setProductId(item.getProductId());
		feeItem.setSpData(item.getSpData());
		feeItem.setPrice(charges.getPrice());
		feeItem.setPriceType(charges.getPriceType());
		feeItem.setWeight(item.getActualWeight());
		feeItem.setQuantity(item.getActualQuantity());

		BigDecimal price = BigDecimal.valueOf(charges.getPrice());

		// 配置按斤价
		if (charges.getPriceType() == 1) {
			feeItem.setAmount(price.multiply(item.getActualWeight()).setScale(0, RoundingMode.HALF_UP).intValueExact());
			log.info("根据备货单sku[{}]计算转运费，按斤计算转运费,入库重量(斤):{},入库单价(分):{},总费用(分):{}", item.getProductSkuId(), item.getActualWeight(), price, feeItem.getAmount());
		} else {
			// 配置为按件价
			feeItem.setAmount(price.multiply(BigDecimal.valueOf(item.getActualQuantity())).setScale(0, RoundingMode.HALF_UP).intValueExact());
			log.info("根据备货单sku[{}]计算转运费，按件计算转运费,入库数量:{},入库单价(分):{},总费用(分):{}", item.getProductSkuId(), item.getActualQuantity(), price, feeItem.getAmount());
		}
		return feeItem;
	}

	/**
	 * 生成运费
	 *
	 * @param priceType 基础价格类型（0-基本单位单价，1-包装单位单价
	 * @param price     价格(分)
	 * @param quantity  入库数量
	 * @param weight    入库重量
	 * @return 运费
	 */
	private BigDecimal generateAmount(Integer priceType, Integer price, Integer quantity, BigDecimal weight) {
		// 基本单位
		if (priceType == 0 && Func.notNull(weight)) {
			return BigDecimal.valueOf(price).multiply(weight);
		}
		// 包装单位
		if (priceType == 1 && Func.notNull(quantity)) {
			return BigDecimal.valueOf(price).multiply(BigDecimal.valueOf(quantity));
		}
		return BigDecimal.ZERO;
	}
}
