<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.order.mapper.ReplenishmentOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="replenishmentOrderResultMap" type="org.springblade.modules.business.order.pojo.entity.ReplenishmentOrderEntity">
        <result column="supplier_id" property="supplierId"/>
        <result column="purchase_id" property="purchaseId"/>
        <result column="unit_quantity" property="unitQuantity"/>
        <result column="unit_weight" property="unitWeight"/>
        <result column="unit_actualweight" property="unitActualweight"/>
        <result column="total_price" property="totalPrice"/>
        <result column="create_time_at" property="createTimeAt"/>
    </resultMap>

    <select id="selectReplenishmentOrderPage" resultMap="replenishmentOrderResultMap">
        select * from chy_replenishment_order where is_deleted = 0
    </select>

    <select id="exportReplenishmentOrder" resultType="org.springblade.modules.business.order.excel.ReplenishmentOrderExcel">
        SELECT * FROM chy_replenishment_order ${ew.customSqlSegment}
    </select>

    <select id="replenishmentList"   resultType="org.springblade.modules.business.order.pojo.vo.ReplenishmentListVO">
        SELECT o.*,s.full_name as supplierName,s.phone as supplierPhone, w.replenishment_order_id,w.product_sku_id,
        w.sp_data ,w.quantity as unitWeight,pr.`name` as product_name,o.supply_way,s.address, w.note as noteDetail
        FROM chy_replenishment_order o
        LEFT JOIN chy_supplier s ON s.id = o.supplier_id
        Left join chy_replenishment_order_item w on w.replenishment_order_id = o.id
        left join chy_sku_stock sku on w.product_sku_id = sku.id
        left join chy_product pr on sku.product_id=pr.id
        WHERE o.is_deleted = 0 and o.warehouse_id = #{dto.warehouseId}
        <if test="dto.supplierName!= null and dto.supplierName!= ''">
            AND  s.full_name LIKE CONCAT('%',#{dto.supplierName},'%')
        </if>
        <if test="dto.warehouseNumber!= null and dto.warehouseNumber!= ''">
            AND s.address LIKE CONCAT('%',#{dto.warehouseNumber},'%')
        </if>
        <if test="dto.replenishmentNo!= null and dto.replenishmentNo!= ''">
            AND o.replenishment_no LIKE CONCAT('%',#{dto.replenishmentNo},'%')
        </if>
        order by o.create_time_at desc
    </select>

    <select id="replenishmentDetail"   resultType="org.springblade.modules.business.order.pojo.vo.ReplenishmentDetailVO">
        SELECT o.*,s.name as supplierName,s.phone as supplierPhone,s.address as supplierAddress,w.warehouse_name as warehouseName
        FROM chy_replenishment_order o
        LEFT JOIN chy_supplier s ON s.id = o.supplier_id
        Left join chy_warehouse w on o.warehouse_id = w.id
        WHERE o.is_deleted = 0 and
        o.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by o.create_time_at desc

    </select>

    <select id="listByPurchaseOrderAndSku"
            resultType="org.springblade.modules.business.order.pojo.vo.ReplenishmentOrderAllocationVO">
        SELECT o.supplier_id,s.full_name AS supplier_name,s.phone,i.quantity,i.support_trans_unit_id,i.support_trans_num
        FROM chy_replenishment_order o
                 INNER JOIN chy_replenishment_order_item i ON i.replenishment_order_id = o.id AND i.is_deleted = 0
                 INNER JOIN chy_supplier s ON s.id = o.supplier_id
        WHERE o.is_deleted = 0 AND i.purchase_order_id = #{purchaseOrderId} AND i.product_sku_id = #{skuId}
    </select>

    <select id="listSupplierReplenishment"
            resultType="org.springblade.modules.business.order.pojo.vo.SupplierReplenishmentOrderVO">
        SELECT o.*,s.full_name as supplier_name,s.phone as supplier_phone,w.warehouse_name
        FROM chy_replenishment_order o
        LEFT JOIN chy_supplier s ON s.id = o.supplier_id AND s.is_deleted = 0
        LEFT JOIN chy_warehouse w ON o.warehouse_id = w.id AND w.is_deleted = 0
        WHERE o.is_deleted = 0 AND o.purchase_order_id = #{orderId}
    </select>

    <select id="listBySupplier"
            resultType="org.springblade.modules.business.order.pojo.dto.ReplenishmentListSupplierPageDTO">
        SELECT o.id,o.replenishment_no,o.warehouse_id,w.warehouse_name,p.purchaser_id,o.update_time,o.`status`,o.note
        FROM chy_replenishment_order o
        INNER JOIN chy_purchase_order p ON p.id = o.purchase_order_id AND p.is_deleted = 0
        INNER JOIN chy_warehouse w ON w.id = o.warehouse_id AND w.is_deleted = 0
        WHERE o.is_deleted = 0 AND o.supplier_id = #{supplierId}
        <if test="dto.status != null">
            AND o.`status` = #{dto.status}
        </if>
        <if test="dto.warehouseId != null">
            AND o.warehouse_id = #{dto.warehouseId}
        </if>
        <if test="dto.purchaserId != null">
            AND p.purchaser_id = #{dto.purchaserId}
        </if>
    </select>

    <select id="listByTime"
            resultType="org.springblade.modules.business.order.pojo.vo.ReplenishmentOrderItemByTimeVO">
        SELECT
            o.id,
            o.replenishment_no,
            o.supplier_id,
            i.id AS replenishment_item_id,
            i.product_id,
            i.product_sku_id,
            i.sku_code,
            i.sp_data,
            i.product_name,
            i.quantity,
            i.unit_price,
            i.whole_price,
            i.support_trans_unit_id,
            i.support_trans_num,
            i.purchase_order_item_id,
            s.package_gross_conversion_rate
        FROM
            chy_replenishment_order o
                INNER JOIN chy_replenishment_order_item i ON i.replenishment_order_id = o.id AND i.is_deleted = 0
                INNER JOIN chy_sku_stock s ON s.id = i.product_sku_id AND s.is_deleted = 0
            WHERE o.warehouse_id = #{warehouseId}
                AND o.create_time_at BETWEEN #{startTime} AND #{endTime}
                AND o.is_deleted = 0
                AND (o.status = 0 OR o.status = 1)
                AND i.store_status = 0
        ORDER BY o.supplier_id
    </select>

    <select id="getPrintTransferOrderTransport"   resultType="org.springblade.common.pojo.vo.PrintTransferOrderTransportVO">
        SELECT su.`name` as supplierName,su.address ,r.product_name,un.unit_name as baseUnit ,(CASE  WHEN p.is_standard=0 THEN "标品" ELSE "非标品" END  ) as measureMode
        ,SUM(r.quantity) as total,cao.`name` as productType  FROM chy_replenishment_order t
        left join chy_replenishment_order_item r on t.id = r.replenishment_order_id
        left join chy_purchase_order o on r.purchase_order_id=o.id
        LEFT JOIN chy_sku_stock s on r.product_sku_id=s.id
        LEFT JOIN chy_base_unit un on un.id=s.base_unit_id
        LEFT JOIN chy_product p on s.product_id =p.id
        LEFT JOIN chy_product_category ca on p.product_category_id=ca.id
        LEFT JOIN chy_product_category cao on cao.id =ca.parent_id
        LEFT JOIN chy_supplier su on su.id=t.supplier_id
        where t.is_deleted=0 and o.is_deleted=0 and r.is_deleted=0
        and DATE(t.create_time)=DATE(#{dto.transferDate})
        and cao.id=#{dto.productCategory}
        and o.order_warehouse_id in
        <foreach collection="dto.warehouseIds" item="warehouseId" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>
        GROUP BY su.`name`,su.address,r.product_name,un.unit_name,p.is_standard,cao.`name`

        ORDER BY r.product_name
    </select>
    <select id="getPrintTransferOrderProduct"
            resultType="org.springblade.common.pojo.vo.PrintTransferOrderProductVO">

        SELECT  r.product_name,un.unit_name as baseUnit ,(CASE  WHEN p.is_standard=0 THEN "标品" ELSE "非标品" END  ) as measureMode
        ,SUM(r.quantity) as quantity,cao.`name` as productType ,w.warehouse_name as stall FROM chy_transport_order t
        left join chy_transport_order_item r on t.id = r.transport_order_id

        LEFT JOIN chy_sku_stock s on r.product_sku_id=s.id
        LEFT JOIN chy_base_unit un on un.id=s.base_unit_id
        LEFT JOIN chy_product p on s.product_id =p.id
        LEFT JOIN chy_product_category ca on p.product_category_id=ca.id
        LEFT JOIN chy_product_category cao on cao.id =ca.parent_id
        LEFT JOIN chy_warehouse w on w.id =t.transport_warehouse_id

        where t.is_deleted=0 and r.is_deleted=0
        and DATE(t.create_time)=DATE(#{dto.transferDate})
        and cao.id=#{dto.productCategory}
        and t.transport_warehouse_id in
        <foreach collection="dto.warehouseIds" item="warehouseId" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>

        GROUP BY  r.product_name,un.unit_name,p.is_standard,cao.`name`,w.warehouse_name

        ORDER BY r.product_name
    </select>
    <select id="getPrintTransferOrderWarehouse"
            resultType="org.springblade.common.pojo.vo.PrintTransferOrderWarehouseVO">
        SELECT  r.product_name,un.unit_name as baseUnit ,(CASE  WHEN p.is_standard=0 THEN "标品" ELSE "非标品" END  ) as measureMode
        ,SUM(r.quantity) as quantity,cao.`name` as productType ,w.warehouse_name as stall,oui.quantity as outboundQuantity,oui.weight as outboundWeight  FROM chy_transport_order t
        left join chy_transport_order_item r on t.id = r.transport_order_id

        LEFT JOIN chy_sku_stock s on r.product_sku_id=s.id
        LEFT JOIN chy_base_unit un on un.id=s.base_unit_id
        LEFT JOIN chy_product p on s.product_id =p.id
        LEFT JOIN chy_product_category ca on p.product_category_id=ca.id
        LEFT JOIN chy_product_category cao on cao.id =ca.parent_id
        LEFT JOIN chy_warehouse w on w.id =t.transport_warehouse_id
        LEFT JOIN chy_warehouse_outbound ou on ou.related_order_id=t.id and ou.biz_type=4
        left JOIN chy_warehouse_outbound_item oui on oui.warehouse_outbound_id=ou.id and oui.product_sku_id=r.product_sku_id

        where t.is_deleted=0 and r.is_deleted=0 and oui.is_deleted=0 and ou.is_deleted=0
        and DATE(t.create_time)=DATE(#{dto.transferDate})
        and cao.id=#{dto.productCategory}
        and t.transport_warehouse_id in
        <foreach collection="dto.warehouseIds" item="warehouseId" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>

        GROUP BY  r.product_name,un.unit_name,p.is_standard,cao.`name`,w.warehouse_name

        ORDER BY r.product_name
    </select>

</mapper>
