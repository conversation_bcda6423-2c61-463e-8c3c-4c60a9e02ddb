package org.springblade.modules.business.order.pojo.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "供应商商品实时对账分页查询VO")
public class SupplierProductReconciliationPageVO {

    @Schema(description = "分页数据")
    private IPage<SupplierProductReconciliationVO> page;

    @Schema(description = "总计")
    private RealtimeReconciliationVO summary;
} 