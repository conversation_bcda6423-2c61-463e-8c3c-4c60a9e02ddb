/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.cache.ProductPutCache;
import org.springblade.common.constant.CacheConstant;
import org.springblade.common.enums.*;
import org.springblade.common.utills.CommonUtil;
import org.springblade.common.utills.GenerateNumberUtil;
import org.springblade.common.utills.PriceCalculator;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.*;
import org.springblade.message.enums.MessageTypeEnum;
import org.springblade.modules.business.cust.service.impl.MessageSenderService;
import org.springblade.modules.business.order.event.BusinessEvent;
import org.springblade.modules.business.order.mapper.SaleOrderMapper;
import org.springblade.modules.business.order.pojo.entity.SaleOrderDiffEntity;
import org.springblade.modules.business.order.pojo.entity.SaleOrderEntity;
import org.springblade.modules.business.order.pojo.entity.SaleOrderItemEntity;
import org.springblade.modules.business.order.service.ISaleOrderDiffService;
import org.springblade.modules.business.order.service.ISaleOrderItemService;
import org.springblade.modules.business.product.excel.SkuWarehouseRelationExcel;
import org.springblade.modules.business.product.mapper.ProductMapper;
import org.springblade.modules.business.product.mapper.SkuWarehouseRelationMapper;
import org.springblade.modules.business.product.pojo.dto.SaveSkuWarehouseDTO;
import org.springblade.modules.business.product.pojo.dto.SkuPriceDTO;
import org.springblade.modules.business.product.pojo.entity.*;
import org.springblade.modules.business.product.pojo.vo.*;
import org.springblade.modules.business.product.service.*;
import org.springblade.modules.business.warehouse.pojo.dto.BatchSelectSkuDTO;
import org.springblade.modules.business.warehouse.pojo.dto.BatchSelectSkuSingle;
import org.springblade.modules.business.warehouse.pojo.dto.IsListManageDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.ListRelationSkuVO;
import org.springblade.modules.business.warehouse.pojo.vo.WarehouseAdminIdVO;
import org.springblade.modules.business.warehouse.service.IWarehousePurchaseService;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品sku与仓库关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Service
@AllArgsConstructor
@Slf4j
public class SkuWarehouseRelationServiceImpl extends BaseServiceImpl<SkuWarehouseRelationMapper, SkuWarehouseRelationEntity> implements ISkuWarehouseRelationService {

	private IWarehouseService warehouseService;
	private ISkuStockService skuStockService;
	@Autowired
	private IProductPriceService productPriceService;
	@Autowired
	private ISkuPpService skuPpService;
	@Autowired
	private ISkuOemService skuOemService;
	@Autowired
	private ProductMapper productMapper;
	@Autowired
	private SaleOrderMapper saleOrderMapper;
	@Autowired
	private ISaleOrderDiffService saleOrderDiffService;
	@Autowired
	private ISaleOrderItemService saleOrderItemService;
	@Autowired
	private IBaseUnitService baseUnitService;
	@Autowired
	private IPackageUnitService packageUnitService;
	@Autowired
	private ISkuWarehousePriceTemporaryService skuWarehousePriceTemporaryService;
	private final RedisLockClient redisLockClient;
	private final IWarehousePurchaseService warehousePurchaseService;
	@Autowired
	private ProductPutCache productPutCache;
	private final MessageSenderService messageSenderService;
	private final StringRedisTemplate stringRedisTemplate;
	private final ApplicationEventPublisher eventPublisher;
	private static final int IS_SHOW_ASSOCIATED = 1; // 是否关联（0-不关联，1-关联）
	private static final int IS_SHOW_NOT_ASSOCIATED = 0; // 是否关联（0-不关联，1-关联）

	@Override
	public IPage<SkuWarehouseRelationVO> selectSkuWarehouseRelationPage(IPage<SkuWarehouseRelationVO> page, SkuWarehouseRelationVO skuWarehouseRelation) {
		return page.setRecords(baseMapper.selectSkuWarehouseRelationPage(page, skuWarehouseRelation));
	}

	@Override
	public List<SkuWarehouseRelationExcel> exportSkuWarehouseRelation(Wrapper<SkuWarehouseRelationEntity> queryWrapper) {
		List<SkuWarehouseRelationExcel> skuWarehouseRelationList = baseMapper.exportSkuWarehouseRelation(queryWrapper);
		//skuWarehouseRelationList.forEach(skuWarehouseRelation -> {
		//	skuWarehouseRelation.setTypeName(DictCache.getValue(DictEnum.YES_NO, SkuWarehouseRelationEntity.getType()));
		//});
		return skuWarehouseRelationList;
	}

	@Override
	public void saveSkuWarehouseAll(SaveSkuWarehouseDTO dto) {
		//判断仓库是不是存在
		warehouseService.getById(dto.getWarehouseId());
		List<Long> ids = Arrays.stream(dto.getSkuIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
		//获取未关联的sku
		List<SkuWarehouseRelationEntity> unRelatedList = baseMapper.unRelatedSku(ids, dto.getWarehouseId());
		List<Long> skuIds = unRelatedList.stream().map(SkuWarehouseRelationEntity::getSkuId).collect(Collectors.toList());
		//获取sku并存入
		if (!skuIds.isEmpty()) {
			List<SkuStockEntity> skuStockList = skuStockService.getIds(skuIds);
			//保存新的关系
			List<SkuWarehouseRelationEntity> skuWarehouseRelationList = new ArrayList<>();
			for (SkuStockEntity skuStock : skuStockList) {
				SkuWarehouseRelationEntity skuWarehouseRelation = new SkuWarehouseRelationEntity();
				skuWarehouseRelation.setSkuId(skuStock.getId());
				skuWarehouseRelation.setWarehouseId(dto.getWarehouseId());
				skuWarehouseRelation.setIsList(1);
				skuWarehouseRelation.setCreateTimeAt(GenerateNumberUtil.TimeStamp());
				skuWarehouseRelation.setCreateTime(new Date());
				skuWarehouseRelation.setCreateUser(AuthUtil.getUserId());
				skuWarehouseRelation.setStatus(1);
				skuWarehouseRelationList.add(skuWarehouseRelation);
			}
			if (!skuWarehouseRelationList.isEmpty()) {
				baseMapper.insert(skuWarehouseRelationList);
			}
		}

	}

	@Override
	public List<ListRelationSkuVO> listRelationSku(Integer isList, Long purchaserId, String productName) {
		if (ObjectUtil.isEmpty(purchaserId)) {
			throw new ServiceException("采购员不能为空");
		}
		if (ObjectUtil.isNotEmpty(isList) && isList != 0 && isList != 1) {
			throw new ServiceException("isList参数错误");
		}
		WarehouseEntity warehouse = warehouseService.getByUserId(purchaserId);
		if (ObjectUtil.isEmpty(warehouse)) {
			throw new ServiceException("未获取到仓库信息");
		}
		Long warehouseId = warehouse.getId();
		Integer warehouseType = warehouse.getWarehouseType();
		boolean isGeneralWarehouse = WarehouseTypeEnum.isTotalWarehouse(warehouseType);
		List<ListRelationSkuVO> result = new ArrayList<>();

		// 获取所有审核状态为「通过」的SKU ID集合（用于后续过滤）
		Set<Long> approvedSkuIds = this.getApprovedSkuIds();

		// 获取所有专采skuId
		Set<Long> ppAllSkuIds = skuPpService.list(new QueryWrapper<SkuPpEntity>().lambda()
				.eq(SkuPpEntity::getIsDeleted, 0))
			.stream()
			.map(SkuPpEntity::getSkuId)
			.collect(Collectors.toSet());

		// 处理采购员专采SKU（来源标识0）
		Set<Long> exclusivePurchaseSkuIds = this.processExclusivePurchases(purchaserId, approvedSkuIds, result);

		// 处理本仓库专供SKU（来源标识1）
		Set<Long> exclusiveSupplySkuIds = this.processExclusiveSupplies(warehouseId, approvedSkuIds, ppAllSkuIds, result);

		// 处理总仓SKU，总仓逻辑处理：展示未关联专采以及除本仓库专供外的SKU（来源标识2）
		if (isGeneralWarehouse) {
			this.processGeneralWarehouse(warehouseId, approvedSkuIds, exclusiveSupplySkuIds, ppAllSkuIds, result);
		}
		// 合并过滤条件：isList和productName的过滤逻辑
		if (ObjectUtil.isNotEmpty(isList) || ObjectUtil.isNotEmpty(productName)) {
			result = result.stream()
				.filter(e -> {
					// isList过滤条件
					boolean isListMatch = ObjectUtil.isEmpty(isList) || Objects.equals(e.getIsList(), isList);
					// productName模糊匹配过滤条件
					boolean productNameMatch = ObjectUtil.isEmpty(productName) ||
						(ObjectUtil.isNotEmpty(e.getProductName()) && e.getProductName().contains(productName));
					return isListMatch && productNameMatch;
				})
				.collect(Collectors.toList());
		}
		return result;
	}

	@Override
	public Set<Long> getApprovedSkuIds() {
		return skuStockService.list(new QueryWrapper<SkuStockEntity>().lambda()
				.eq(SkuStockEntity::getAuditStatus, 1))
			.stream()
			.map(SkuStockEntity::getId)
			.collect(Collectors.toSet());
	}

	/**
	 * 专采SKU筛选规则：
	 * 1. 按采购员ID过滤
	 * 2. 仅包含未删除且审核通过的SKU
	 * 3. 自动关联商品主数据
	 */
	private Set<Long> processExclusivePurchases(Long purchaserId, Set<Long> approvedSkuIds, List<ListRelationSkuVO> result) {
		List<SkuPpEntity> skuPpList = skuPpService.list(new QueryWrapper<SkuPpEntity>().lambda()
				.eq(SkuPpEntity::getPurchaserId, purchaserId)
				.eq(SkuPpEntity::getIsDeleted, 0))
			.stream()
			.filter(e -> approvedSkuIds.contains(e.getSkuId()))
			.toList();

		return this.processSkus(skuPpList, SkuPpEntity::getSkuId, 0, result);
	}

	/**
	 * 专供SKU筛选规则：
	 * 1. 按仓库ID过滤
	 * 2. 排除所有专采SKU
	 * 3. 价格数据取自专供定价表
	 */
	private Set<Long> processExclusiveSupplies(Long warehouseId, Set<Long> approvedSkuIds, Set<Long> ppAllSkuIds, List<ListRelationSkuVO> result) {
		List<SkuOemEntity> skuOemList = skuOemService.list(new QueryWrapper<SkuOemEntity>().lambda()
				.eq(SkuOemEntity::getWarehouseId, warehouseId)
				.eq(SkuOemEntity::getIsDeleted, 0))
			.stream()
			.filter(e -> approvedSkuIds.contains(e.getSkuId()) && !ppAllSkuIds.contains(e.getSkuId()))
			.toList();

		return this.processSkus(skuOemList, SkuOemEntity::getSkuId, 1, result);
	}

	private <T> Set<Long> processSkus(List<T> skuList, Function<T, Long> skuIdExtractor, int skuSource, List<ListRelationSkuVO> result) {
		if (CollectionUtil.isEmpty(skuList)) return Collections.emptySet();

		// 批量查询SKU和Product信息
		Set<Long> skuIds = skuList.stream().map(skuIdExtractor).collect(Collectors.toSet());
		Map<Long, SkuStockEntity> skuStockMap = this.safeQuerySkuStockMap(skuIds);
		Set<Long> productIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getProductId).collect(Collectors.toSet());
		Map<Long, ProductEntity> productMap = this.safeQueryProductMap(productIds);
		Set<Long> baseUnitIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getBaseUnitId).collect(Collectors.toSet());
		Map<Long, BaseUnitEntity> baseUnitMap = this.safeQueryBaseUnitMap(baseUnitIds);
		Set<Long> packageUnitIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getPackageUnitId).collect(Collectors.toSet());
		Map<Long, PackageUnitEntity> packageUnitMap = this.safeQueryPackageUnitMap(packageUnitIds);

		skuList.forEach(entity -> {
			Long skuId = skuIdExtractor.apply(entity);
			SkuStockEntity skuStock = skuStockMap.get(skuId);
			ProductEntity product = productMap.get(skuStock.getProductId());
			BaseUnitEntity baseUnitEntity = baseUnitMap.get(skuStock.getBaseUnitId());
			if (0 == product.getIsStandard() && ObjectUtil.isEmpty(baseUnitEntity)){
				throw new ServiceException("该标品的展示单位已被删除或禁用，skuStock：" + skuStock);
			}
			PackageUnitEntity packageUnitEntity = packageUnitMap.get(skuStock.getPackageUnitId());
			if (ObjectUtil.isEmpty(packageUnitEntity)) {
				throw new ServiceException("该包装单位已被删除或禁用，skuStock：" + skuStock);
			}
			ListRelationSkuVO vo = this.buildBaseVO(skuStock, product, skuSource, baseUnitEntity, packageUnitEntity);
			if (entity instanceof SkuPpEntity ppEntity) {
				vo.setIsList(ppEntity.getIsList());
				vo.setLimitPurchaseQuantity(ppEntity.getLimitPurchaseQuantity());
				vo.setPrice(CommonUtil.ConvertIntBigDecimal(ppEntity.getPrice()));
				vo.setPriceType(ppEntity.getPriceType());
				vo.setPurchaserId(ppEntity.getPurchaserId());
			} else if (entity instanceof SkuOemEntity oemEntity) {
				vo.setIsList(oemEntity.getIsList());
				vo.setLimitPurchaseQuantity(oemEntity.getLimitPurchaseQuantity());
				vo.setPrice(CommonUtil.ConvertIntBigDecimal(oemEntity.getPrice()));
				vo.setPriceType(oemEntity.getPriceType());
				vo.setWarehouseId(oemEntity.getWarehouseId());
			}
			result.add(vo);
		});

		return skuIds;
	}

	/**
	 * 安全查询 SKU 映射表，避免 NullPointerException
	 */
	@Override
	public Map<Long, SkuStockEntity> safeQuerySkuStockMap(Set<Long> skuIds) {
		List<SkuStockEntity> skuStockList = skuStockService.listByIds(skuIds);
		return (skuStockList == null) ? Collections.emptyMap() : skuStockList.stream()
			.collect(Collectors.toMap(SkuStockEntity::getId, Function.identity()));
	}

	/**
	 * 安全查询product映射表，避免 NullPointerException
	 */
	@Override
	public Map<Long, ProductEntity> safeQueryProductMap(Set<Long> productIds) {
		List<ProductEntity> productList = productMapper.selectByIds(productIds);
		return (productList == null) ? Collections.emptyMap() : productList.stream()
			.collect(Collectors.toMap(ProductEntity::getId, Function.identity()));
	}

	/**
	 * 总仓SKU处理规则：
	 * 1. 排除所有专采以及本仓库专供关联的SKU
	 * 2. 优先展示有仓库价关联的SKU
	 * 3. 无关联的SKU新建，默认标记为「下架」状态（isList=0）
	 */
	private void processGeneralWarehouse(Long warehouseId, Set<Long> approvedSkuIds, Set<Long> excludedSkuIds, Set<Long> ppAllSkuIds, List<ListRelationSkuVO> result) {
		// 获取总仓所有SKU并过滤
		Set<Long> allSkuIds = skuStockService.list(new QueryWrapper<SkuStockEntity>().lambda()
				.eq(SkuStockEntity::getIsDeleted, 0))
			.stream()
			.map(SkuStockEntity::getId)
			.filter(approvedSkuIds::contains)
			.collect(Collectors.toSet());

		if (CollectionUtil.isEmpty(allSkuIds)) {
			return;
		}

		// 排除所有专采SKU
		allSkuIds.removeAll(ppAllSkuIds);

		if (CollectionUtil.isEmpty(allSkuIds)) {
			return;
		}

		// 排除本仓库专供sku
		allSkuIds.removeAll(excludedSkuIds);

		if (CollectionUtil.isEmpty(allSkuIds)) {
			return;
		}

		// 批量处理仓库关联
		List<SkuWarehouseRelationEntity> relations = this.list(new QueryWrapper<SkuWarehouseRelationEntity>().lambda()
			.in(SkuWarehouseRelationEntity::getSkuId, allSkuIds)
			.eq(SkuWarehouseRelationEntity::getWarehouseId, warehouseId)
			.eq(SkuWarehouseRelationEntity::getIsDeleted, 0));

		if (CollectionUtil.isEmpty(relations)) {
			return;
		}

		// 批量查询SKU、Product、BaseUnitEntity及PackageUnitEntity信息
		Map<Long, SkuStockEntity> skuStockMap = this.safeQuerySkuStockMap(allSkuIds);
		Set<Long> productIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getProductId).collect(Collectors.toSet());
		Map<Long, ProductEntity> productMap = this.safeQueryProductMap(productIds);
		Set<Long> baseUnitIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getBaseUnitId).collect(Collectors.toSet());
		Map<Long, BaseUnitEntity> baseUnitMap = this.safeQueryBaseUnitMap(baseUnitIds);
		Set<Long> packageUnitIds = skuStockMap.values().stream()
			.map(SkuStockEntity::getPackageUnitId).collect(Collectors.toSet());
		Map<Long, PackageUnitEntity> packageUnitMap = this.safeQueryPackageUnitMap(packageUnitIds);

		// 处理有仓库关联的SKU
		relations.forEach(relation -> {
			SkuStockEntity skuStock = skuStockMap.get(relation.getSkuId());
			ProductEntity product = productMap.get(skuStock.getProductId());
			BaseUnitEntity baseUnitEntity = baseUnitMap.get(skuStock.getBaseUnitId());
			if (0 == product.getIsStandard() && ObjectUtil.isEmpty(baseUnitEntity)){
				throw new ServiceException("该标品的展示单位已被删除或禁用，skuStock：" + skuStock);
			}
			PackageUnitEntity packageUnitEntity = packageUnitMap.get(skuStock.getPackageUnitId());
			if (ObjectUtil.isEmpty(packageUnitEntity)) {
				throw new ServiceException("该包装单位已被删除或禁用，skuStock：" + skuStock);
			}
			ListRelationSkuVO vo = this.buildBaseVO(skuStock, product, 2, baseUnitEntity, packageUnitEntity);
			vo.setWarehouseId(warehouseId);
			vo.setIsList(relation.getIsList());
			vo.setLimitPurchaseQuantity(relation.getLimitPurchaseQuantity());
			vo.setPriceType(relation.getPriceType());
			vo.setPrice(CommonUtil.ConvertIntBigDecimal(relation.getPrice()));
			vo.setWarehouseId(relation.getWarehouseId());
			result.add(vo);
		});

		// 处理无仓库关联的SKU
		allSkuIds.removeAll(relations.stream().map(SkuWarehouseRelationEntity::getSkuId).collect(Collectors.toSet()));
		allSkuIds.forEach(skuId -> {
			SkuStockEntity skuStock = skuStockMap.get(skuId);
			ProductEntity product = productMap.get(skuStock.getProductId());
			BaseUnitEntity baseUnitEntity = baseUnitService.getById(skuStock.getBaseUnitId());
			if (0 == product.getIsStandard() && ObjectUtil.isEmpty(baseUnitEntity)){
				throw new ServiceException("该标品的展示单位已被删除或禁用，skuStock：" + skuStock);
			}
			PackageUnitEntity packageUnitEntity = packageUnitService.getById(skuStock.getPackageUnitId());
			if (ObjectUtil.isEmpty(packageUnitEntity)) {
				throw new ServiceException("该包装单位已被删除或禁用，skuStock：" + skuStock);
			}
			ListRelationSkuVO vo = this.buildBaseVO(skuStock, product, 2, baseUnitEntity, packageUnitEntity);
			vo.setWarehouseId(warehouseId);
			vo.setIsList(0);
			result.add(vo);
		});
	}

	/**
	 * 安全查询PackageUnitEntity映射表，避免 NullPointerException
	 */
	@Override
	public Map<Long, PackageUnitEntity> safeQueryPackageUnitMap(Set<Long> packageUnitIds) {
		List<PackageUnitEntity> packageUnitList = packageUnitService.listByIds(packageUnitIds);
		return (packageUnitList == null) ? Collections.emptyMap() : packageUnitList.stream()
			.collect(Collectors.toMap(PackageUnitEntity::getId, Function.identity()));
	}

	/**
	 * 安全查询BaseUnitEntity映射表，避免 NullPointerException
	 */
	@Override
	public Map<Long, BaseUnitEntity> safeQueryBaseUnitMap(Set<Long> baseUnitIds) {
		List<BaseUnitEntity> baseUnitList = baseUnitService.listByIds(baseUnitIds);
		return (baseUnitList == null) ? Collections.emptyMap() : baseUnitList.stream()
			.collect(Collectors.toMap(BaseUnitEntity::getId, Function.identity()));
	}

	/**
	 * 构建SKU基础视图对象
	 *
	 * @param skuSource 来源标识（0-专采，1-专供，2-总仓）
	 */
	private ListRelationSkuVO buildBaseVO(SkuStockEntity skuStock, ProductEntity product, int skuSource, BaseUnitEntity baseUnitEntity, PackageUnitEntity packageUnitEntity) {
		ListRelationSkuVO vo = new ListRelationSkuVO();
		vo.setProductId(product.getId());         // 商品ID（关联商品主数据）
		vo.setProductName(product.getName());     // 商品名称（前端展示用）
		vo.setSkuId(skuStock.getId());            // SKU唯一标识
		vo.setSpData(skuStock.getSpData());       // SKU规格数据（JSON格式）
		vo.setSkuFrom(skuSource);                 // 来源标识（影响前端展示逻辑）
		// 设置单位名称，非标品写死为"件"，标品动态展示
		if (1 == product.getIsStandard()) {
			vo.setBaseUnitName("件");
		} else {
			vo.setBaseUnitName(baseUnitEntity.getUnitName());
		}
		// 设置净重和毛重描述
		String grossDescribe = "每" + vo.getBaseUnitName() + "毛重" + skuStock.getPackageGrossConversionRate().toString() + packageUnitEntity.getPackageName();
		String NetDescribe = "每" + vo.getBaseUnitName() + "净重" + skuStock.getPackageNetConversionRate().toString() + packageUnitEntity.getPackageName();
		vo.setGrossDescribe(grossDescribe);
		vo.setNetDescribe(NetDescribe);
		vo.setGrossRate(skuStock.getPackageGrossConversionRate());
		vo.setNetRate(skuStock.getPackageNetConversionRate());
		return vo;
	}

	@Override
	public IPage<PageAuditSkuVO> pageAuditSku(IPage<PageAuditSkuVO> page, Long warehouseId, Integer selectType, String selectCode, Long categoryId, Integer returnPrice) {
		IPage<PageAuditSkuVO> resPage = page.setRecords(skuStockService.pageAuditSku(page, warehouseId, selectType, selectCode, categoryId));
		if (Func.notNull(returnPrice) && returnPrice == 1 && resPage.getTotal() > 0) {
			List<Long> skuIds = resPage.getRecords().stream().map(PageAuditSkuVO::getId).distinct().toList();
			Map<Long, Integer> priceMap = this.getSkuPrice(warehouseId, skuIds);
			if (Func.isNotEmpty(priceMap)) {
				resPage.getRecords().forEach(item -> item.setUnitPrice(CommonUtil.ConvertIntBigDecimal(priceMap.get(item.getId()))));
			}
		}
		return resPage;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean batchSelectSku(BatchSelectSkuDTO batchSelectSkuDTO) {
		// 参数校验
		this.validateBatchSelectSkuParams(batchSelectSkuDTO);
		Long warehouseId = batchSelectSkuDTO.getWarehouseId();
		//Long purchaserId = warehouseService.getPurchaserIdByWarehouseId(warehouseId); // 通过仓库寻找采购员（一个仓库有且只有一个采购员）
		List<BatchSelectSkuSingle> skuSingles = batchSelectSkuDTO.getSkuList();

		// 查询此仓库所有已关联的sku
		List<SkuWarehouseRelationEntity> allSelectSkuByWarehouseId = baseMapper.selectAllRelationsWarehouse(warehouseId);

		// 如果skuList为空，则取消所有关联
		if (CollectionUtil.isEmpty(skuSingles)) {
			if (CollectionUtil.isNotEmpty(allSelectSkuByWarehouseId)) {
				allSelectSkuByWarehouseId.forEach(relation -> {
					if (relation != null) {
						relation.setIsShow(IS_SHOW_NOT_ASSOCIATED); // 是否关联（0-不关联，1-关联）
						relation.setAddPrice(0);
					}
				});
				try {
					this.updateBatchById(allSelectSkuByWarehouseId);
				} catch (Exception e) {
					throw new ServiceException("操作过快，请勿重复操作");
				}
			}
			return true;
		}

		// 批量获取需要更新的SKU信息
		List<Long> skuIdsToUpdate = skuSingles.stream().map(BatchSelectSkuSingle::getSkuId).collect(Collectors.toList());

		// 查询allSelectSkuByWarehouseId中skuId未包含在skuIdsToUpdate中的数据
		if (CollectionUtil.isNotEmpty(allSelectSkuByWarehouseId)) {
			List<SkuWarehouseRelationEntity> needToUnSelectList = allSelectSkuByWarehouseId.stream()
				.filter(relation -> !skuIdsToUpdate.contains(relation.getSkuId()))
				.toList();
			// 取消关联
			if (CollectionUtil.isNotEmpty(needToUnSelectList)) {
				needToUnSelectList.forEach(relation -> {
					if (relation != null) {
						relation.setIsShow(IS_SHOW_NOT_ASSOCIATED); // 是否关联（0-不关联，1-关联）
						relation.setAddPrice(0);
					}
				});
				try {
					this.updateBatchById(needToUnSelectList);
				} catch (Exception e) {
					throw new ServiceException("操作过快，请勿重复操作");
				}
			}
		}
		// 更新现有的SKU关系
		Map<Long, SkuWarehouseRelationEntity> existingRelations = baseMapper.RelatedSkuNotShow(warehouseId, skuIdsToUpdate)
			.stream()
			.collect(Collectors.toMap(SkuWarehouseRelationEntity::getSkuId, relation -> relation));
		List<SkuWarehouseRelationEntity> updatedRelations = new ArrayList<>();
		for (BatchSelectSkuSingle single : skuSingles) {
			Long skuId = single.getSkuId();
			SkuWarehouseRelationEntity relation = existingRelations.get(skuId);
			if (relation != null) {
				relation.setIsShow(IS_SHOW_ASSOCIATED); // 是否关联（0-不关联，1-关联）
				relation.setAddPrice(CommonUtil.ConvertBigDecimalInt(single.getAddPrice()));
				updatedRelations.add(relation);
			}
		}
		this.updateBatchById(updatedRelations); // 批量更新
		// 构建新的SKU关系
		List<SkuWarehouseRelationEntity> newRelations = skuSingles.stream()
			.filter(single -> !existingRelations.containsKey(single.getSkuId())) // 过滤已存在的SKU
			.map(single -> this.createSkuWarehouseRelationEntity(warehouseId, single))
			.collect(Collectors.toList());

		// 批量保存新的SKU关系
		if (CollectionUtil.isNotEmpty(newRelations)) {
			try {
				this.saveBatch(newRelations);
			} catch (Exception e) {
				throw new ServiceException("操作过快，请勿重复操作");
			}
		}
		return true;
	}

	/**
	 * 参数校验方法
	 */
	private void validateBatchSelectSkuParams(BatchSelectSkuDTO batchSelectSkuDTO) {
		if (ObjectUtil.isEmpty(batchSelectSkuDTO)) {
			throw new ServiceException("批量选择库存的SKU入参对象不能为空");
		}
		Long warehouseId = batchSelectSkuDTO.getWarehouseId();
		if (ObjectUtil.isEmpty(warehouseId)) {
			throw new ServiceException("仓库ID不能为空");
		}
		// 如果skuList不为空，则校验其中的skuId
		List<BatchSelectSkuSingle> skuList = batchSelectSkuDTO.getSkuList();
		if (CollectionUtil.isNotEmpty(skuList)) {
			boolean hasInvalidSkuId = skuList.stream()
				.anyMatch(single -> ObjectUtil.isEmpty(single.getSkuId()));
			if (hasInvalidSkuId) {
				throw new ServiceException("SKU ID不能为空");
			}
		}
	}

	/**
	 * 创建单个SkuWarehouseRelationEntity对象
	 */
	private SkuWarehouseRelationEntity createSkuWarehouseRelationEntity(Long warehouseId, BatchSelectSkuSingle single) {
		SkuWarehouseRelationEntity entity = new SkuWarehouseRelationEntity();
		entity.setWarehouseId(warehouseId);
		entity.setSkuId(single.getSkuId());
		entity.setAddPrice(CommonUtil.ConvertBigDecimalInt(single.getAddPrice()));
		entity.setIsShow(IS_SHOW_ASSOCIATED); // 是否关联（0-不关联，1-关联）
		// 校验此sku是否属于专采或专供，并修改价格和上下架状态
		this.managePpOrOem(warehouseId, entity, single.getSkuId());
		return entity;
	}

	private void managePpOrOem(Long warehouseId, SkuWarehouseRelationEntity skuWarehouseRelation, Long skuId) {
		SkuPpEntity skuPpEntity = skuPpService.getOne(new LambdaQueryWrapper<SkuPpEntity>()
			.eq(SkuPpEntity::getSkuId, skuId)
			.eq(SkuPpEntity::getIsDeleted, 0));

		if (ObjectUtil.isNotEmpty(skuPpEntity)) {
			this.setRelationAttributes(skuWarehouseRelation, skuPpEntity);
			return;
		}

		SkuOemEntity skuOemEntity = skuOemService.getOne(new LambdaQueryWrapper<SkuOemEntity>()
			.eq(SkuOemEntity::getSkuId, skuId)
			.eq(SkuOemEntity::getWarehouseId, warehouseId)
			.eq(SkuOemEntity::getIsDeleted, 0));

		if (ObjectUtil.isNotEmpty(skuOemEntity)) {
			this.setRelationAttributes(skuWarehouseRelation, skuOemEntity);
		}
	}

	private void setRelationAttributes(SkuWarehouseRelationEntity skuWarehouseRelation, Object entity) {
		if (entity instanceof SkuPpEntity skuPpEntity) {
			skuWarehouseRelation.setPrice(skuPpEntity.getPrice());
			skuWarehouseRelation.setPriceType(skuPpEntity.getPriceType());
			skuWarehouseRelation.setIsList(skuPpEntity.getIsList());
			skuWarehouseRelation.setLimitPurchaseQuantity(skuPpEntity.getLimitPurchaseQuantity());
			skuWarehouseRelation.setSold(skuPpEntity.getSold());
		} else if (entity instanceof SkuOemEntity skuOemEntity) {
			skuWarehouseRelation.setPrice(skuOemEntity.getPrice());
			skuWarehouseRelation.setPriceType(skuOemEntity.getPriceType());
			skuWarehouseRelation.setIsList(skuOemEntity.getIsList());
			skuWarehouseRelation.setLimitPurchaseQuantity(skuOemEntity.getLimitPurchaseQuantity());
			skuWarehouseRelation.setSold(skuOemEntity.getSold());
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean isListManage(List<IsListManageDTO> isListManageDTOs) {
		Boolean lock = redisLockClient.lockFair("lock:isListManage:" + AuthUtil.getUser().getUserId(), 1, 300, () -> {
//		// 校验所有仓库是否都在营业时间之外
//		if (!this.checkAllWarehouseIsOutOfBusinessTime()) {
//			throw new ServiceException("有部分仓库还在营业，请在所有仓库都未营业时询价");
//		}
			// 参数基础校验
			this.validateInput(isListManageDTOs);

			// 初始化需要更新的专采集合，批处理增加效率
			List<SkuPpEntity> skuPpEntities = new ArrayList<>();

			// 初始化需要更新的sku与仓库关联关系集合，批处理增加效率
			List<SkuWarehouseRelationEntity> updateEntities = new ArrayList<>();

			// 初始化询价记录集合，批处理增加效率（目前只有专采才会用到）
			List<ProductPriceEntity> productPriceEntities = new ArrayList<>();

			// 获取所有skuId
			Set<Long> skuAllIds = isListManageDTOs.stream()
				.map(IsListManageDTO::getSkuId)
				.collect(Collectors.toSet());

			// 批量查询isListManageDTOs中的所有SKU并按照skuId分组以减少数据库交互
			Map<Long, SkuStockEntity> skuMap = this.getSkuMapFromDTOs(skuAllIds);

			// 获取isListManageDTOs下的skuFrom为总仓的所有专供的仓库并按照skuId分组以减少数据库交互
			//Map<Long, List<SkuOemEntity>> allGeneralPurchaseOemList = this.getAllGeneralPurchaseOemList(isListManageDTOs);

			// 获取所有专采的skuId
			Set<Long> skuIdFromPurchaserSpecial = isListManageDTOs.stream()
				.filter(dto -> dto.getSkuFrom() == 0)
				.map(IsListManageDTO::getSkuId)
				.collect(Collectors.toSet());
			// 获取isListManageDTOs下的skuFrom为专采的所有专采的仓库并按照skuId分组以减少数据库交互
			Map<Long, SkuPpEntity> allGeneralPurchasePpList = this.getAllGeneralPurchasePpList(skuIdFromPurchaserSpecial);

			// 获取skuIdFromPurchaserSpecial对应的商品信息并以skuId分组
			Map<Long, ProductEntity> productMap = this.getProductsBySkuIds(skuIdFromPurchaserSpecial);

			// 查询所有的展示单位名称并以其id分组
			Map<Long, String> baseUnitMap = baseUnitService.list().stream().collect(Collectors.toMap(BaseUnitEntity::getId, BaseUnitEntity::getUnitName));

			// 处理每个SKU的变更，同时生成chy_product_price的批次号
			Long batchNo = IdWorker.getId();
			Date date = new Date();
			for (IsListManageDTO dto : isListManageDTOs) {
				this.processSkuChange(
					dto, updateEntities, skuMap,
					//allGeneralPurchaseOemList,
					skuPpEntities, productPriceEntities, allGeneralPurchasePpList, productMap, batchNo, baseUnitMap, date
				);
			}

			// 批处理更新
			if (CollectionUtil.isNotEmpty(updateEntities)) {
				this.updateBatchById(updateEntities);

				// 将此仓库的爆款、热销商品、秒杀商品基础信息缓存删除,同时删除有变更的商品价格缓存
				this.dealWithCache(updateEntities);
			}
			if (CollectionUtil.isNotEmpty(skuPpEntities)){
				skuPpService.updateBatchById(skuPpEntities);
			}

			//推送微信消息通知及批处理询价日志
			if(Func.isNotEmpty(productPriceEntities)){
				productPriceService.saveBatch(productPriceEntities);
				log.info("发布询价微信消息通知，涉及 {} 条明细。", productPriceEntities.size());
				eventPublisher.publishEvent(new BusinessEvent<>(this, productPriceEntities, "IS_LIST_MANAGE_ITEMS"));
			}
			return Boolean.TRUE;
		});

		if (Func.isNull(lock)) {
			throw new ServiceException("请求正在处理中，请勿重复提交");
		}
		return true;
	}

	//推送询价微信消息通知方法
	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, condition = "#event.type == 'IS_LIST_MANAGE_ITEMS'")
	public void handleIsListManageEvent(BusinessEvent<List<ProductPriceEntity>> event) {
		List<ProductPriceEntity> itemList = event.getData();
		try {
			log.info("开始处理商品询价推送消息（事务提交后），共涉及 {} 条采购明细。", itemList.size());
			if (Func.isEmpty(itemList)) {
				return;
			}

			// 1. 一次性获取所有相关的管理员ID（总仓），并去重
			Set<Long> allAdminIds = warehousePurchaseService.getWarehouseAdminList().stream()
				.map(WarehouseAdminIdVO::getUserId)
				.collect(Collectors.toSet());

			if (allAdminIds.isEmpty()) {
				log.warn("未找到任何相关的仓库管理员，将跳过管理员通知。");
				return;
			}
			log.info("查询到 {} 位相关仓库管理员，用户ID: {}", allAdminIds.size(), allAdminIds);

			// 2. 按采购员对价格变更进行分组
			Map<String, List<ProductPriceEntity>> changesByPurchaser = itemList.stream()
				.filter(item -> StringUtil.isNotBlank(item.getPurchaserName()))
				.collect(Collectors.groupingBy(ProductPriceEntity::getPurchaserName));

			if (changesByPurchaser.isEmpty()) {
				log.warn("没有找到任何包含采购员信息的询价变更，跳过通知。");
				return;
			}

			String dateStr = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

			// 3. 遍历每个采购员的变更，并发送汇总通知
			for (Map.Entry<String, List<ProductPriceEntity>> entry : changesByPurchaser.entrySet()) {
				String purchaserName = entry.getKey();
				List<ProductPriceEntity> purchaserItems = entry.getValue();

				String representativeProductName = formatProductNameForWechat(purchaserItems);

				log.info("准备向管理员批量推送关于采购员 '{}' 的询价通知...", purchaserName);
				messageSenderService.batchPushSupplyMessage(
					MessageTypeEnum.PRICE_CHANGE,
					new ArrayList<>(allAdminIds),
					representativeProductName, // 使用格式化后的商品名
					dateStr,
					purchaserName
				);
			}
		} catch (Exception e) {
			log.error("处理商品询价推送消息时发生异常（该异常不会影响主事务）", e);
		}
	}

	/**
	 * 根据微信订阅消息的 `thing` 类型（20个字符）限制，格式化商品名称。
	 *
	 * @param items 同一个采购员的商品变更列表
	 * @return 格式化后的商品名称字符串
	 */
	private String formatProductNameForWechat(List<ProductPriceEntity> items) {
		final int WECHAT_THING_MAX_LENGTH = 20;
		long distinctProductCount = items.stream().map(ProductPriceEntity::getProductName).distinct().count();

		if (distinctProductCount == 0) {
			return "未知商品";
		}

		String firstProductName = items.get(0).getProductName();

		if (distinctProductCount == 1) {
			// 只有一个商品，直接截断
			if (firstProductName.length() > WECHAT_THING_MAX_LENGTH) {
				return firstProductName.substring(0, WECHAT_THING_MAX_LENGTH - 3) + "...";
			}
			return firstProductName;
		} else {
			// 多个商品，智能汇总截断
			String suffix = String.format("等%d个商品", distinctProductCount);
			int suffixLength = suffix.length();
			int availableLength = WECHAT_THING_MAX_LENGTH - suffixLength;

			if (availableLength <= 0) {
				// 极端情况，后缀本身就超长了（几乎不可能），直接返回一个通用文本
				return "多个商品";
			}

			if (firstProductName.length() > availableLength) {
				return firstProductName.substring(0, availableLength - 3) + "..." + suffix;
			}
			return firstProductName + suffix;
		}
	}


	/**
	 * 根据SKU ID列表查询对应的产品实体，并返回一个以SKU ID为键的Map。
	 *
	 * @param skuIds SKU ID的列表
	 * @return 一个Map，其中键是SKU ID，值是对应的 ProductEntity
	 */
	@Override
	public Map<Long, ProductEntity> getProductsBySkuIds(Set<Long> skuIds) {
		// 如果输入的skuIds列表为空或为null，则返回一个空的Map
		if (skuIds == null || skuIds.isEmpty()) {
			return Collections.emptyMap();
		}

		// 1. 根据skuIds批量查询SkuStockEntity列表
		List<SkuStockEntity> skuStockEntities = skuStockService.listByIds(skuIds);

		// 如果没有找到任何SKU实体，也返回一个空的Map
		if (skuStockEntities.isEmpty()) {
			return Collections.emptyMap();
		}

		// 2. 从查询到的SkuStockEntity列表中，提取出所有不重复的productId
		List<Long> productIds = skuStockEntities.stream()
			.map(SkuStockEntity::getProductId)
			.distinct()
			.collect(Collectors.toList());

		// 3. 根据productIds批量查询ProductEntity列表
		List<ProductEntity> productEntities = productMapper.selectByIds(productIds);

		// 4. 为了方便后续查找，将ProductEntity列表转换为一个以productId为键的Map
		Map<Long, ProductEntity> productEntityMap = productEntities.stream()
			.collect(Collectors.toMap(ProductEntity::getId, Function.identity()));

		// 5. 构建最终的结果：一个以skuId为键，对应的ProductEntity为值的Map
		// 遍历第一步获取的skuStockEntities列表
		return skuStockEntities.stream()
			// 确保SKU对应的产品存在于productEntityMap中
			.filter(sku -> productEntityMap.containsKey(sku.getProductId()))
			// 将流转换为Map，键为skuId，值为从productEntityMap中获取的ProductEntity
			.collect(Collectors.toMap(
				SkuStockEntity::getId, // SkuStockEntity的ID就是skuId
				sku -> productEntityMap.get(sku.getProductId())
			));
	}

	private Map<Long, SkuPpEntity> getAllGeneralPurchasePpList(Set<Long> skuIdFromPurchaserSpecial) {
		// 获取skuIdFromPurchaserSpecial中每个skuId下的专采仓库集合并按照skuId分组
		if (CollectionUtil.isEmpty(skuIdFromPurchaserSpecial)){
			return Collections.emptyMap();
		} else {
			return skuPpService.list(new LambdaQueryWrapper<SkuPpEntity>()
				.in(SkuPpEntity::getSkuId, skuIdFromPurchaserSpecial)
				.eq(SkuPpEntity::getIsDeleted, 0)
				.eq(SkuPpEntity::getStatus, 1)
				)
				.stream()
				.collect(Collectors.toMap(SkuPpEntity::getSkuId, SkuPpEntity -> SkuPpEntity));
		}
	}

	/**
	 * 将此仓库的爆款、热销商品基础信息缓存删除,同时删除有变更的商品价格缓存
	 */
	private void dealWithCache(List<SkuWarehouseRelationEntity> updateEntities) {
		// 收集所有skuId和warehouseId
		Set<Long> skuIds = updateEntities.stream()
			.map(SkuWarehouseRelationEntity::getSkuId)
			.collect(Collectors.toSet());
		// 批量查询skuId对应的SkuStockEntity
		List<SkuStockEntity> skuStockList = skuStockService.listByIds(skuIds);
		Map<Long, Long> skuIdToProductIdMap = skuStockList.stream()
			.collect(Collectors.toMap(SkuStockEntity::getId, SkuStockEntity::getProductId));
		// 构建 warehouseId -> Set<productId> 的映射
		Map<Long, Set<Long>> warehouseProductMap = new HashMap<>();
		for (SkuWarehouseRelationEntity entity : updateEntities) {
			Long warehouseId = entity.getWarehouseId();
			Long skuId = entity.getSkuId();
			Long productId = skuIdToProductIdMap.get(skuId);
			if (productId == null) continue;
			warehouseProductMap
				.computeIfAbsent(warehouseId, k -> new HashSet<>())
				.add(productId);
		}
		List<String> deleteProductBasic = new ArrayList<>();
		warehouseProductMap.forEach((warehouseId, productIds) -> {
			// 将此仓库的爆款、热销商品基础信息缓存删除
			productPutCache.initHotProducts(warehouseId, new ArrayList<>());
			productPutCache.initTopProducts(warehouseId, new ArrayList<>());
			productPutCache.initSecKillProducts(warehouseId, new ArrayList<>());
			productIds.forEach(productId -> {
				// 批量删除商品价格缓存
				deleteProductBasic.add(CacheConstant.PRODUCT_PRICE + warehouseId + ":" + productId);
			});
		});
		stringRedisTemplate.delete(deleteProductBasic);
	}

	private Map<Long, List<SkuOemEntity>> getAllGeneralPurchaseOemList(List<IsListManageDTO> isListManageDTOs) {
		Set<Long> skuIdFromGeneralPurchases = isListManageDTOs.stream()
			.filter(dto -> dto.getSkuFrom() == 2)
			.map(IsListManageDTO::getSkuId)
			.collect(Collectors.toSet());
		// 获取skuIdFromGeneralPurchases中每个skuId下的专供仓库集合并按照skuId分组
		if (CollectionUtil.isEmpty(skuIdFromGeneralPurchases)) {
			return Collections.emptyMap();
		} else {
			return skuOemService.list(new LambdaQueryWrapper<SkuOemEntity>()
				.in(SkuOemEntity::getSkuId, skuIdFromGeneralPurchases)
				.eq(SkuOemEntity::getIsDeleted, 0)
				.eq(SkuOemEntity::getStatus, 1)
			).stream().collect(Collectors.groupingBy(SkuOemEntity::getSkuId));
		}
	}

	private Map<Long, SkuStockEntity> getSkuMapFromDTOs(Set<Long> skuAllIds) {
		return skuStockService.list(new LambdaQueryWrapper<SkuStockEntity>()
				.in(SkuStockEntity::getId, skuAllIds))
			.stream()
			.collect(Collectors.toMap(SkuStockEntity::getId, sku -> sku));
	}

	/**
	 * 判断当前时间是否在营业时间内
	 */
	private boolean isWithinBusinessHours(WarehouseEntity warehouse, LocalTime currentTime) {
		String startTimeStr = warehouse.getBusinessStartTime();
		String endTimeStr = warehouse.getBusinessEndTime();

		if (startTimeStr == null || endTimeStr == null) {
			return false;
		}

		LocalTime startTime = LocalTime.parse(startTimeStr, DateTimeFormatter.ofPattern("HH:mm"));
		LocalTime endTime = LocalTime.parse(endTimeStr, DateTimeFormatter.ofPattern("HH:mm"));

		if (endTime.isBefore(startTime)) {
			// 跨天营业时间处理
			return !(currentTime.isAfter(endTime) && currentTime.isBefore(startTime));
		} else {
			// 普通营业时间处理
			return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
		}
	}

	/**
	 * 提供给定时任务调用的退补差价处理方法
	 */
	@Transactional(rollbackFor = Exception.class)
	public void processPriceDifference() {
		// 1. 获取符合条件的仓库ID列表
		List<Long> warehouseIds = this.getWarehousesInBusinessHours();
		if (CollectionUtil.isEmpty(warehouseIds)) {
			return;
		}

		// 2. 获取需要处理的SKU关系数据
		List<SkuWarehouseRelationEntity> relations = this.getRelationsToProcess(warehouseIds);
		if (CollectionUtil.isEmpty(relations)) {
			return;
		}

		// 3. 处理价格变更
		this.processPriceChanges(relations);
	}

	/**
	 * 获取营业时间内的仓库ID列表
	 */
	private List<Long> getWarehousesInBusinessHours() {
		LocalTime currentTime = LocalTime.now();
		return warehouseService.list(new LambdaQueryWrapper<WarehouseEntity>()
				.eq(WarehouseEntity::getStatus, 1)
				.eq(WarehouseEntity::getIsDeleted, 0)
				.isNotNull(WarehouseEntity::getBusinessStartTime)
				.isNotNull(WarehouseEntity::getBusinessEndTime))
			.stream()
			.filter(warehouse -> this.isWithinAdjustedBusinessHours(warehouse, currentTime))
			.map(WarehouseEntity::getId)
			.collect(Collectors.toList());
	}

	/**
	 * 判断是否在调整后的营业时间内（前后缩减10分钟）
	 */
	private boolean isWithinAdjustedBusinessHours(WarehouseEntity warehouse, LocalTime currentTime) {
		LocalTime startTime = LocalTime.parse(warehouse.getBusinessStartTime(), DateTimeFormatter.ofPattern("HH:mm"))
			.plusMinutes(10);
		LocalTime endTime = LocalTime.parse(warehouse.getBusinessEndTime(), DateTimeFormatter.ofPattern("HH:mm"))
			.minusMinutes(10);

		return endTime.isBefore(startTime)
			? !(currentTime.isAfter(endTime) && currentTime.isBefore(startTime))
			: currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
	}

	/**
	 * 获取需要处理的SKU关系数据
	 */
	private List<SkuWarehouseRelationEntity> getRelationsToProcess(List<Long> warehouseIds) {
		// 1. 获取临时价格表数据
		List<SkuWarehousePriceTemporaryEntity> temporaryPrices = skuWarehousePriceTemporaryService.list(
			new LambdaQueryWrapper<SkuWarehousePriceTemporaryEntity>()
				.in(SkuWarehousePriceTemporaryEntity::getWarehouseId, warehouseIds)
		);

		if (CollectionUtil.isEmpty(temporaryPrices)) {
			return Collections.emptyList();
		}

		// 2. 获取SKU关系数据
		List<Long> relationIds = temporaryPrices.stream()
			.map(SkuWarehousePriceTemporaryEntity::getId)
			.collect(Collectors.toList());

		return this.listByIds(relationIds);
	}

	/**
	 * 处理价格变更
	 */
	private void processPriceChanges(List<SkuWarehouseRelationEntity> relations) {
		// 1. 初始化价格累计容器
		PriceAccumulator accumulator = new PriceAccumulator();

		// 2. 处理每个SKU关系
		relations.forEach(relation -> {
			// 构建并处理DTO
			IsListManageDTO dto = this.buildListManageDTO(relation);
			// 处理order_item价格变更
			this.processRelationPrices(dto, relation, accumulator);

			// 删除临时价格记录
			skuWarehousePriceTemporaryService.physicalDeletionById(relation.getId());
		});

		// 3. 处理order订单差价
		this.processOrderPriceDifference(
			accumulator.getTotalOrderSumDiffMap(),
			accumulator.getTotalDeliveryFeeDiffMap()
			//accumulator.getChangedOrderIdsMap()
		);
	}

	/**
	 * 价格累计器
	 */
	@Data
	private static class PriceAccumulator {
		private final Map<Long, Integer> totalOrderSumDiffMap = new HashMap<>();
		private final Map<Long, Integer> totalDeliveryFeeDiffMap = new HashMap<>();
		//private final Map<Long, Set<Long>> changedOrderIdsMap = new HashMap<>();
	}

	/**
	 * 构建ListManageDTO对象
	 */
	private IsListManageDTO buildListManageDTO(SkuWarehouseRelationEntity relation) {
		IsListManageDTO dto = new IsListManageDTO();
		dto.setSkuId(relation.getSkuId());
		dto.setWarehouseId(relation.getWarehouseId());
		dto.setIsList(relation.getIsList());
		dto.setPriceType(relation.getPriceType());
		dto.setPrice(CommonUtil.ConvertIntBigDecimal(relation.getPrice()));
		dto.setLimitPurchaseQuantity(relation.getLimitPurchaseQuantity());

		// 设置SKU来源和采购员信息
		SkuSourceInfo sourceInfo = this.determineSkuSource(relation);
		dto.setSkuFrom(sourceInfo.getSkuFrom());
		dto.setPurchaserId(sourceInfo.getPurchaserId());

		return dto;
	}

	/**
	 * SKU来源信息
	 */
	@Data
	@AllArgsConstructor
	private static class SkuSourceInfo {
		private final int skuFrom;
		private final Long purchaserId;
	}

	/**
	 * 确定SKU来源
	 */
	private SkuSourceInfo determineSkuSource(SkuWarehouseRelationEntity relation) {
		// 检查是否是专采
		SkuPpEntity skuPp = skuPpService.getOne(new LambdaQueryWrapper<SkuPpEntity>()
			.eq(SkuPpEntity::getSkuId, relation.getSkuId())
			.eq(SkuPpEntity::getIsDeleted, 0));
		if (skuPp != null) {
			return new SkuSourceInfo(0, skuPp.getPurchaserId());
		}

		// 检查是否是专供
		SkuOemEntity skuOem = skuOemService.getOne(new LambdaQueryWrapper<SkuOemEntity>()
			.eq(SkuOemEntity::getSkuId, relation.getSkuId())
			.eq(SkuOemEntity::getWarehouseId, relation.getWarehouseId())
			.eq(SkuOemEntity::getIsDeleted, 0));
		if (skuOem != null) {
			return new SkuSourceInfo(1, null);
		}

		// 默认为总仓
		return new SkuSourceInfo(2, null);
	}

	/**
	 * 处理关系价格
	 */
	private void processRelationPrices(IsListManageDTO dto, SkuWarehouseRelationEntity relation, PriceAccumulator accumulator) {
		if (dto.getSkuFrom() == 0 || dto.getSkuFrom() == 1) {
			// 专采或专供
			this.mergeOrderPrices(relation.getSkuId(),
				Collections.singletonList(relation.getWarehouseId()),
				dto, accumulator.getTotalOrderSumDiffMap(),
				accumulator.getTotalDeliveryFeeDiffMap());
		} else {
			// 总仓
			List<Long> affectedWarehouseIds = this.getAffectedWarehouseIds(relation.getSkuId());
			this.mergeOrderPrices(relation.getSkuId(),
				affectedWarehouseIds,
				dto, accumulator.getTotalOrderSumDiffMap(),
				accumulator.getTotalDeliveryFeeDiffMap());
		}
	}

	/**
	 * 获取受影响的仓库ID列表（用于总仓SKU）
	 */
	private List<Long> getAffectedWarehouseIds(Long skuId) {
		return this.list(new LambdaQueryWrapper<SkuWarehouseRelationEntity>()
				.eq(SkuWarehouseRelationEntity::getSkuId, skuId)
				.eq(SkuWarehouseRelationEntity::getIsDeleted, 0))
			.stream()
			.map(SkuWarehouseRelationEntity::getWarehouseId)
			.collect(Collectors.toList());
	}

	/**
	 * 参数基础校验
	 */
	private void validateInput(List<IsListManageDTO> dos) {
		if (CollectionUtil.isEmpty(dos)) {
			throw new ServiceException("上下架集合不能为空");
		}
	}

	/**
	 * 价格单位转换与校验
	 */
	private Integer convertAndValidatePrice(IsListManageDTO dto) {
		int isListStatus = dto.getIsList();
		BigDecimal originalPrice = dto.getPrice();

		// 处理价格为负数的情况
		if (originalPrice != null && originalPrice.compareTo(BigDecimal.ZERO) < 0) {
			throw new ServiceException("询价价格不能小于0");
		}

		// 处理上架状态且价格为0的情况
		if (isListStatus == 1 && (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) == 0)) {
			return null; // 返回null表示跳过此条处理
		}

		// 转换价格成分单位
		// 下架状态时，即使价格为0或null也继续处理
		return originalPrice != null ? CommonUtil.ConvertBigDecimalInt(originalPrice) : 0;
	}

	/**
	 * 获取并校验SKU库存实体
	 */
	private void getValidSkuStock(Long skuId, SkuStockEntity entity) {
		if (ObjectUtil.isEmpty(entity)) {
			throw new ServiceException("SKU不存在，skuId：" + skuId);
		}
	}

	/**
	 * 更新采购员专采信息
	 */
	private void updatePurchaserSpecial(IsListManageDTO dto,
										Integer price,
										Map<Long, SkuPpEntity> allGeneralPurchasePpList,
										Map<Long, ProductEntity> productMap,
										List<ProductPriceEntity> productPriceEntities,
										Map<Long, SkuStockEntity> skuMap,
										Long batchNo,
										Map<Long, String> baseUnitMap,
										List<SkuPpEntity> skuPpEntities, Date date) {
		LambdaQueryWrapper<SkuPpEntity> query = new LambdaQueryWrapper<SkuPpEntity>()
			.eq(SkuPpEntity::getSkuId, dto.getSkuId())
			.eq(SkuPpEntity::getPurchaserId, dto.getPurchaserId())
			.eq(SkuPpEntity::getIsDeleted, 0);

		SkuPpEntity entity = Optional.ofNullable(skuPpService.getOne(query))
			.orElseThrow(() -> new ServiceException("专采记录不存在，skuId：%s, purchaserId：%s"
				.formatted(dto.getSkuId(), dto.getPurchaserId())));

		// 校验是否数据有变化，如果无变化则跳过处理
		boolean ifChanged = this.checkIfChangedSkuPp(entity, dto, price);
		if (!ifChanged) {
			return;
		}

		entity.setPrice(price);
		entity.setIsList(dto.getIsList());
		entity.setPriceType(dto.getPriceType());
		// 如果限制购买量有变化，则重置已售出数量为0
		Integer limitPurchaseQuantityOld = entity.getLimitPurchaseQuantity();
		if (!Objects.equals(limitPurchaseQuantityOld, dto.getLimitPurchaseQuantity())) {
			entity.setSold(0);
		}
		entity.setLimitPurchaseQuantity(dto.getLimitPurchaseQuantity());
		skuPpEntities.add(entity);

		// 存放价格变更记录（目前只考虑专采）
		if (dto.getSkuFrom() == 0){
			SkuPpEntity pp = allGeneralPurchasePpList.get(dto.getSkuId());
			if (ObjectUtil.isEmpty(pp)){
				throw new ServiceException("询价错误，未找到对应的专采信息");
			}
			ProductEntity product = productMap.get(dto.getSkuId());
			if (ObjectUtil.isEmpty(product)){
				throw new ServiceException("询价错误，未找到对应的商品信息");
			}
			SkuStockEntity skuStock = skuMap.get(dto.getSkuId());
			if (ObjectUtil.isEmpty(skuStock)){
				throw new ServiceException("询价错误，未找到对应的SKU信息");
			}
			String baseUnitName = baseUnitMap.get(product.getBaseUnitId());
			if (StringUtil.isBlank(baseUnitName)){
				throw new ServiceException("询价错误，未找到对应的商品基本单位信息");
			}
			ProductPriceEntity productPrice = new ProductPriceEntity();
			productPrice.setBatchNo(batchNo);
			productPrice.setProductId(product.getId());
			productPrice.setProductName(product.getName());
			productPrice.setIsStandard(product.getIsStandard());
			productPrice.setSkuId(dto.getSkuId());
			productPrice.setSpData(skuStock.getSpData());
			productPrice.setPackageGrossConversionRate(skuStock.getPackageGrossConversionRate());
			productPrice.setPackageNetConversionRate(skuStock.getPackageNetConversionRate());
			productPrice.setPurchaserId(AuthUtil.getUserId());
			productPrice.setPurchaserName(AuthUtil.getNickName());
			productPrice.setBaseUnitId(product.getBaseUnitId());
			productPrice.setBaseUnitName(baseUnitName);
			productPrice.setBeforePriceType(pp.getPriceType());
			productPrice.setBeforePrice(pp.getPrice());
			productPrice.setBeforeIsList(pp.getIsList());
			productPrice.setAfterPriceType(dto.getPriceType());
			productPrice.setAfterPrice(price);
			productPrice.setAfterIsList(dto.getIsList());
			productPrice.setSkuFrom(dto.getSkuFrom());
			productPrice.setCreateTimeAt(System.currentTimeMillis());
			productPrice.setCreateTime(date);
			productPriceEntities.add(productPrice);
		}
	}

	/**
	 * 通用方法：校验SKU数据是否有变化
	 * 支持SkuPpEntity、SkuOemEntity、SkuWarehouseRelationEntity三种实体类型
	 */
	private <T> boolean checkIfChanged(T entity, IsListManageDTO dto, Integer price,
									   Function<T, Integer> isListGetter,
									   Function<T, Integer> priceGetter,
									   Function<T, Integer> limitPurchaseQuantityGetter,
									   Function<T, Integer> priceTypeGetter) {
		// 上下架状态不同则有变化
		if (!ObjectUtil.nullSafeEquals(isListGetter.apply(entity), dto.getIsList())) {
			return true;
		}
		// 价格不同则有变化
		if (!ObjectUtil.nullSafeEquals(priceGetter.apply(entity), price)) {
			return true;
		}
		// 价格类型不同则有变化
		if (!ObjectUtil.nullSafeEquals(priceTypeGetter.apply(entity), dto.getPriceType())) {
			return true;
		}
		// 限制购买量不同则有变化
		if (!ObjectUtil.nullSafeEquals(limitPurchaseQuantityGetter.apply(entity), dto.getLimitPurchaseQuantity())) {
			return true;
		}
		/*// 在上架的情况下：如果价格或价格类型不同，则有变化；限制购买量不同，则有变化
		if (isListGetter.apply(entity) == 1) {
			if (!ObjectUtil.nullSafeEquals(priceGetter.apply(entity), price)) {
				return true;
			}
			if (!ObjectUtil.nullSafeEquals(priceTypeGetter.apply(entity), dto.getPriceType())) {
				return true;
			}
			if (!ObjectUtil.nullSafeEquals(limitPurchaseQuantityGetter.apply(entity), dto.getLimitPurchaseQuantity())) {
				return true;
			}
		}*/
		return false;
	}

	/**
	 * 校验专采是否数据有变化
	 */
	private boolean checkIfChangedSkuPp(SkuPpEntity entity, IsListManageDTO dto, Integer price) {
		return checkIfChanged(entity, dto, price,
			SkuPpEntity::getIsList,
			SkuPpEntity::getPrice,
			SkuPpEntity::getLimitPurchaseQuantity,
			SkuPpEntity::getPriceType);
	}

	/**
	 * 更新仓库专供信息
	 */
	private void updateWarehouseSpecial(IsListManageDTO dto, Integer price) {
		LambdaQueryWrapper<SkuOemEntity> query = new LambdaQueryWrapper<SkuOemEntity>()
			.eq(SkuOemEntity::getSkuId, dto.getSkuId())
			.eq(SkuOemEntity::getWarehouseId, dto.getWarehouseId())
			.eq(SkuOemEntity::getIsDeleted, 0);

		SkuOemEntity entity = Optional.ofNullable(skuOemService.getOne(query))
			.orElseThrow(() -> new ServiceException("专供记录不存在，skuId：%s, warehouseId：%s"
				.formatted(dto.getSkuId(), dto.getWarehouseId())));

		// 校验是否数据有变化，如果无变化则跳过处理
		boolean ifChanged = this.checkIfChangedSkuOem(entity, dto, price);
		if (!ifChanged) {
			return;
		}

		entity.setPrice(price);
		entity.setIsList(dto.getIsList());
		entity.setPriceType(dto.getPriceType());
		// 如果限制购买量有变化，则重置已售出数量为0
		Integer limitPurchaseQuantityOld = entity.getLimitPurchaseQuantity();
		if (!Objects.equals(limitPurchaseQuantityOld, dto.getLimitPurchaseQuantity())) {
			entity.setSold(0);
		}
		entity.setLimitPurchaseQuantity(dto.getLimitPurchaseQuantity());
		skuOemService.updateById(entity);
	}

	/**
	 * 校验专供是否数据有变化
	 */
	private boolean checkIfChangedSkuOem(SkuOemEntity entity, IsListManageDTO dto, Integer price) {
		return checkIfChanged(entity, dto, price,
			SkuOemEntity::getIsList,
			SkuOemEntity::getPrice,
			SkuOemEntity::getLimitPurchaseQuantity,
			SkuOemEntity::getPriceType);
	}

	/**
	 * 处理订单差价逻辑
	 */
	private void processOrderPriceDifference(Map<Long, Integer> totalOrderSumDiffMap, Map<Long, Integer> totalDeliveryFeeDiffMap) {
		totalOrderSumDiffMap.forEach((orderId, diffAmount) -> {
			SaleOrderEntity order = Optional.ofNullable(saleOrderMapper.selectById(orderId))
				.orElseThrow(() -> new ServiceException("订单不存在，orderId：" + orderId));
			if (diffAmount == 0) return;
			Integer newTotal = order.getTotalAmount() + diffAmount;
			if (newTotal <= 0) {
				throw new ServiceException("订单应付金额不能为负数，orderId：" + orderId + "，修改后应付金额：" + newTotal);
			}
			int newPayAmount = order.getPayAmount() + diffAmount;
			if (newPayAmount <= 0) {
				throw new ServiceException("订单实付金额不能为负数，orderId：" + orderId + "，修改后实付金额：" + newPayAmount);
			}
			int newDeliveryExpense = order.getDeliveryExpense() + totalDeliveryFeeDiffMap.get(orderId);
			if (newDeliveryExpense <= 0) {
				throw new ServiceException("订单运费不能为负数，orderId：" + orderId + "，修改后运费：" + newDeliveryExpense);
			}
			// 新增或修改订单差价记录
			SaleOrderDiffEntity diff = new SaleOrderDiffEntity();
			diff.setOrderId(orderId);
			diff.setOrderNo(order.getOrderNo());
			diff.setTotalAmount(newTotal);
			diff.setDifferenceAmount(Math.abs(diffAmount));
			diff.setDifferenceType(diffAmount > 0 ? DifferenceEnum.ADD_DIFFERENCE.getCode() : DifferenceEnum.RETURN_DIFFERENCE.getCode());
			diff.setDifferenceStatus(DifferenceStatusEnum.WAIT.getCode());
			saleOrderDiffService.checkAndUpdate(diff);
			// 修改chy_sale_order的total_amount、delivery_expense、pay_amount字段
			order.setTotalAmount(newTotal);
			order.setDeliveryExpense(newDeliveryExpense);
			order.setPayAmount(newPayAmount);
			saleOrderMapper.updateById(order);
		});
	}

	/**
	 * 生成价格变更记录
	 */
	private void createPriceRecord(IsListManageDTO dto, Long warehouseId, Long purchaserId) {
		ProductPriceEntity record = new ProductPriceEntity();
		record.setSkuId(dto.getSkuId());
		//record.setWarehouseId(warehouseId);
		record.setPurchaserId(purchaserId);
		record.setAfterPriceType(dto.getPriceType());
		record.setAfterPrice(CommonUtil.ConvertBigDecimalInt(dto.getPrice()));
		//record.setIsList(dto.getIsList());
		record.setSkuFrom(dto.getSkuFrom());
		record.setCreateTimeAt(System.currentTimeMillis());
		productPriceService.save(record);
	}

	/**
	 * 合并订单价格变更数据
	 */
	private void mergeOrderPrices(Long skuId, List<Long> warehouseIds, IsListManageDTO dto, Map<Long, Integer> totalDiffMap, Map<Long, Integer> totalDeliveryFeeDiffMap) {
		List<SkuOrderPriceInquiryVO> priceList = this.getSkuOrderPrices(skuId, warehouseIds, dto);
		// 合并流操作，同时计算总价格和配送费，以及查询改价订单
		priceList.stream()
			.filter(Objects::nonNull) // 过滤掉 null 元素
			.collect(Collectors.groupingBy(
				SkuOrderPriceInquiryVO::getOrderId,
				Collectors.reducing(new OrderPriceSummary(0, 0, new HashSet<>()),
					vo -> new OrderPriceSummary(vo.getTotalPriceDiff(), vo.getDeliveryFeeTotalDiff(), Collections.singleton(vo.getId())),
					(summary1, summary2) -> new OrderPriceSummary(
						summary1.productPriceSumDiff + summary2.productPriceSumDiff,
						summary1.deliveryFeeSumDiff + summary2.deliveryFeeSumDiff,
						Stream.concat(summary1.ids.stream(), summary2.ids.stream()).collect(Collectors.toSet())
					))
			))
			.forEach((orderId, summary) -> {
				totalDiffMap.merge(orderId, summary.productPriceSumDiff, Integer::sum);
				totalDeliveryFeeDiffMap.merge(orderId, summary.deliveryFeeSumDiff, Integer::sum);
			});
	}

	// 辅助类：用于汇总订单的价格和配送费
	private static class OrderPriceSummary {
		int productPriceSumDiff;
		int deliveryFeeSumDiff;
		Set<Long> ids;

		public OrderPriceSummary(int productPriceSumDiff, int deliveryFeeSumDiff, Set<Long> ids) {
			this.productPriceSumDiff = productPriceSumDiff;
			this.deliveryFeeSumDiff = deliveryFeeSumDiff;
			this.ids = ids;
		}
	}

	/**
	 * 获取订单价格查询结果
	 * 业务规则：
	 * 1. 查询指定SKU和仓库的订单项信息
	 * 2. 计算新的商品价格、配送费和配套运输费
	 * 3. 更新订单项的价格相关信息
	 * 4. 返回处理后的订单价格查询结果
	 *
	 * @param skuId        SKU ID
	 * @param warehouseIds 仓库ID列表
	 * @param dto          价格管理DTO
	 * @return 订单价格查询结果列表
	 */
	private List<SkuOrderPriceInquiryVO> getSkuOrderPrices(Long skuId, List<Long> warehouseIds, IsListManageDTO dto) {
		// 1. 查询基础数据
		List<SkuOrderPriceInquiryVO> results = baseMapper.getSkuOrderPriceInquiryVOS(skuId, warehouseIds);
		if (CollectionUtil.isEmpty(results)) {
			return results;
		}

		// 2. 准备订单项数据
		Map<Long, SaleOrderItemEntity> orderItemMap = this.prepareOrderItemMap(results);
		if (CollectionUtil.isEmpty(orderItemMap)) {
			return results;
		}

		// 3. 处理每个订单项的价格计算
		List<SaleOrderItemEntity> updateList = this.processOrderItems(results, orderItemMap, dto);

		// 4. 批量更新订单项
		if (CollectionUtil.isNotEmpty(updateList)) {
			saleOrderItemService.updateBatchById(updateList);
		}

		return results;
	}

	/**
	 * 准备订单项映射表
	 */
	private Map<Long, SaleOrderItemEntity> prepareOrderItemMap(List<SkuOrderPriceInquiryVO> results) {
		List<Long> orderItemIds = results.stream()
			.filter(vo -> vo != null && vo.getId() != null)
			.map(SkuOrderPriceInquiryVO::getId)
			.collect(Collectors.toList());

		if (CollectionUtil.isEmpty(orderItemIds)) {
			return Collections.emptyMap();
		}

		return saleOrderItemService.listByIds(orderItemIds)
			.stream()
			.collect(Collectors.toMap(SaleOrderItemEntity::getId, e -> e));
	}

	/**
	 * 处理订单项价格计算
	 */
	private List<SaleOrderItemEntity> processOrderItems(List<SkuOrderPriceInquiryVO> results,
														Map<Long, SaleOrderItemEntity> orderItemMap,
														IsListManageDTO dto) {
		List<SaleOrderItemEntity> updateList = new ArrayList<>();
		Integer basePrice = CommonUtil.ConvertBigDecimalInt(dto.getPrice());
		if (basePrice == null) {
			throw new ServiceException("基础价格转换失败");
		}

		for (SkuOrderPriceInquiryVO vo : results) {
			if (vo == null || vo.getId() == null) {
				continue;
			}

			SaleOrderItemEntity orderItem = orderItemMap.get(vo.getId());
			if (orderItem == null) {
				continue;
			}

			// 计算服务费
			Integer serviceFee = this.calculateServiceFee(vo);

			// 计算新价格
			PriceCalculationResult priceResult = this.calculateNewPrices(vo, basePrice, dto, serviceFee);

			// 更新订单项
			this.updateOrderItem(orderItem, vo, dto, basePrice, priceResult, serviceFee);
			updateList.add(orderItem);
		}

		return updateList;
	}

	/**
	 * 计算服务费
	 */
	private Integer calculateServiceFee(SkuOrderPriceInquiryVO vo) {
		return Objects.equals(vo.getIsServiceFeeNew(), IsServiceFeeEnum.YES.getCode())
			? vo.getServiceFeeNew() : 0;
	}

	/**
	 * 价格计算结果对象
	 */
	@Data
	@AllArgsConstructor
	private static class PriceCalculationResult {
		private final Integer productPriceNew;
		private final Integer deliveryExpenseNew;
		private final Integer supportTransNum;
		private final Integer supportTransPriceAllNew;
		private final Integer oldTotalPrice;
		private final Integer newTotalPrice;
	}

	/**
	 * 计算新价格
	 */
	private PriceCalculationResult calculateNewPrices(SkuOrderPriceInquiryVO vo,
													  Integer basePrice,
													  IsListManageDTO dto,
													  Integer serviceFee) {
		// 计算新产品价格
		Integer productPriceNew = PriceCalculator.getPrice(
			basePrice,
			dto.getPriceType(),
			vo.getAddPriceNew(),
			vo.getFormulaNew(),
			vo.getPackageGrossConversionRateNew(),
			serviceFee,
			vo.getProductQuantity()
		);

		// 计算新配送费总额
		Integer deliveryExpenseNew = this.calculateDeliveryExpense(vo);

		// 计算配套运输信息
		Integer supportTransNum = this.calculateSupportTransNum(vo);
		Integer supportTransPriceAllNew = this.calculateSupportTransPrice(vo, supportTransNum);

		// 计算新旧总价
		Integer oldTotalPrice = vo.getProductPriceOld() + vo.getDeliveryExpenseOld() +
			vo.getTransportPriceOld() * vo.getTransportNumOld();
		Integer newTotalPrice = productPriceNew + deliveryExpenseNew + supportTransPriceAllNew;

		// 设置order_item的总配送费差额、所有总价格差额（以便在order主表中聚合生成退补差）,这两个字段不做order_item字段更新，只为后续计算
		vo.setDeliveryFeeTotalDiff(deliveryExpenseNew - vo.getDeliveryExpenseOld());
		vo.setTotalPriceDiff(newTotalPrice - oldTotalPrice);

		return new PriceCalculationResult(
			productPriceNew,
			deliveryExpenseNew,
			supportTransNum,
			supportTransPriceAllNew,
			oldTotalPrice,
			newTotalPrice
		);
	}

	/**
	 * 计算配送费
	 */
	private Integer calculateDeliveryExpense(SkuOrderPriceInquiryVO vo) {
		return Objects.equals(vo.getDeliveryTypeNew(), DeliveryEnum.PICKUP.getCode())
			? 0
			: PriceCalculator.getDeliveryFee(
			vo.getDeliveryFeeNew(),
			0,
			BigDecimal.valueOf(vo.getProductQuantity()),
			vo.getPackageGrossConversionRateNew()
		);
	}

	/**
	 * 计算配套运输数量
	 */
	private Integer calculateSupportTransNum(SkuOrderPriceInquiryVO vo) {
		return vo.getTransportConversionRateNew() != null
			? vo.getProductQuantity() * vo.getTransportConversionRateNew()
			: 0;
	}

	/**
	 * 计算配套运输总价
	 */
	private Integer calculateSupportTransPrice(SkuOrderPriceInquiryVO vo, Integer supportTransNum) {
		return vo.getTransportPriceNew() != null
			? vo.getTransportPriceNew() * supportTransNum
			: 0;
	}

	/**
	 * 更新订单项信息
	 */
	private void updateOrderItem(SaleOrderItemEntity orderItem,
								 SkuOrderPriceInquiryVO vo,
								 IsListManageDTO dto,
								 Integer basePrice,
								 PriceCalculationResult priceResult,
								 Integer serviceFee) {
		// 设置商品销售价格（斤）
		orderItem.setProductSalePrice(
			PriceCalculator.getPriceGrossWeight(
				basePrice,
				dto.getPriceType(),
				vo.getAddPriceNew(),
				vo.getFormulaNew(),
				vo.getPackageGrossConversionRateNew(),
				serviceFee,
				BigDecimal.ONE
			)
		);

		// 设置商品单价（件）
		orderItem.setProductUnitPrice(
			PriceCalculator.getPrice(
				basePrice,
				dto.getPriceType(),
				vo.getAddPriceNew(),
				vo.getFormulaNew(),
				vo.getPackageGrossConversionRateNew(),
				serviceFee,
				1
			)
		);

		// 设置配套运输信息
		orderItem.setSupportTransPrice(vo.getTransportPriceNew());
		orderItem.setSupportTrans(vo.getTransportNameNew());
		orderItem.setSupportTransUnitId(vo.getTransportUnitIdNew());
		orderItem.setSupportTransNum(priceResult.getSupportTransNum());

		// 设置商品销售价格、毛重率
		orderItem.setPackageGrossConversionRate(vo.getPackageGrossConversionRateNew());
		orderItem.setProductPrice(priceResult.getProductPriceNew());

		// 设置送货费用
		orderItem.setDeliveryExpense(priceResult.getDeliveryExpenseNew());

		// 设置差价金额
		orderItem.setDifferenceAmount(priceResult.getNewTotalPrice() - priceResult.getOldTotalPrice());
	}

	/**
	 * 校验价格类型有效性
	 */
	private boolean isValidPriceType(Integer priceType) {
		return priceType != null && (priceType == 0 || priceType == 1);
	}

	@Override
	public void removeByShow(List<Long> idList, Long warehouseId) {
		baseMapper.removeByShow(idList, warehouseId);
	}

	@Override
	public List<SkuWarehouseRelationEntity> getByWarehouseId(Long parentId, List<Long> skuIdList) {
		return baseMapper.getByWarehouseId(parentId, skuIdList);
	}


	@Override
	public List<SkuWarehouseRelationEntity> listByWarehouseIdAndSkuIds(Long warehouseId, List<Long> skuIds) {
		return list(Wrappers.<SkuWarehouseRelationEntity>lambdaQuery()
			.select(SkuWarehouseRelationEntity::getSkuId, SkuWarehouseRelationEntity::getPrice, SkuWarehouseRelationEntity::getPriceType, SkuWarehouseRelationEntity::getAddPrice)
			.eq(SkuWarehouseRelationEntity::getWarehouseId, warehouseId)
			.in(SkuWarehouseRelationEntity::getSkuId, skuIds));
	}

	/**
	 * 获取SKU价格信息
	 * 业务规则：
	 * 1. 支持按重量(斤)或按件数计算价格
	 * 2. 计算包含：商品单价、销售价、配送费、配套运输费等
	 * 3. 价格计算规则：
	 * - 商品单价：根据价格类型(0-按斤,1-按件)计算
	 * - 销售价：商品单价 * 重量/件数
	 * - 配送费：根据配送类型计算(自提为0)
	 * - 配套运输费：配套运输单价 * 配套运输数量
	 * 4. 返回格式：List<Map<String, SkuPriceVO>>，key为skuId
	 *
	 * @param skuPriceDTOs 价格查询参数列表
	 * @return 价格信息列表
	 */
	@Override
	public Map<Long, SkuPriceVO> getSkuPrice(List<SkuPriceDTO> skuPriceDTOs) {
		// 1. 参数基础校验
		this.validateSkuPriceParams(skuPriceDTOs);

		// 2. 批量处理SKU价格计算
		return skuPriceDTOs.stream()
			.map(this::processSingleSkuPrice)
			.collect(Collectors.toMap(SkuPriceVO::getProductSkuId, Function.identity()));
	}

	/**
	 * 参数基础校验
	 */
	private void validateSkuPriceParams(List<SkuPriceDTO> skuPriceDTOs) {
		if (CollectionUtil.isEmpty(skuPriceDTOs)) {
			throw new ServiceException("参数不能为空");
		}
	}

	/**
	 * 处理单个SKU的价格计算
	 */
	private SkuPriceVO processSingleSkuPrice(SkuPriceDTO dto) {
		// 1. 参数校验
		this.validateSingleSkuPriceParams(dto);

		// 2. 查询SKU价格信息
		Map<String, Object> skuPriceMap = baseMapper.selectSkuPriceMap(dto);
		if (CollectionUtil.isEmpty(skuPriceMap)) {
			throw new ServiceException("未找到SKU价格信息，skuId：" + dto.getProductSkuId());
		}

		// 3. 计算商品数量和重量
		BigDecimal packageGrossConversionRate = (BigDecimal) skuPriceMap.get("packageGrossConversionRate");
		SkuQuantityInfo quantityInfo = this.calculateQuantityAndWeight(dto, packageGrossConversionRate);

		// 4. 构建价格VO对象
		SkuPriceVO skuPriceVO = this.buildSkuPriceVO(dto, skuPriceMap, quantityInfo);

		// 5. 计算并设置价格相关字段
		this.calculatePrices(skuPriceVO, skuPriceMap, quantityInfo, dto);

		// 6. 返回结果
		return skuPriceVO;
	}

	/**
	 * 校验单个SKU价格参数
	 */
	private void validateSingleSkuPriceParams(SkuPriceDTO dto) {
		if (ObjectUtil.isEmpty(dto) || ObjectUtil.isEmpty(dto.getProductSkuId())
			|| ObjectUtil.isEmpty(dto.getWarehouseId()) || ObjectUtil.isEmpty(dto.getDeliveryType())
			|| (ObjectUtil.isEmpty(dto.getWeightInPounds()) && ObjectUtil.isEmpty(dto.getProductQuantity()))) {
			throw new ServiceException("参数不能为空");
		}
	}

	/**
	 * 计算商品数量和重量
	 */
	private SkuQuantityInfo calculateQuantityAndWeight(SkuPriceDTO dto, BigDecimal packageGrossConversionRate) {
		SkuQuantityInfo info = new SkuQuantityInfo();
		info.productQuantity = dto.getProductQuantity();
		if (ObjectUtil.isNotEmpty(dto.getWeightInPounds())) {
			info.weightInPounds = dto.getWeightInPounds();
//			info.productQuantity = info.weightInPounds.divide(packageGrossConversionRate, 0, RoundingMode.HALF_UP).intValue();
		} else {
			info.weightInPounds = BigDecimal.valueOf(info.productQuantity).multiply(packageGrossConversionRate);
		}
		info.packageGrossConversionRate = packageGrossConversionRate;
		return info;
	}

	/**
	 * 构建SKU价格VO对象
	 */
	private SkuPriceVO buildSkuPriceVO(SkuPriceDTO dto, Map<String, Object> skuPriceMap, SkuQuantityInfo quantityInfo) {
		SkuPriceVO vo = new SkuPriceVO();
		vo.setWarehouseId(dto.getWarehouseId());
		vo.setProductPic((String) skuPriceMap.get("productPic"));
		vo.setProductName((String) skuPriceMap.get("productName"));
		vo.setProductBrand((String) skuPriceMap.get("productBrand"));
		vo.setProductUnit((String) skuPriceMap.get("productUnit"));
		vo.setProductQuantity(quantityInfo.productQuantity);
		vo.setProductSkuId(dto.getProductSkuId());
		vo.setPackageGrossConversionRate(quantityInfo.packageGrossConversionRate);
		vo.setProductId((Long) skuPriceMap.get("productId"));
		vo.setProductCategoryId((Long) skuPriceMap.get("productCategoryId"));
		vo.setProductAttr((String) skuPriceMap.get("productAttr"));
		vo.setSkuCode((String) skuPriceMap.get("skuCode"));
		return vo;
	}

	/**
	 * 计算价格相关字段
	 */
	private void calculatePrices(SkuPriceVO vo, Map<String, Object> skuPriceMap, SkuQuantityInfo quantityInfo, SkuPriceDTO dto) {
		// 1. 提取基础价格信息
		Integer price = ((Number) skuPriceMap.get("price")).intValue();
		Integer priceType = (Integer) skuPriceMap.get("priceType");
		Integer addPrice = ((Number) skuPriceMap.get("addPrice")).intValue();
		String formula = (String) skuPriceMap.get("formula");
		Integer serviceFee = Objects.equals((Integer) skuPriceMap.get("isServiceFee"), IsServiceFeeEnum.YES.getCode())
			? ((Number) skuPriceMap.get("serviceFee")).intValue() : 0;

		// 2. 计算商品单价和销售价
		if (Func.notNull(dto.getProductQuantity())) {
			vo.setProductUnitPrice(PriceCalculator.getPrice(price, priceType, addPrice, formula,
				quantityInfo.packageGrossConversionRate, serviceFee, 1));
		}
		vo.setProductSalePrice(PriceCalculator.getPriceGrossWeight(price, priceType, addPrice, formula,
			quantityInfo.packageGrossConversionRate, serviceFee, BigDecimal.ONE));

		// 3. 计算配送费
		Integer deliveryType = dto.getDeliveryType();
		int deliveryExpense = Objects.equals(deliveryType, DeliveryEnum.PICKUP.getCode())
			? 0
			: PriceCalculator.getDeliveryFee(
			(Integer) skuPriceMap.get("deliveryFeeUnit"),
			1,
			quantityInfo.weightInPounds,
			quantityInfo.packageGrossConversionRate
		);
		vo.setDeliveryExpense(deliveryExpense);

		// 4. 计算总价
		vo.setProductPrice(
			quantityInfo.weightInPounds.multiply(BigDecimal.valueOf(vo.getProductSalePrice()))
				.setScale(0, RoundingMode.HALF_UP)
				.intValueExact()
		);

		// 5. 计算服务费总价
		vo.setServiceExpense(PriceCalculator.getServiceFee(serviceFee, quantityInfo.weightInPounds));
	}

	@Override
	public Map<Long, Integer> getSkuPrice(Long warehouseId, List<Long> skuIds) {
		List<SkuSalePriceVO> skusPriceList = baseMapper.selectSkusPriceMap(warehouseId, skuIds);
		if (Func.isEmpty(skusPriceList)) {
			return Map.of();
		}

		return skusPriceList.stream()
			.collect(Collectors.toMap(SkuSalePriceVO::getSkuId, sku ->
				PriceCalculator.getPriceGrossWeight(
					sku.getPrice(), sku.getPriceType(), sku.getAddPrice(), sku.getFormula(),
					sku.getPackageGrossConversionRate(),
					(IsServiceFeeEnum.YES.getCode().equals(sku.getIsServiceFee()) ? sku.getServiceFee() : 0),
					BigDecimal.ONE))
			);
	}

	/**
	 * SKU数量信息内部类
	 */
	private static class SkuQuantityInfo {
		int productQuantity;
		BigDecimal weightInPounds;
		BigDecimal packageGrossConversionRate;
	}

	/**
	 * 处理单个SKU的变更（不包含退补差处理）
	 */
	private void processSkuChange(IsListManageDTO dto,
								  List<SkuWarehouseRelationEntity> updateEntities,
								  Map<Long, SkuStockEntity> skuMap,
								  //Map<Long, List<SkuOemEntity>> allGeneralPurchaseOemList,
								  List<SkuPpEntity> skuPpEntities,
								  List<ProductPriceEntity> productPriceEntities,
								  Map<Long, SkuPpEntity> allGeneralPurchasePpList,
								  Map<Long, ProductEntity> productMap,
								  Long batchNo,
								  Map<Long, String> baseUnitMap,
								  Date date) {
		// 价格转换与校验
		Integer price = this.convertAndValidatePrice(dto);

		// 如果price为null，表示需要跳过此条处理
		if (price == null) {
			return;
		}

		// 校验SKU库存实体
		SkuStockEntity entity = skuMap.get(dto.getSkuId());
		this.getValidSkuStock(dto.getSkuId(), entity);

		// 生成价格变更记录（目前只考虑专采）
//		if (dto.getSkuFrom() == 0){
//			this.createPriceRecord(dto, dto.getSkuFrom() == 1 ? dto.getWarehouseId() : null,
//				dto.getSkuFrom() == 0 ? dto.getPurchaserId() : null);
//		}

		// 根据SKU来源类型更新相关信息（目前只有专采）
		if (dto.getSkuFrom() == 0){
			this.handlePurchaserSpecial(dto, price, updateEntities, skuPpEntities, allGeneralPurchasePpList, productMap, productPriceEntities, skuMap, batchNo, baseUnitMap, date);
		}
		/*switch (dto.getSkuFrom()) {
			case 0 -> this.handlePurchaserSpecial(dto, price, updateEntities, skuPpEntities, allGeneralPurchasePpList, productMap, productPriceEntities, skuMap, batchNo, baseUnitMap);  // 专采，存放价格变更记录（目前只考虑专采）
			case 1 -> this.handleWarehouseSpecial(dto, price, updateEntities);  // 专供
			case 2 -> this.handleGeneralPurchase(dto, price, updateEntities, allGeneralPurchaseOemList);   // 总仓
			default -> throw new ServiceException("未知SKU来源类型：" + dto.getSkuFrom());
		}*/
	}

	/**
	 * 处理采购员专采逻辑
	 */
	private void handlePurchaserSpecial(IsListManageDTO dto,
										Integer price,
										List<SkuWarehouseRelationEntity> updateEntities,
										List<SkuPpEntity> skuPpEntities,
										Map<Long, SkuPpEntity> allGeneralPurchasePpList,
										Map<Long, ProductEntity> productMap,
										List<ProductPriceEntity> productPriceEntities,
										Map<Long, SkuStockEntity> skuMap,
										Long batchNo,
										Map<Long, String> baseUnitMap,
										Date date) {
		// 1. 更新专采信息
		this.updatePurchaserSpecial(dto, price, allGeneralPurchasePpList, productMap, productPriceEntities, skuMap, batchNo, baseUnitMap, skuPpEntities, date);

		// 2. 更新仓库关联关系
		this.updateWarehouseRelations(dto.getSkuId(), null, price, dto, updateEntities);
	}

	/**
	 * 处理仓库专供逻辑
	 */
	private void handleWarehouseSpecial(IsListManageDTO dto,
										Integer price,
										List<SkuWarehouseRelationEntity> updateEntities) {
		// 1. 更新专供信息
		this.updateWarehouseSpecial(dto, price);

		// 2. 更新仓库关联关系
		this.updateWarehouseRelations(dto.getSkuId(), dto.getWarehouseId(), price, dto, updateEntities);
	}

	/**
	 * 处理总仓采购逻辑
	 */
	private void handleGeneralPurchase(IsListManageDTO dto,
									   Integer price,
									   List<SkuWarehouseRelationEntity> updateEntities,
									   Map<Long, List<SkuOemEntity>> allGeneralPurchaseOemList) {

		// 1. 获取该sku下的所有专供仓库ID
		List<Long> specialWarehouseIds = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(specialWarehouseIds)) {
			List<SkuOemEntity> skuOemList = allGeneralPurchaseOemList.get(dto.getSkuId());
			if (CollectionUtil.isNotEmpty(skuOemList)) {
				specialWarehouseIds = skuOemList.stream().map(SkuOemEntity::getWarehouseId).toList();
			}
		}

		// 2. 处理非专供仓库关联
		LambdaQueryWrapper<SkuWarehouseRelationEntity> query = new LambdaQueryWrapper<SkuWarehouseRelationEntity>()
			.eq(SkuWarehouseRelationEntity::getSkuId, dto.getSkuId())
			.eq(SkuWarehouseRelationEntity::getIsDeleted, 0)
			.notIn(!specialWarehouseIds.isEmpty(), SkuWarehouseRelationEntity::getWarehouseId, specialWarehouseIds);

		List<SkuWarehouseRelationEntity> relations = this.list(query);
		if (CollectionUtil.isEmpty(relations)) return;

		// 3. 批量更新关联关系
		relations.forEach(rel -> {
//			// 获取仓库信息
//			WarehouseEntity warehouse = warehouseService.getById(rel.getWarehouseId());
//			if (warehouse == null) {
//				throw new ServiceException("仓库不存在: " + rel.getWarehouseId());
//			}
//			// 检查仓库营业时间
//			if (this.isWithinBusinessHours(warehouse, LocalTime.now())) {
//				return; // 如果在营业时间内，跳过处理
//			}
			// 校验是否数据有变化，如果无变化则跳过处理
			boolean ifChanged = this.checkIfChangedSkuWarehouse(rel, dto, price);
			if (!ifChanged) {
				return;
			}
			rel.setPrice(price);
			rel.setPriceType(dto.getPriceType());
			rel.setIsList(dto.getIsList());
			rel.setLimitPurchaseQuantity(dto.getLimitPurchaseQuantity());
			updateEntities.add(rel);
			//this.updateById(rel);
//			// 处理临时价格表
//			this.handleTemporaryPrice(rel, dto.getIsList());
		});
	}

	/**
	 * 校验关联关系是否数据有变化
	 */
	private boolean checkIfChangedSkuWarehouse(SkuWarehouseRelationEntity entity, IsListManageDTO dto, Integer price) {
		return checkIfChanged(entity, dto, price,
			SkuWarehouseRelationEntity::getIsList,
			SkuWarehouseRelationEntity::getPrice,
			SkuWarehouseRelationEntity::getLimitPurchaseQuantity,
			SkuWarehouseRelationEntity::getPriceType);
	}

	/**
	 * 更新仓库-SKU关联关系
	 */
	private void updateWarehouseRelations(Long skuId,
										  Long warehouseId,
										  Integer price,
										  IsListManageDTO dto,
										  List<SkuWarehouseRelationEntity> updateEntities) {
		LambdaQueryWrapper<SkuWarehouseRelationEntity> query = new LambdaQueryWrapper<SkuWarehouseRelationEntity>()
			.eq(SkuWarehouseRelationEntity::getSkuId, skuId)
			.eq(warehouseId != null, SkuWarehouseRelationEntity::getWarehouseId, warehouseId)
			.eq(SkuWarehouseRelationEntity::getIsDeleted, 0);

		list(query).forEach(rel -> {
//			// 获取采购员对应的仓库
//			WarehouseEntity warehouse = warehouseService.getById(rel.getWarehouseId());
//			if (warehouse == null) {
//				throw new ServiceException("未找到对应的仓库信息");
//			}

//			// 检查仓库营业时间
//			if (this.isWithinBusinessHours(warehouse, LocalTime.now())) {
//				return; // 如果在营业时间内，跳过处理
//			}
			// 校验是否数据有变化，如果无变化则跳过处理
			boolean ifChanged = this.checkIfChangedSkuWarehouse(rel, dto, price);
			if (!ifChanged) {
				return;
			}
			rel.setPrice(price);
			rel.setPriceType(dto.getPriceType());
			rel.setIsList(dto.getIsList());
			// 如果限制购买量有变化，则重置已售出数量为0
			Integer limitPurchaseQuantityOld = rel.getLimitPurchaseQuantity();
			if (!Objects.equals(limitPurchaseQuantityOld, dto.getLimitPurchaseQuantity())) {
				rel.setSold(0);
			}
			rel.setLimitPurchaseQuantity(dto.getLimitPurchaseQuantity());
			//this.updateById(rel);
			updateEntities.add(rel);
//			// 处理临时价格表
//			this.handleTemporaryPrice(rel, isList);
		});
	}

	/**
	 * 处理临时价格表数据
	 *
	 * @param relation SKU仓库关联实体
	 * @param isList   上下架状态
	 */
	private void handleTemporaryPrice(SkuWarehouseRelationEntity relation, Integer isList) {
		// 查询是否存在临时价格记录
		SkuWarehousePriceTemporaryEntity temporaryPrice = skuWarehousePriceTemporaryService.getById(relation.getId());

		if (isList == 0) { // 下架
			if (temporaryPrice != null) {
				// 物理删除
				skuWarehousePriceTemporaryService.physicalDeletionById(relation.getId());
			}
		} else { // 上架
			if (temporaryPrice == null) {
				// 新增记录
				SkuWarehousePriceTemporaryEntity newTemporary = new SkuWarehousePriceTemporaryEntity();
				newTemporary.setId(relation.getId());
				newTemporary.setWarehouseId(relation.getWarehouseId());
				newTemporary.setSkuId(relation.getSkuId());
				skuWarehousePriceTemporaryService.save(newTemporary);
			}
		}
	}

	/**
	 * 检查所有仓库是否都在营业时间之外
	 */
	@Override
	public boolean checkAllWarehouseIsOutOfBusinessTime() {
		// 1. 获取当前时间
		LocalTime currentTime = LocalTime.now();

		// 2. 查询所有有效的仓库（状态为1且未删除，且有营业时间设置）
		List<WarehouseEntity> validWarehouses = warehouseService.list(new LambdaQueryWrapper<WarehouseEntity>()
			.eq(WarehouseEntity::getStatus, 1)
			.eq(WarehouseEntity::getIsDeleted, 0)
			.isNotNull(WarehouseEntity::getBusinessStartTime)
			.isNotNull(WarehouseEntity::getBusinessEndTime));

		// 3. 如果没有有效的仓库，返回true（认为都在营业时间之外）
		if (CollectionUtil.isEmpty(validWarehouses)) {
			return true;
		}

		// 4. 检查所有仓库是否都在营业时间之外
		return validWarehouses.stream()
			.noneMatch(warehouse -> this.isWithinBusinessHours(warehouse, currentTime));
	}

	/**
	 * 获取仓库入库时各sku的询价
	 */
	@Override
	public Map<Long, WarehouseStorePrice> getManagerWarehouseStorePrices(Long warehouseId, List<Long> skuIds) {
		// 参数校验
		if (warehouseId == null || skuIds == null || skuIds.isEmpty()) {
			return new HashMap<>();
		}

		Map<Long, WarehouseStorePrice> resultMap = new HashMap<>();
		List<Long> remainingSkuIds = new ArrayList<>(skuIds);

		// 第一优先级：查询chy_sku_warehouse_relation中warehouse_id=warehouseId及sku_id=skuId的记录
		if (!remainingSkuIds.isEmpty()) {
			List<WarehouseStorePrice> firstPriorityResults = baseMapper.batchQueryByWarehouseAndSku(warehouseId, remainingSkuIds);
			for (WarehouseStorePrice priceInfo : firstPriorityResults) {
				resultMap.put(priceInfo.getSkuId(), priceInfo);
				remainingSkuIds.remove(priceInfo.getSkuId());
			}
		}

		// 第二优先级：查询chy_warehouse_purchase中warehouse_id=warehouseId及role_type=1的purchaser_id，
		// 然后查询chy_sku_pp中purchaser_id=purchaser_id及sku_id=skuId
		if (!remainingSkuIds.isEmpty()) {
			Long purchaserId = baseMapper.getPurchaserIdByWarehouse(warehouseId);
			if (purchaserId != null) {
				List<WarehouseStorePrice> secondPriorityResults = baseMapper.batchQueryPpByPurchaserAndSku(purchaserId, remainingSkuIds);
				for (WarehouseStorePrice priceInfo : secondPriorityResults) {
					resultMap.put(priceInfo.getSkuId(), priceInfo);
					remainingSkuIds.remove(priceInfo.getSkuId());
				}
			}
		}

		// 第三优先级：查询chy_sku_oem中warehouse_id=warehouseId及sku_id=skuId
		if (!remainingSkuIds.isEmpty()) {
			List<WarehouseStorePrice> thirdPriorityResults = baseMapper.batchQueryOemByWarehouseAndSku(warehouseId, remainingSkuIds);
			for (WarehouseStorePrice priceInfo : thirdPriorityResults) {
				resultMap.put(priceInfo.getSkuId(), priceInfo);
				remainingSkuIds.remove(priceInfo.getSkuId());
			}
		}

		// 第四优先级：查询chy_sku_warehouse_relation中sku_id=skuId的记录，取第一条
		if (!remainingSkuIds.isEmpty()) {
			List<WarehouseStorePrice> fourthPriorityResults = baseMapper.batchQueryAnySku(remainingSkuIds);
			for (WarehouseStorePrice priceInfo : fourthPriorityResults) {
				resultMap.put(priceInfo.getSkuId(), priceInfo);
				remainingSkuIds.remove(priceInfo.getSkuId());
			}
		}

		// 如果还有SKU没有找到价格信息，则报错
//		if (!remainingSkuIds.isEmpty()) {
//			throw new ServiceException("以下SKU未找到价格信息：" + remainingSkuIds);
//		}

		return resultMap;
	}

	/**
	 * 查询某采购可询价的所有skuId
	 */
	@Override
	public List<Long> listRelationSkuId(Long purchaserId) {
		if (ObjectUtil.isEmpty(purchaserId)) {
			purchaserId = AuthUtil.getUserId();
		}
		WarehouseEntity warehouse = warehouseService.getByUserId(purchaserId);
		if (ObjectUtil.isEmpty(warehouse)) {
			throw new ServiceException("未获取到仓库信息");
		}
		Long warehouseId = warehouse.getId();
		Integer warehouseType = warehouse.getWarehouseType();
		boolean isGeneralWarehouse = WarehouseTypeEnum.isTotalWarehouse(warehouseType);
		List<Long> result = new ArrayList<>();

		// 获取所有审核状态为「通过」的SKU ID集合（用于后续过滤）
		Set<Long> approvedSkuIds = this.getApprovedSkuIds();

		// 获取所有专采skuId
		Set<Long> ppAllSkuIds = skuPpService.list(new QueryWrapper<SkuPpEntity>().lambda()
				.eq(SkuPpEntity::getIsDeleted, 0))
			.stream()
			.map(SkuPpEntity::getSkuId)
			.collect(Collectors.toSet());

		// 添加专采skuId
		Set<Long> exclusivePurchaseSkuIds = this.addSkuPpIds(result, purchaserId, approvedSkuIds);

		// 添加专供skuId
		Set<Long> exclusiveSupplySkuIds = this.addSkuOemIds(result, warehouseId, approvedSkuIds, ppAllSkuIds);

		// 添加总仓skuId
		if (isGeneralWarehouse) {
			this.addGeneralWarehouseSkuIds(approvedSkuIds, exclusiveSupplySkuIds, ppAllSkuIds, result);
		}

		return result;
	}

	@Override
	public List<SkuWarehouseRelationEntity> getWarehouseBySkuIds(List<Long> ids) {
		return baseMapper.getWarehouseBySkuIds(ids);
	}

	private void addGeneralWarehouseSkuIds(Set<Long> approvedSkuIds, Set<Long> exclusiveSupplySkuIds, Set<Long> ppAllSkuIds, List<Long> result) {
		// 获取总仓所有SKU并过滤
		Set<Long> allSkuIds = skuStockService.list(new QueryWrapper<SkuStockEntity>().lambda()
				.eq(SkuStockEntity::getIsDeleted, 0))
			.stream()
			.map(SkuStockEntity::getId)
			.filter(approvedSkuIds::contains)
			.collect(Collectors.toSet());

		// 排除所有专采SKU
		allSkuIds.removeAll(ppAllSkuIds);

		// 排除本仓库专供sku
		allSkuIds.removeAll(exclusiveSupplySkuIds);

		result.addAll(allSkuIds);
	}

	private Set<Long> addSkuOemIds(List<Long> result, Long warehouseId, Set<Long> approvedSkuIds, Set<Long> ppAllSkuIds) {
		List<SkuOemEntity> skuOemList = skuOemService.list(new QueryWrapper<SkuOemEntity>().lambda()
				.eq(SkuOemEntity::getWarehouseId, warehouseId)
				.eq(SkuOemEntity::getIsDeleted, 0))
			.stream()
			.filter(e -> approvedSkuIds.contains(e.getSkuId()) && !ppAllSkuIds.contains(e.getSkuId()))
			.toList();

		if (CollectionUtil.isNotEmpty(skuOemList)) {
			Set<Long> skuIds = skuOemList.stream().map(SkuOemEntity::getSkuId).collect(Collectors.toSet());
			result.addAll(skuIds);
			return skuIds;
		} else {
			return Collections.emptySet();
		}
	}

	private Set<Long> addSkuPpIds(List<Long> result, Long purchaserId, Set<Long> approvedSkuIds) {
		List<SkuPpEntity> skuPpList = skuPpService.list(new QueryWrapper<SkuPpEntity>().lambda()
				.eq(SkuPpEntity::getPurchaserId, purchaserId)
				.eq(SkuPpEntity::getIsDeleted, 0))
			.stream()
			.filter(e -> approvedSkuIds.contains(e.getSkuId()))
			.toList();

		if (CollectionUtil.isNotEmpty(skuPpList)) {
			Set<Long> skuIds = skuPpList.stream().map(SkuPpEntity::getSkuId).collect(Collectors.toSet());
			result.addAll(skuIds);
			return skuIds;
		} else {
			return Collections.emptySet();
		}
	}

}
