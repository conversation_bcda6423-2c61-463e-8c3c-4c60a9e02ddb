/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.warehouse.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springblade.common.enums.WarehouseTypeEnum;
import org.springblade.common.utills.CommonUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.cust.mapper.CustMapper;
import org.springblade.modules.business.cust.pojo.entity.CustEntity;
import org.springblade.modules.business.product.pojo.entity.SkuOemEntity;
import org.springblade.modules.business.product.pojo.entity.SkuWarehouseRelationEntity;
import org.springblade.modules.business.product.service.ISkuOemService;
import org.springblade.modules.business.warehouse.excel.WarehouseExcel;
import org.springblade.modules.business.warehouse.excel.WarehouseImportExcel;
import org.springblade.modules.business.warehouse.mapper.WarehouseMapper;
import org.springblade.modules.business.warehouse.mapper.WarehousePurchaseMapper;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseJobDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseTreeDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.entity.WarehousePurchaseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.*;
import org.springblade.modules.business.warehouse.service.IWarehousePurchaseService;
import org.springblade.modules.business.warehouse.service.IWarehouseService;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.excel.EasyExcel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仓库信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
public class WarehouseServiceImpl extends BaseServiceImpl<WarehouseMapper, WarehouseEntity> implements IWarehouseService {

	@Autowired
	private WarehousePurchaseMapper purchaseMapper;

	@Autowired
	private ISkuOemService skuOemService;

	@Autowired
	private IWarehousePurchaseService  purchaseService;

	@Autowired
	private CustMapper custMapper;

	@Override
	public IPage<WarehouseVO> selectWarehousePage(IPage<WarehouseVO> page, WarehouseVO warehouse) {
		return page.setRecords(baseMapper.selectWarehousePage(page, warehouse));
	}

	@Override
	public List<WarehouseExcel> exportWarehouse(Wrapper<WarehouseEntity> queryWrapper) {
		List<WarehouseExcel> warehouseList = baseMapper.exportWarehouse(queryWrapper);
		//warehouseList.forEach(warehouse -> {
		//	warehouse.setTypeName(DictCache.getValue(DictEnum.YES_NO, WarehouseEntity.getType()));
		//});
		return warehouseList;
	}

	@Override
	public List<TreeNode> warehouseTree(WarehouseTreeDTO dto) {
		// 如果请求只返回总仓数据，则设置查询条件
		if (Func.isNotEmpty(dto.getIsTotalWarehouse()) && dto.getIsTotalWarehouse()) {
			// 根据导入逻辑，总仓类型为2，与WarehouseTypeEnum.CITY的值对应
			dto.setWarehouseType(WarehouseTypeEnum.CITY.getCode());
		}

		if (Func.isEmpty(dto.getWarehouseName())) {
			return ForestNodeMerger.merge(baseMapper.warehouseTree(dto));
		} else {
			return baseMapper.warehouseTree(dto);
		}
	}

	@Override
	public List<WarehouseListTreeVO> warehouseListTree(WarehouseTreeDTO dto) {

		if (Func.isNotEmpty(dto.getUserId())) {
			QueryWrapper<WarehousePurchaseEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("purchaser_id", dto.getUserId());
			List<WarehousePurchaseEntity> list = purchaseMapper.selectList(queryWrapper);
			if (list.size() > 0) {
				dto.setIds(list.stream().map(WarehousePurchaseEntity::getWarehouseId).distinct().collect(Collectors.toList()));
			}
		}
		List<WarehouseListTreeVO> temp = baseMapper.warehouseListTree(dto);
		for (WarehouseListTreeVO treeVO : temp) {
			treeVO.setServiceFee(CommonUtil.ConvertToBigDecimal(treeVO.getServiceFee()));
			treeVO.setDeliveryFee(CommonUtil.ConvertToBigDecimal(treeVO.getDeliveryFee()));
		}
		if (Func.isEmpty(dto.getWarehouseName()) || Func.isEmpty(dto.getUserId())
			|| Func.isEmpty(dto.getBusinessStartTime()) || Func.isEmpty(dto.getBusinessEndTime())) {
			return ForestNodeMerger.merge(temp);
		} else {
			return temp;
		}
	}

	@Override
	public List<WarehouseEntity> getWareHouseByIds(List<Long> wareHouseIds) {
		return baseMapper.getWareHouseByIds(wareHouseIds);
	}

	@Override
	public List<WarehouseJobVO> getWarehouseCutList() {
		return baseMapper.getWarehouseCutList();
	}

	@Override
	public void saveJob(WarehouseJobDTO job) {
		JobCountVO jobCountVO = baseMapper.selectJob(job);
		if (jobCountVO.getNum().equals(0)){
			baseMapper.saveJob(job);
		}
	}

	@Override
	public void updateCutTimeJob(List<Long> warehouseIdList) {
		baseMapper.updateCutTimeJob(warehouseIdList);
	}

	@Override
	public Map<Long, String> listNameByIds(List<Long> warehouseIdList) {
		if (Func.isEmpty(warehouseIdList)) {
			return Map.of();
		}
		return list(Wrappers.<WarehouseEntity>lambdaQuery()
			.select(WarehouseEntity::getId, WarehouseEntity::getWarehouseName)
			.in(WarehouseEntity::getId, warehouseIdList))
			.stream()
			.collect(Collectors.toMap(WarehouseEntity::getId, WarehouseEntity::getWarehouseName));
	}

	@Override
	public WarehouseEntity getByUserId(Long userId) {
		Assert.isTrue(userId > 0, () -> new ServiceException("登录已失效，请重新登录"));
		return baseMapper.getByUserId(userId, null);
	}

	@Override
	public WarehouseEntity getByUserIdPurchase(Long userId) {
		Assert.isTrue(userId > 0, () -> new ServiceException("登录已失效，请重新登录"));
		return baseMapper.getByUserId(userId, 1);
	}

	/**
	 * 获取仓库名称
	 *
	 * @param id
	 */
	@Override
	public String getWarehouseName(Long id) {
		return Optional.ofNullable(id)
			.flatMap(idValue -> Optional.ofNullable(getById(idValue)))
			.map(WarehouseEntity::getWarehouseName)
			.orElse("");
	}

	@Override
	public Long getPurchaserIdByWarehouseId(Long warehouseId) {
		return baseMapper.getPurchaserIdByWarehouseId(warehouseId);
	}

	@Override
	public List<WarehouseListVO> warehouseList(WarehouseTreeDTO dto) {

		return baseMapper.warehouseList(dto);
	}

	@Override
	public List<TreeNode> permissionWarehouseTree() {
		if (AuthUtil.isAdmin()) {
			return ForestNodeMerger.merge(baseMapper.warehouseTree(new WarehouseTreeDTO()));
		}

		WarehouseEntity warehouse = this.getByUserId(AuthUtil.getUserId());
		if (Func.isNull(warehouse)) {
			throw new ServiceException("当前用户未关联仓库");
		}

		// 如果关联总仓，则允许操作所有仓库
		if (WarehouseTypeEnum.isTotalWarehouse(warehouse.getWarehouseType())) {
			return ForestNodeMerger.merge(baseMapper.warehouseTree(new WarehouseTreeDTO()));
		}

		TreeNode node = new TreeNode();
		node.setId(warehouse.getId());
		node.setKey(warehouse.getId());
		node.setValue(warehouse.getId());
		node.setTitle(warehouse.getWarehouseName());
		node.setHasChildren(false);
		return Lists.newArrayList(node);
	}

	@Override
	public List<WarehouseEntity> getGeneralWarehouses() {
		return this.list(new LambdaQueryWrapper<WarehouseEntity>()
				.eq(WarehouseEntity::getWarehouseType, WarehouseTypeEnum.CITY.getCode())
				.eq(WarehouseEntity::getStatus, 1)
				.eq(WarehouseEntity::getIsDeleted, 0));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importWarehouse(MultipartFile file) {
		try {
			List<WarehouseImportExcel> list = EasyExcel.read(file.getInputStream())
				.head(WarehouseImportExcel.class)
				.sheet()
				.doReadSync();

			if (CollUtil.isEmpty(list)) {
				throw new ServiceException("导入数据为空");
			}

			// 查询上级仓库
			WarehouseEntity parentWarehouse = this.getOne(new LambdaQueryWrapper<WarehouseEntity>()
				.like(WarehouseEntity::getWarehouseName, "重庆总仓")
				.eq(WarehouseEntity::getIsDeleted, 0));

			if (parentWarehouse == null) {
				throw new ServiceException("未找到上级仓库'重庆总仓'");
			}

			List<WarehouseEntity> warehouseList = new ArrayList<>();
			for (WarehouseImportExcel excel : list) {
				WarehouseEntity warehouse = new WarehouseEntity();
				// 设置仓库名称
				warehouse.setWarehouseName(excel.getWarehouseName());
				// 设置上级仓库ID
				warehouse.setParentId(parentWarehouse.getId());

				// 设置仓库类型（根据Excel中的文字内容转换）
				String warehouseTypeStr = excel.getWarehouseType();
				if (warehouseTypeStr != null) {
					switch (warehouseTypeStr.trim()) {
						case "全国仓":
							warehouse.setWarehouseType(1); // 全国仓
							break;
						case "总仓":
							warehouse.setWarehouseType(2); // 总仓
							break;
						case "分仓":
							warehouse.setWarehouseType(3); // 分仓
							break;
						default:
							throw new ServiceException("仓库类型[" + warehouseTypeStr + "]不正确，请检查Excel数据");
					}
				}

				// 设置服务费（转换为分）
				if (excel.getServiceFee() != null) {
					warehouse.setServiceFee(CommonUtil.ConvertBigDecimalInt(excel.getServiceFee()));
				}

				// 设置配送费（转换为分）
				if (excel.getDeliveryFee() != null) {
					warehouse.setDeliveryFee(CommonUtil.ConvertBigDecimalInt(excel.getDeliveryFee()));
				}

				// 处理营业时间
				String businessTime = excel.getBusinessTime();
				if (businessTime != null) {
					String[] timeRange = businessTime.split("-");
					if (timeRange.length == 2) {
						// 处理开始时间，将中文冒号替换为英文冒号
						String startTime = timeRange[0].trim().replace("：", ":");
						// 处理结束时间，将中文冒号替换为英文冒号
						String endTime = timeRange[1].trim().replace("：", ":");

						warehouse.setBusinessStartTime(startTime);
						warehouse.setBusinessEndTime(endTime);
					}
				}

				// 设置截单时间
				if (excel.getCutTime() != null) {
					warehouse.setCutTime(excel.getCutTime().replace("：", ":"));
				}

				// 设置公式
				warehouse.setFormula(excel.getFormula());

				warehouseList.add(warehouse);
			}

			// 批量保存
			this.saveBatch(warehouseList);
		} catch (Exception e) {
			throw new ServiceException("导入失败：" + e.getMessage());
		}
	}

	@Override
	public boolean removeWarehouses(List<Long> longList) {
		//判断创库是否被用户信息引用
		QueryWrapper<CustEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.in("warehouse_id",longList);
		long count = custMapper.selectList(queryWrapper).stream().count();
		if (count > 0){
			throw new ServiceException("存在用户信息引用，请先处理用户信息");
		}

		longList.forEach(warehouseId -> {
			// 删除仓库
			this.removeById(warehouseId);
			// 删除专供
			SkuOemEntity skuOem = skuOemService.getOne(new LambdaQueryWrapper<SkuOemEntity>()
				.eq(SkuOemEntity::getWarehouseId, warehouseId)
			);
			if (skuOem != null) {
				skuOemService.removeById(skuOem.getId());
			}
			// 删除仓库采购关联表数据
			WarehousePurchaseEntity purchase = purchaseService.getOne(new LambdaQueryWrapper<WarehousePurchaseEntity>()
				.eq(WarehousePurchaseEntity::getWarehouseId, warehouseId)
			);
			if (purchase != null) {
				purchaseService.removeById(purchase.getId());
			}
		});
		return true;
	}

	@Override
	public List<WarehouseJobVO> getWarehouseAddCutList(List<Long> warehouseIds) {
		return baseMapper.getWarehouseAddCutList(warehouseIds);
	}
	/**
	 * 获取总仓库
	 */
	@Override
	public WarehouseEntity getTotalWarehouse(){
		//获取总仓的值
		return this.getOne(
			Wrappers.<WarehouseEntity>lambdaQuery()
				.eq(WarehouseEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED)
				.eq(WarehouseEntity::getStatus, BladeConstant.DB_STATUS_1)
				.eq(WarehouseEntity::getWarehouseType, WarehouseTypeEnum.CITY.getCode())
				.last("limit1")
		);
	}

	@Override
	public List<WarehouseCutTimeListVO> getWarehouseCutTimeList() {
		List<WarehouseEntity> list = super.list();
		List<String> cutTimeList = list.stream().map(WarehouseEntity::getCutTime).distinct().toList();
		//对cutTimeLis排序
		cutTimeList = cutTimeList.stream().sorted().toList();
		List<WarehouseCutTimeListVO> temp=new  ArrayList<>();
		for (String cutTime:cutTimeList) {
			WarehouseCutTimeListVO vo=new WarehouseCutTimeListVO();
			vo.setCutTime(cutTime);
			vo.setList(list.stream().filter(warehouse -> warehouse.getCutTime().equals(cutTime)).map(warehouse -> {
				WarehouseCutTimeListVO entity=new WarehouseCutTimeListVO();
				entity.setCutTime(cutTime);
				entity.setWarehouseId(warehouse.getId());
				entity.setWarehouseName(warehouse.getWarehouseName());
				return entity;
			}).toList());
			temp.add(vo);
		}
		return temp;
	}
}
