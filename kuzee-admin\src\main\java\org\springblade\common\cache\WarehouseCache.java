//package org.springblade.common.cache;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import org.springblade.core.cache.utils.CacheUtil;
//import org.springblade.core.tool.utils.SpringUtil;
//import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
//import org.springblade.modules.business.warehouse.service.IWarehouseService;
//
//import java.util.Optional;
//
//
//public class WarehouseCache {
//
//	private static final String WAR_CACHE = "chy:war";
//	private static final String WAREHOUSE_ID = "warehouse:id:";
//
//	private static final IWarehouseService warehouseService;
//
//	static {
//		warehouseService = SpringUtil.getBean(IWarehouseService.class);
//	}
//
//	public static WarehouseEntity getById(Long id) {
//		return CacheUtil.get(WAR_CACHE, WAREHOUSE_ID, id, () -> warehouseService.getOne(Wrappers.<WarehouseEntity>lambdaQuery()
//			.select(WarehouseEntity::getId, WarehouseEntity::getParentId, WarehouseEntity::getWarehouseName, WarehouseEntity::getWarehouseType,
//				WarehouseEntity::getServiceFee, WarehouseEntity::getDeliveryFee, WarehouseEntity::getBusinessStartTime, WarehouseEntity::getBusinessEndTime)
//			.eq(WarehouseEntity::getId, id)), Boolean.FALSE);
//	}
//
//	public static String getNameById(Long id) {
//		return Optional.ofNullable(getById(id)).map(WarehouseEntity::getWarehouseName).orElse(null);
//	}
//}
