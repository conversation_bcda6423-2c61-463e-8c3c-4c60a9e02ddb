/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.warehouse.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.core.tool.node.TreeNode;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseJobDTO;
import org.springblade.modules.business.warehouse.pojo.dto.WarehouseTreeDTO;
import org.springblade.modules.business.warehouse.pojo.entity.WarehouseEntity;
import org.springblade.modules.business.warehouse.pojo.vo.*;
import org.springblade.modules.business.warehouse.excel.WarehouseExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.system.pojo.entity.User;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 仓库信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface IWarehouseService extends BaseService<WarehouseEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param warehouse 查询参数
	 * @return IPage<WarehouseVO>
	 */
	IPage<WarehouseVO> selectWarehousePage(IPage<WarehouseVO> page, WarehouseVO warehouse);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<WarehouseExcel>
	 */
	List<WarehouseExcel> exportWarehouse(Wrapper<WarehouseEntity> queryWrapper);

    List<TreeNode> warehouseTree(WarehouseTreeDTO dto);

	List<WarehouseListTreeVO> warehouseListTree(WarehouseTreeDTO dto);

    List<WarehouseEntity> getWareHouseByIds(List<Long> wareHouseIds);

    List<WarehouseJobVO> getWarehouseCutList();

    void saveJob(WarehouseJobDTO job);

	void updateCutTimeJob(List<Long> warehouseIdList);

    Map<Long, String> listNameByIds(List<Long> warehouseIdList);

	/**
	 * 获取当前后台用户关联仓库
	 */
	WarehouseEntity getByUserId(Long userId);
	/**
	 * 获取当前后台用户关联仓库 采购员
	 */
	WarehouseEntity getByUserIdPurchase(Long userId);
	/**
	 * 获取仓库名称
	 * @param id
	 */
	String getWarehouseName(Long id);

	Long getPurchaserIdByWarehouseId(Long warehouseId);

	List<WarehouseListVO> warehouseList(WarehouseTreeDTO dto);

	/**
	 * 根据权限获取仓库
	 */
	List<TreeNode> permissionWarehouseTree();

	List<WarehouseEntity> getGeneralWarehouses();

	/**
	 * 导入仓库数据
	 *
	 * @param file Excel文件
	 */
	void importWarehouse(MultipartFile file);

    boolean removeWarehouses(List<Long> longList);

	List<WarehouseJobVO> getWarehouseAddCutList(List<Long> warehouseIds);

	/**
	 * 获取总仓库
	 */
	WarehouseEntity getTotalWarehouse();

	List<WarehouseCutTimeListVO> getWarehouseCutTimeList();
}
