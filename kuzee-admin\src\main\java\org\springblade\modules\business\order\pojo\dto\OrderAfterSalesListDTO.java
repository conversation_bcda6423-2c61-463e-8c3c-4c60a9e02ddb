package org.springblade.modules.business.order.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OrderAfterSalesListDTO {

	@Schema(description = "客户名称/电话")
	private String keywords;

	//售后状态
	@Schema(description = "售后状态")
	private String status;

	//订单号
	@Schema(description = "订单号")
	private String orderNo;

	//售后日期开始
	@Schema(description = "售后日期开始")
	private String beginDate;

	//售后日期结束
	@Schema(description = "售后日期结束")
	private String endDate;
}
