<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustWalletMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custWalletResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustWalletEntity">
        <result column="cust_id" property="custId"/>
        <result column="balance" property="balance"/>
        <result column="freeze_balance" property="freezeBalance"/>
        <result column="total_expense" property="totalExpense"/>
        <result column="total_recharge" property="totalRecharge"/>
    </resultMap>

    <select id="selectCustWalletPage" resultMap="custWalletResultMap">
        select * from chy_cust_wallet where is_deleted = 0
    </select>

    <select id="exportCustWallet" resultType="org.springblade.modules.business.cust.excel.CustWalletExcel">
        SELECT * FROM chy_cust_wallet ${ew.customSqlSegment}
    </select>

</mapper>
