<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custRoleResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustRoleEntity">
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
    </resultMap>

    <select id="selectCustRolePage" resultMap="custRoleResultMap">
        select * from chy_cust_role where is_deleted = 0
    </select>

    <select id="exportCustRole" resultType="org.springblade.modules.business.cust.excel.CustRoleExcel">
        SELECT * FROM chy_cust_role ${ew.customSqlSegment}
    </select>

</mapper>
