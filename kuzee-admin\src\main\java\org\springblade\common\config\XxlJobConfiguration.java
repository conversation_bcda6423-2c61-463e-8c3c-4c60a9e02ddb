//package org.springblade.common.config;
//
//import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * xxl-job 配置
// */
//@Slf4j
//@Configuration
//@EnableConfigurationProperties(XxlJobProperties.class)
//public class XxlJobConfiguration {
//
//	@Bean
//	public XxlJobSpringExecutor xxlJobExecutor(XxlJobProperties properties) {
//		log.info(">>>>>>>>>>> xxl-job config init.");
//		XxlJobProperties.Executor executor = properties.getExecutor();
//		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
//		xxlJobSpringExecutor.setAdminAddresses(properties.getAdmin().getAddresses());
//		xxlJobSpringExecutor.setAppname(executor.getAppname());
//		xxlJobSpringExecutor.setIp(executor.getIp());
//		xxlJobSpringExecutor.setPort(executor.getPort());
//		xxlJobSpringExecutor.setAccessToken(properties.getAccessToken());
//		xxlJobSpringExecutor.setLogPath(executor.getLogpath());
//		xxlJobSpringExecutor.setLogRetentionDays(executor.getLogretentiondays());
//		return xxlJobSpringExecutor;
//	}
//}
