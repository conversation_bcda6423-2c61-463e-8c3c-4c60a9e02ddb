/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.cust.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.business.cust.pojo.entity.CustWalletTradeRecordEntity;
import org.springblade.modules.business.cust.pojo.vo.CustWalletTradeRecordVO;
import org.springblade.modules.business.cust.excel.CustWalletTradeRecordExcel;
import org.springblade.modules.business.cust.wrapper.CustWalletTradeRecordWrapper;
import org.springblade.modules.business.cust.service.ICustWalletTradeRecordService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 客户钱包交易记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/custWalletTradeRecord")
@Tag(name = "客户钱包交易记录表", description = "客户钱包交易记录表接口")
public class CustWalletTradeRecordController extends BladeController {

	private final ICustWalletTradeRecordService custWalletTradeRecordService;

	/**
	 * 客户钱包交易记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入custWalletTradeRecord")
	public R<CustWalletTradeRecordVO> detail(CustWalletTradeRecordEntity custWalletTradeRecord) {
		CustWalletTradeRecordEntity detail = custWalletTradeRecordService.getOne(Condition.getQueryWrapper(custWalletTradeRecord));
		return R.data(CustWalletTradeRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 客户钱包交易记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入custWalletTradeRecord")
	public R<IPage<CustWalletTradeRecordVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> custWalletTradeRecord, Query query) {
		IPage<CustWalletTradeRecordEntity> pages = custWalletTradeRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(custWalletTradeRecord, CustWalletTradeRecordEntity.class));
		return R.data(CustWalletTradeRecordWrapper.build().pageVO(pages));
	}


	/**
	 * 客户钱包交易记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入custWalletTradeRecord")
	public R<IPage<CustWalletTradeRecordVO>> page(CustWalletTradeRecordVO custWalletTradeRecord, Query query) {
		IPage<CustWalletTradeRecordVO> pages = custWalletTradeRecordService.selectCustWalletTradeRecordPage(Condition.getPage(query), custWalletTradeRecord);
		return R.data(pages);
	}

	/**
	 * 客户钱包交易记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入custWalletTradeRecord")
	public R save(@Valid @RequestBody CustWalletTradeRecordEntity custWalletTradeRecord) {
		return R.status(custWalletTradeRecordService.save(custWalletTradeRecord));
	}

	/**
	 * 客户钱包交易记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入custWalletTradeRecord")
	public R update(@Valid @RequestBody CustWalletTradeRecordEntity custWalletTradeRecord) {
		return R.status(custWalletTradeRecordService.updateById(custWalletTradeRecord));
	}

	/**
	 * 客户钱包交易记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入custWalletTradeRecord")
	public R submit(@Valid @RequestBody CustWalletTradeRecordEntity custWalletTradeRecord) {
		return R.status(custWalletTradeRecordService.saveOrUpdate(custWalletTradeRecord));
	}

	/**
	 * 客户钱包交易记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(custWalletTradeRecordService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-custWalletTradeRecord")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "导出数据", description  = "传入custWalletTradeRecord")
	public void exportCustWalletTradeRecord(@Parameter(hidden = true) @RequestParam Map<String, Object> custWalletTradeRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<CustWalletTradeRecordEntity> queryWrapper = Condition.getQueryWrapper(custWalletTradeRecord, CustWalletTradeRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(CustWalletTradeRecordEntity::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(CustWalletTradeRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<CustWalletTradeRecordExcel> list = custWalletTradeRecordService.exportCustWalletTradeRecord(queryWrapper);
		ExcelUtil.export(response, "客户钱包交易记录表数据" + DateUtil.time(), "客户钱包交易记录表数据表", list, CustWalletTradeRecordExcel.class);
	}

}
