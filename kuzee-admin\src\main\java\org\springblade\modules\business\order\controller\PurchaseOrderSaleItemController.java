/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.order.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.business.order.pojo.entity.PurchaseOrderSaleItemEntity;
import org.springblade.modules.business.order.pojo.vo.PurchaseOrderSaleItemVO;
import org.springblade.modules.business.order.excel.PurchaseOrderSaleItemExcel;
import org.springblade.modules.business.order.wrapper.PurchaseOrderSaleItemWrapper;
import org.springblade.modules.business.order.service.IPurchaseOrderSaleItemService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 采购详情关联订单详情表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/purchaseOrderSaleItem")
@Tag(name = "采购详情关联订单详情表", description = "采购详情关联订单详情表接口")
public class PurchaseOrderSaleItemController extends BladeController {

	private final IPurchaseOrderSaleItemService purchaseOrderSaleItemService;

//	/**
//	 * 采购详情关联订单详情表 详情
//	 */
//	@GetMapping("/detail")
//	@ApiOperationSupport(order = 1)
//	@Operation(summary = "详情", description  = "传入purchaseOrderSaleItem")
//	public R<PurchaseOrderSaleItemVO> detail(PurchaseOrderSaleItemEntity purchaseOrderSaleItem) {
//		PurchaseOrderSaleItemEntity detail = purchaseOrderSaleItemService.getOne(Condition.getQueryWrapper(purchaseOrderSaleItem));
//		return R.data(PurchaseOrderSaleItemWrapper.build().entityVO(detail));
//	}
//
//	/**
//	 * 采购详情关联订单详情表 分页
//	 */
//	@GetMapping("/list")
//	@ApiOperationSupport(order = 2)
//	@Operation(summary = "分页", description  = "传入purchaseOrderSaleItem")
//	public R<IPage<PurchaseOrderSaleItemVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> purchaseOrderSaleItem, Query query) {
//		IPage<PurchaseOrderSaleItemEntity> pages = purchaseOrderSaleItemService.page(Condition.getPage(query), Condition.getQueryWrapper(purchaseOrderSaleItem, PurchaseOrderSaleItemEntity.class));
//		return R.data(PurchaseOrderSaleItemWrapper.build().pageVO(pages));
//	}
//
//
//	/**
//	 * 采购详情关联订单详情表 自定义分页
//	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 3)
//	@Operation(summary = "分页", description  = "传入purchaseOrderSaleItem")
//	public R<IPage<PurchaseOrderSaleItemVO>> page(PurchaseOrderSaleItemVO purchaseOrderSaleItem, Query query) {
//		IPage<PurchaseOrderSaleItemVO> pages = purchaseOrderSaleItemService.selectPurchaseOrderSaleItemPage(Condition.getPage(query), purchaseOrderSaleItem);
//		return R.data(pages);
//	}
//
//	/**
//	 * 采购详情关联订单详情表 新增
//	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 4)
//	@Operation(summary = "新增", description  = "传入purchaseOrderSaleItem")
//	public R save(@Valid @RequestBody PurchaseOrderSaleItemEntity purchaseOrderSaleItem) {
//		return R.status(purchaseOrderSaleItemService.save(purchaseOrderSaleItem));
//	}
//
//	/**
//	 * 采购详情关联订单详情表 修改
//	 */
//	@PostMapping("/update")
//	@ApiOperationSupport(order = 5)
//	@Operation(summary = "修改", description  = "传入purchaseOrderSaleItem")
//	public R update(@Valid @RequestBody PurchaseOrderSaleItemEntity purchaseOrderSaleItem) {
//		return R.status(purchaseOrderSaleItemService.updateById(purchaseOrderSaleItem));
//	}
//
//	/**
//	 * 采购详情关联订单详情表 新增或修改
//	 */
//	@PostMapping("/submit")
//	@ApiOperationSupport(order = 6)
//	@Operation(summary = "新增或修改", description  = "传入purchaseOrderSaleItem")
//	public R submit(@Valid @RequestBody PurchaseOrderSaleItemEntity purchaseOrderSaleItem) {
//		return R.status(purchaseOrderSaleItemService.saveOrUpdate(purchaseOrderSaleItem));
//	}
//
//	/**
//	 * 采购详情关联订单详情表 删除
//	 */
//	@PostMapping("/remove")
//	@ApiOperationSupport(order = 7)
//	@Operation(summary = "逻辑删除", description  = "传入ids")
//	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
//		return R.status(purchaseOrderSaleItemService.deleteLogic(Func.toLongList(ids)));
//	}
//
//	/**
//	 * 导出数据
//	 */
//	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
//	@GetMapping("/export-purchaseOrderSaleItem")
//	@ApiOperationSupport(order = 8)
//	@Operation(summary = "导出数据", description  = "传入purchaseOrderSaleItem")
//	public void exportPurchaseOrderSaleItem(@Parameter(hidden = true) @RequestParam Map<String, Object> purchaseOrderSaleItem, BladeUser bladeUser, HttpServletResponse response) {
//		QueryWrapper<PurchaseOrderSaleItemEntity> queryWrapper = Condition.getQueryWrapper(purchaseOrderSaleItem, PurchaseOrderSaleItemEntity.class);
//		//if (!AuthUtil.isAdministrator()) {
//		//	queryWrapper.lambda().eq(PurchaseOrderSaleItemEntity::getTenantId, bladeUser.getTenantId());
//		//}
//		//queryWrapper.lambda().eq(PurchaseOrderSaleItemEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
//		List<PurchaseOrderSaleItemExcel> list = purchaseOrderSaleItemService.exportPurchaseOrderSaleItem(queryWrapper);
//		ExcelUtil.export(response, "采购详情关联订单详情表数据" + DateUtil.time(), "采购详情关联订单详情表数据表", list, PurchaseOrderSaleItemExcel.class);
//	}

}
