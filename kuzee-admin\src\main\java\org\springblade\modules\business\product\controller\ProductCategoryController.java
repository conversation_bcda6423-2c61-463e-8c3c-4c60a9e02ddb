/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.business.product.controller;

import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.business.product.pojo.dto.SaveProductCategoryDTO;
import org.springblade.modules.business.product.pojo.entity.ProductCategoryEntity;
import org.springblade.modules.business.product.pojo.entity.ProductEntity;
import org.springblade.modules.business.product.pojo.vo.OneListVO;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryTreeVO;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryVO;
import org.springblade.modules.business.product.pojo.vo.ProductCategoryWithChildrenItem;
import org.springblade.modules.business.product.service.IProductCategoryService;
import org.springblade.modules.business.product.service.IProductService;
import org.springblade.modules.business.product.wrapper.ProductCategoryWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商品分类表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/productCategory")
@Tag(name = "商品分类表", description = "商品分类表接口")
public class ProductCategoryController extends BladeController {

	private final IProductCategoryService productCategoryService;

	private final IProductService productService;

	/**
	 * 查询所有分类
	 */
	@GetMapping("/all")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "查询所有分类", description  = "查询所有分类，无需入参")
	public R<List<ProductCategoryEntity>> all() {
		List<ProductCategoryEntity> all = productCategoryService.getAll();
		return R.data(all);
	}

	/**
	 * 根据id获取商品分类详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "根据id获取商品分类详情", description  = "传入productCategory")
	public R<ProductCategoryVO> detail(ProductCategoryEntity productCategory) {
		ProductCategoryEntity detail = productCategoryService.getOne(Condition.getQueryWrapper(productCategory));
		return R.data(ProductCategoryWrapper.build().entityVO(detail));
	}

	/**
	 * 查询分类及子分类
	 */
	@GetMapping("/withChildren")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "查询分类及子分类", description  = "根据分类名称查询所有一级分类及子分类")
	public R<List<ProductCategoryWithChildrenItem>> listWithChildren(@RequestParam(required = false) String name) {
		List<ProductCategoryWithChildrenItem> list = productCategoryService.listWithChildren(name);
		return R.data(list);
	}

	/**
	 * 商品分类表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "分页", description  = "传入productCategory")
	public R<IPage<ProductCategoryVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> productCategory, Query query) {
		IPage<ProductCategoryEntity> pages = productCategoryService.page(Condition.getPage(query), Condition.getQueryWrapper(productCategory, ProductCategoryEntity.class));
		return R.data(ProductCategoryWrapper.build().pageVO(pages));
	}


	/**
	 * 商品分类表 自定义分页
	 *//*
	@GetMapping("/page")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "分页", description  = "传入productCategory")
	public R<IPage<ProductCategoryVO>> page(ProductCategoryVO productCategory, Query query) {
		IPage<ProductCategoryVO> pages = productCategoryService.selectProductCategoryPage(Condition.getPage(query), productCategory);
		return R.data(pages);
	}*/

	/**
	 * 添加商品分类
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "添加商品分类", description  = "传入productCategory")
	public R save(@Valid @RequestBody SaveProductCategoryDTO saveProductCategoryDTO) {
		return R.status(productCategoryService.saveProductCategory(saveProductCategoryDTO));
	}

	/**
	 * 商品分类表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "修改", description  = "传入productCategory")
	public R update(@Valid @RequestBody ProductCategoryEntity productCategory) {
		return R.status(productCategoryService.updateById(productCategory));
	}

	/**
	 * 商品分类表 新增或修改
	 *//*
	@PostMapping("/submit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "新增或修改", description  = "传入productCategory")
	public R submit(@Valid @RequestBody ProductCategoryEntity productCategory) {
		return R.status(productCategoryService.saveOrUpdate(productCategory));
	}*/

	/**
	 * 删除商品分类
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "删除商品分类", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		List<Long> originalIds = Func.toLongList(ids);
		// 校验其及其所有父类是否已被商品关联
		List<Long> allIdsToCheck = new ArrayList<>(originalIds);

		List<Long> idsToProcess = new ArrayList<>(originalIds);

		while (!idsToProcess.isEmpty()) {
			List<Long> parentIds = new ArrayList<>();
			for (Long id : idsToProcess) {
				ProductCategoryEntity productCategory = productCategoryService.getById(id);
				if (productCategory != null && productCategory.getParentId() != null) {
					// 使用 Set 来确保唯一性并提高查找效率
					if (!allIdsToCheck.contains(productCategory.getParentId())) {
						parentIds.add(productCategory.getParentId());
						allIdsToCheck.add(productCategory.getParentId());
					}
				}
			}
			idsToProcess = parentIds;
		}

		List<ProductEntity> checkEntityList = productService.list(new QueryWrapper<ProductEntity>().lambda()
			.in(ProductEntity::getProductCategoryId, allIdsToCheck)
			.eq(ProductEntity::getIsDeleted, 0)
		);
		if (!checkEntityList.isEmpty()) {
			return R.fail("该分类或者其父类已被商品关联，无法删除");
		}
		return R.status(productCategoryService.deleteLogic(originalIds));
	}

	/**
	 * 导出数据
	 *//*
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-productCategory")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "导出数据", description  = "传入productCategory")
	public void exportProductCategory(@Parameter(hidden = true) @RequestParam Map<String, Object> productCategory, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ProductCategoryEntity> queryWrapper = Condition.getQueryWrapper(productCategory, ProductCategoryEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ProductCategoryEntity::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(ProductCategoryEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ProductCategoryExcel> list = productCategoryService.exportProductCategory(queryWrapper);
		ExcelUtil.export(response, "商品分类表数据" + DateUtil.time(), "商品分类表数据表", list, ProductCategoryExcel.class);
	}*/
	/**
	 * 商品分类表 分页
	 */
	@GetMapping("/listTree")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "分页", description  = "传入productCategory")
	public R<List<ProductCategoryTreeVO>> listTree(@Parameter(hidden = true) @RequestParam Map<String, Object> productCategory) {
		List<ProductCategoryTreeVO> list = productCategoryService.listTree(Condition.getQueryWrapper(productCategory, ProductCategoryEntity.class));
		return R.data(list);
	}


	/**
	 * 商品第一大分类
	 */
	@GetMapping("/getOneList")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "商品第一大分类", description  = "商品第一大分类")
	public R<List<OneListVO>> getOneList() {
		List<OneListVO> list = productCategoryService.getOneList();
		return R.data(list);
	}
}
