/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.analysis.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 金额汇总数据视图对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "金额汇总数据视图对象")
public class AmountsDataVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 实际销售金额(含框)
	 * 计算逻辑: chy_sale_order表中, is_cut_order=1(已截单)的订单的total_amount(订单总金额)之和
	 */
	@Schema(description = "实际销售金额(含框)")
	private BigDecimal actualSaleAmountWithTax;

	/**
	 * 实际销售金额(含框)环比
	 * 计算逻辑: (当期实际销售金额(含框) - 上期实际销售金额(含框)) / 上期实际销售金额(含框)
	 */
	@Schema(description = "实际销售金额(含框)环比")
	private BigDecimal actualSaleAmountWithTaxRatio;

	/**
	 * 上期实际销售金额(含框)
	 */
	@Schema(description = "上期实际销售金额(含框)")
	private BigDecimal previousActualSaleAmountWithTax;

	/**
	 * 实际销售金额(含框)差值
	 * 计算逻辑: 当期实际销售金额(含框) - 上期实际销售金额(含框)
	 */
	@Schema(description = "实际销售金额(含框)差值")
	private BigDecimal actualSaleAmountWithTaxDifference;

	/**
	 * 实际销售金额(不含框)
	 * 计算逻辑: chy_sale_order表中, is_cut_order=1(已截单)的订单的(total_amount - support_trans_total)之和
	 */
	@Schema(description = "实际销售金额(不含框)")
	private BigDecimal actualSaleAmountWithoutTax;

	/**
	 * 实际销售金额(不含框)环比
	 * 计算逻辑: (当期实际销售金额(不含框) - 上期实际销售金额(不含框)) / 上期实际销售金额(不含框)
	 */
	@Schema(description = "实际销售金额(不含框)环比")
	private BigDecimal actualSaleAmountWithoutTaxRatio;

	/**
	 * 上期实际销售金额(不含框)
	 */
	@Schema(description = "上期实际销售金额(不含框)")
	private BigDecimal previousActualSaleAmountWithoutTax;

	/**
	 * 实际销售金额(不含框)差值
	 * 计算逻辑: 当期实际销售金额(不含框) - 上期实际销售金额(不含框)
	 */
	@Schema(description = "实际销售金额(不含框)差值")
	private BigDecimal actualSaleAmountWithoutTaxDifference;

	/**
	 * 基础产品金额(含框)
	 * 计算逻辑: chy_sale_order表中, is_cut_order=1(已截单)的订单的(total_amount - service_fee)之和
	 */
	@Schema(description = "基础产品金额(含框)")
	private BigDecimal baseProductAmountWithTax;

	/**
	 * 基础产品金额(含框)环比
	 * 计算逻辑: (当期基础产品金额(含框) - 上期基础产品金额(含框)) / 上期基础产品金额(含框)
	 */
	@Schema(description = "基础产品金额(含框)环比")
	private BigDecimal baseProductAmountWithTaxRatio;

	/**
	 * 上期基础产品金额(含框)
	 */
	@Schema(description = "上期基础产品金额(含框)")
	private BigDecimal previousBaseProductAmountWithTax;

	/**
	 * 基础产品金额(含框)差值
	 * 计算逻辑: 当期基础产品金额(含框) - 上期基础产品金额(含框)
	 */
	@Schema(description = "基础产品金额(含框)差值")
	private BigDecimal baseProductAmountWithTaxDifference;

	/**
	 * 基础产品金额(不含框)
	 * 计算逻辑: chy_sale_order表中, is_cut_order=1(已截单)的订单的(total_amount - service_fee - support_trans_total)之和
	 */
	@Schema(description = "基础产品金额(不含框)")
	private BigDecimal baseProductAmountWithoutTax;

	/**
	 * 基础产品金额(不含框)环比
	 * 计算逻辑: (当期基础产品金额(不含框) - 上期基础产品金额(不含框)) / 上期基础产品金额(不含框)
	 */
	@Schema(description = "基础产品金额(不含框)环比")
	private BigDecimal baseProductAmountWithoutTaxRatio;

	/**
	 * 上期基础产品金额(不含框)
	 */
	@Schema(description = "上期基础产品金额(不含框)")
	private BigDecimal previousBaseProductAmountWithoutTax;

	/**
	 * 基础产品金额(不含框)差值
	 * 计算逻辑: 当期基础产品金额(不含框) - 上期基础产品金额(不含框)
	 */
	@Schema(description = "基础产品金额(不含框)差值")
	private BigDecimal baseProductAmountWithoutTaxDifference;

	/**
	 * 采购金额
	 * 计算逻辑: chy_replenishment_order_item(备货详情表)中, 所有商品按重量或数量计算的采购价总和, 加上其配套运输品的采购价总和
	 */
	@Schema(description = "采购金额")
	private BigDecimal purchaseAmount;

	/**
	 * 采购金额环比
	 * 计算逻辑: (当期采购金额 - 上期采购金额) / 上期采购金额
	 */
	@Schema(description = "采购金额环比")
	private BigDecimal purchaseAmountRatio;

	/**
	 * 上期采购金额
	 */
	@Schema(description = "上期采购金额")
	private BigDecimal previousPurchaseAmount;

	/**
	 * 采购金额差值
	 * 计算逻辑: 当期采购金额 - 上期采购金额
	 */
	@Schema(description = "采购金额差值")
	private BigDecimal purchaseAmountDifference;

	/**
	 * 配送成本
	 * 计算逻辑: chy_warehouse_outbound(出库表)中, delivery_cost(配套成本)字段之和
	 */
	@Schema(description = "配送成本")
	private BigDecimal deliveryCost;

	/**
	 * 配送成本环比
	 * 计算逻辑: (当期配送成本 - 上期配送成本) / 上期配送成本
	 */
	@Schema(description = "配送成本环比")
	private BigDecimal deliveryCostRatio;

	/**
	 * 上期配送成本
	 */
	@Schema(description = "上期配送成本")
	private BigDecimal previousDeliveryCost;

	/**
	 * 配送成本差值
	 * 计算逻辑: 当期配送成本 - 上期配送成本
	 */
	@Schema(description = "配送成本差值")
	private BigDecimal deliveryCostDifference;

	/**
	 * 转运费
	 * 计算逻辑: chy_transport_fee(转运费表)中, amount(转运费)字段之和
	 */
	@Schema(description = "转运费")
	private BigDecimal transferFee;

	/**
	 * 转运费环比
	 * 计算逻辑: (当期转运费 - 上期转运费) / 上期转运费
	 */
	@Schema(description = "转运费环比")
	private BigDecimal transferFeeRatio;

	/**
	 * 上期转运费
	 */
	@Schema(description = "上期转运费")
	private BigDecimal previousTransferFee;

	/**
	 * 转运费差值
	 * 计算逻辑: 当期转运费 - 上期转运费
	 */
	@Schema(description = "转运费差值")
	private BigDecimal transferFeeDifference;

	/**
	 * 售后金额
	 * 计算逻辑: chy_order_after_sales_service(售后服务表)中, finance_amount(财务审核退款总额)字段之和
	 */
	@Schema(description = "售后金额")
	private BigDecimal afterSaleAmount;

	/**
	 * 售后金额环比
	 * 计算逻辑: (当期售后金额 - 上期售后金额) / 上期售后金额
	 */
	@Schema(description = "售后金额环比")
	private BigDecimal afterSaleAmountRatio;

	/**
	 * 上期售后金额
	 */
	@Schema(description = "上期售后金额")
	private BigDecimal previousAfterSaleAmount;

	/**
	 * 售后金额差值
	 * 计算逻辑: 当期售后金额 - 上期售后金额
	 */
	@Schema(description = "售后金额差值")
	private BigDecimal afterSaleAmountDifference;

	/**
	 * 供应商罚款金额
	 * 计算逻辑: chy_order_after_sales_service_item_money(售后商品罚款表)中, finance_amount(财务审核金额)字段之和
	 */
	@Schema(description = "供应商罚款金额")
	private BigDecimal supplierFineAmount;

	/**
	 * 供应商罚款金额环比
	 * 计算逻辑: (当期供应商罚款金额 - 上期供应商罚款金额) / 上期供应商罚款金额
	 */
	@Schema(description = "供应商罚款金额环比")
	private BigDecimal supplierFineAmountRatio;

	/**
	 * 上期供应商罚款金额
	 */
	@Schema(description = "上期供应商罚款金额")
	private BigDecimal previousSupplierFineAmount;

	/**
	 * 供应商罚款金额差值
	 * 计算逻辑: 当期供应商罚款金额 - 上期供应商罚款金额
	 */
	@Schema(description = "供应商罚款金额差值")
	private BigDecimal supplierFineAmountDifference;

	/**
	 * 供应商退货金额
	 * 计算逻辑: chy_order_after_sales_service_item_supplier(售后商品供应商退货表)中, finance_amount(财务审核金额)字段之和
	 */
	@Schema(description = "供应商退货金额")
	private BigDecimal supplierReturnAmount;

	/**
	 * 供应商退货金额环比
	 * 计算逻辑: (当期供应商退货金额 - 上期供应商退货金额) / 上期供应商退货金额
	 */
	@Schema(description = "供应商退货金额环比")
	private BigDecimal supplierReturnAmountRatio;

	/**
	 * 上期供应商退货金额
	 */
	@Schema(description = "上期供应商退货金额")
	private BigDecimal previousSupplierReturnAmount;

	/**
	 * 供应商退货金额差值
	 * 计算逻辑: 当期供应商退货金额 - 上期供应商退货金额
	 */
	@Schema(description = "供应商退货金额差值")
	private BigDecimal supplierReturnAmountDifference;

	/**
	 * 卖框金额
	 * 计算逻辑: chy_sale_order(订单表)中, is_cut_order=1(已截单)的订单的support_trans_total(配套运输品总额)字段之和
	 */
	@Schema(description = "卖框金额")
	private BigDecimal sellingBoxAmount;

	/**
	 * 卖框金额环比
	 * 计算逻辑: (当期卖框金额 - 上期卖框金额) / 上期卖框金额
	 */
	@Schema(description = "卖框金额环比")
	private BigDecimal sellingBoxAmountRatio;

	/**
	 * 上期卖框金额
	 */
	@Schema(description = "上期卖框金额")
	private BigDecimal previousSellingBoxAmount;

	/**
	 * 卖框金额差值
	 * 计算逻辑: 当期卖框金额 - 上期卖框金额
	 */
	@Schema(description = "卖框金额差值")
	private BigDecimal sellingBoxAmountDifference;

	/**
	 * 毛利
	 * 计算逻辑: 实际销售金额(含框) - 成本金额 - 售后金额 + (供应商罚款金额 + 供应商退货金额) + 卖框金额
	 */
	@Schema(description = "毛利")
	private BigDecimal profit;

	/**
	 * 毛利环比
	 * 计算逻辑: (当期毛利 - 上期毛利) / 上期毛利
	 */
	@Schema(description = "毛利环比")
	private BigDecimal profitRatio;

	/**
	 * 上期毛利
	 */
	@Schema(description = "上期毛利")
	private BigDecimal previousProfit;

	/**
	 * 毛利差值
	 * 计算逻辑: 当期毛利 - 上期毛利
	 */
	@Schema(description = "毛利差值")
	private BigDecimal profitDifference;

	/**
	 * 成本金额
	 * 计算逻辑: 采购金额 + 配送成本
	 */
	@Schema(description = "成本金额")
	private BigDecimal costAmount;

	/**
	 * 成本金额环比
	 * 计算逻辑: (当期成本金额 - 上期成本金额) / 上期成本金额
	 */
	@Schema(description = "成本金额环比")
	private BigDecimal costAmountRatio;

	/**
	 * 上期成本金额
	 */
	@Schema(description = "上期成本金额")
	private BigDecimal previousCostAmount;

	/**
	 * 成本金额差值
	 * 计算逻辑: 当期成本金额 - 上期成本金额
	 */
	@Schema(description = "成本金额差值")
	private BigDecimal costAmountDifference;

}
