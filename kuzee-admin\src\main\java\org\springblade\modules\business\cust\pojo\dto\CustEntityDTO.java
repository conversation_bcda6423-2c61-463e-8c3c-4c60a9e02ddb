package org.springblade.modules.business.cust.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.modules.resource.pojo.entity.Attach;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CustEntityDTO {
	@Serial
	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "id")
	private Long id;
	/**
	 * 系统用户id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "系统用户id")
	private Long bladeUserId;
	/**
	 * 父级客户id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "父级客户id")
	private Long parentId;
	/**
	 * 客户名称
	 */
	@Schema(description = "客户名称")
	private String custName;
	/**
	 * 所属分区
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "所属分区")
	private Long warehouseId;
	/**
	 * 授信额度
	 */
	@Schema(description = "授信额度")
	private BigDecimal creditLimit;
	/**
	 * 授信额度还款周期
	 */
	@Schema(description = "授信额度还款周期")
	private Integer creditCycle;
	/**
	 * 授信截止时间
	 */
	@Schema(description = "授信截止时间")
	@JsonFormat(pattern = DateUtil.PATTERN_DATETIME)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	private LocalDateTime creditEndTime;
	/**
	 * 手机号
	 */
	@Schema(description = "手机号")
	private String phone;
	/**
	 * 密码
	 */
	@Schema(description = "密码")
	private String password;
	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String nickname;
	/**
	 * 头像
	 */
	@Schema(description = "头像")
	private String icon;
	/**
	 * 性别
	 */
	@Schema(description = "性别")
	private Integer gender;
	/**
	 * 客户来源
	 */
	@Schema(description = "客户来源")
	private Integer sourceType;
	/**
	 * 客户角色id
	 */
	@Schema(description = "客户角色id")
	private Long custRoleId;
	/**
	 * 微信unionid
	 */
	@Schema(description = "微信unionid")
	private String unionid;

	/**
	 * 业务员id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "业务员id")
	private Long userId;

	/**
	 * 编号
	 */
	@Schema(description = "编号")
	private String code;

	/**
	 * 已使用授信额度
	 */
	@Schema(description = "已使用授信额度")
	private BigDecimal useCredit;

	/**
	 *省份/直辖市
	 */
	@Schema(description = "省份/直辖市")
	private String province;
	/**
	 *城市
	 */
	@Schema(description = "城市")
	private String city;
	/**
	 *区
	 */
	@Schema(description = "区")
	private String region;

	/**
	 *区域名称
	 */
	@Schema(description = "区域名称")
	private String regionName;


	/**
	 *联系人
	 */
	@Schema(description = "联系人")
	private String contacts;

	/**
	 *详细地址
	 */
	@Schema(description = "详细地址")
	private String address;

	/**
	 *统一社会信用代码
	 */
	@Schema(description = "统一社会信用代码")
	private String socialCreditCode;

	/**
	 *营业执照
	 */
	@Schema(description = "营业执照")
	private String license;
	/**
	 *备注
	 */
	@Schema(description = "备注")
	private String note;
}
