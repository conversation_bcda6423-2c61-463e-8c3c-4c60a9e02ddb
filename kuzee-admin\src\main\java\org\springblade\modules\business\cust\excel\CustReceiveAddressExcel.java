/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.cust.excel;


import lombok.Data;

import java.io.Serializable;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serial;


/**
 * 客户收货地址表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustReceiveAddressExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 客户名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户名称")
	private Long custId;
	/**
	 * 收货人名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货人名称")
	private String name;
	/**
	 * 收货人手机号
	 */
	@ColumnWidth(20)
	@ExcelProperty("收货人手机号")
	private String phone;
	/**
	 * 是否默认地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否默认地址")
	private Byte defaultStatus;
	/**
	 * 邮政编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("邮政编码")
	private String postCode;
	/**
	 * 省份/直辖市
	 */
	@ColumnWidth(20)
	@ExcelProperty("省份/直辖市")
	private String province;
	/**
	 * 城市
	 */
	@ColumnWidth(20)
	@ExcelProperty("城市")
	private String city;
	/**
	 * 区
	 */
	@ColumnWidth(20)
	@ExcelProperty("区")
	private String region;
	/**
	 * 详细地址(街道)
	 */
	@ColumnWidth(20)
	@ExcelProperty("详细地址(街道)")
	private String detailAddress;

}
