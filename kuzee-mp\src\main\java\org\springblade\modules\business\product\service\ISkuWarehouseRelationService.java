/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.business.product.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.business.product.pojo.dto.SaveSkuWarehouseDTO;
import org.springblade.modules.business.product.pojo.entity.SkuStockEntity;
import org.springblade.modules.business.product.pojo.entity.SkuWarehouseRelationEntity;
import org.springblade.modules.business.product.pojo.vo.PageAuditSkuVO;
import org.springblade.modules.business.product.pojo.vo.SkuWarehouseRelationVO;

import java.util.List;

/**
 * 商品sku与仓库关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
public interface ISkuWarehouseRelationService extends BaseService<SkuWarehouseRelationEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param skuWarehouseRelation 查询参数
	 * @return IPage<SkuWarehouseRelationVO>
	 */
	IPage<SkuWarehouseRelationVO> selectSkuWarehouseRelationPage(IPage<SkuWarehouseRelationVO> page, SkuWarehouseRelationVO skuWarehouseRelation);

//
//    void saveSkuWarehouseAll(SaveSkuWarehouseDTO dto);
//
//
//	IPage<PageAuditSkuVO> pageAuditSku(IPage<PageAuditSkuVO> page, Long warehouseId, Integer selectType, String selectCode);
//
//
//	void removeByShow(List<Long> idList,Long warehouseId);
//
//    List<SkuWarehouseRelationEntity> getByWarehouseId(Long parentId, List<Long> skuIdList);
}
