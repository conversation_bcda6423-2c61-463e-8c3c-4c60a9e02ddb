<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.business.cust.mapper.CustCreditUsageRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="custCreditUsageRecordResultMap" type="org.springblade.modules.business.cust.pojo.entity.CustCreditUsageRecordEntity">
        <result column="cust_id" property="custId"/>
        <result column="biz_type" property="bizType"/>
        <result column="used_amount" property="usedAmount"/>
        <result column="balance" property="balance"/>
        <result column="biz_no" property="bizNo"/>
        <result column="usage_time" property="usageTime"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="selectCustCreditUsageRecordPage" resultMap="custCreditUsageRecordResultMap">
        select * from chy_cust_credit_usage_record where is_deleted = 0
    </select>

    <select id="exportCustCreditUsageRecord" resultType="org.springblade.modules.business.cust.excel.CustCreditUsageRecordExcel">
        SELECT * FROM chy_cust_credit_usage_record ${ew.customSqlSegment}
    </select>

</mapper>
