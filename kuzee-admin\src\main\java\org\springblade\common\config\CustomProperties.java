package org.springblade.common.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "custom")
public class CustomProperties {

	@Schema(description = "商户名称")
	private String merchantName;

	@Schema(description = "商户小程序appid")
	private String custAppid;

	@Schema(description = "商户小程序secret")
	private String custSecret;
}
